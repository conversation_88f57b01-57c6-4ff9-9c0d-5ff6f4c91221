#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试设备工站解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.content_parser import content_parser

def test_equipment_markdown_parsing():
    """测试Markdown格式的设备工站解析"""
    
    # 模拟智能匹配生成的设备部分内容
    equipment_content = """
### 二、设备部分

#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯
2. ST10工站配置支架压装、插线束高度检测以及发生器预装3小分站，可以独立进行作业
3. 设备左右和前方需要3组安全光栅，硬件要接入安全回路进行控制

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足
2. 3个小分站的气动需要分开控制，气压表需要设置在正对人侧方便点检使用

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

二、预装夹具-Fixture 
（一）机械要求:
1. 发生器检测到位选用大款传感器，检测整个端面
2. 治具夹紧发生器后不晃动、松动

（二）电气要求:
无

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

#### ST20 SAB-B-Folding
一、折叠设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯
2. 设备前方需要2组安全光栅，硬件要接入安全回路进行控制

（二）电气要求:
1. 电控系统的电源供给为单一电源
2. 气动控制系统需要独立控制

（三）防错及点检要求:
要求1：折叠动作要同步，折叠尺尽量不晃动
方案1：使用传感器检测折叠位置
"""
    
    print("=" * 80)
    print("测试设备工站Markdown格式解析")
    print("=" * 80)
    
    # 解析设备工站信息
    equipment_stations = content_parser.extract_equipment_station_info(equipment_content)
    
    print(f"解析到 {len(equipment_stations)} 个设备工站")
    
    for station in equipment_stations:
        print(f"\n设备工站: ST{station['station_number']} - {station.get('station_name', '未命名')}")
        details = station.get('equipment_details', {})
        
        print(f"  设备类型: {details.get('equipment_type', '未识别')}")
        print(f"  机械要求: {details.get('mechanical_requirements', '未识别')[:100]}...")
        print(f"  电气要求: {details.get('electrical_requirements', '未识别')[:100]}...")
        print(f"  防错要求: {details.get('error_prevention_requirements', '未识别')[:100]}...")
        
        # 检查向后兼容性
        print(f"  技术要求(兼容): {details.get('technical_requirements', '未识别')[:50]}...")
        print(f"  设备参数(兼容): {details.get('equipment_parameters', '未识别')[:50]}...")
        print(f"  安全要求(兼容): {details.get('safety_requirements', '未识别')[:50]}...")

def test_traditional_equipment_parsing():
    """测试传统格式的设备工站解析"""
    
    print("\n" + "=" * 80)
    print("测试传统格式设备工站解析")
    print("=" * 80)
    
    # 传统格式的设备内容
    traditional_content = """
【设备部分】
ST10设备信息
设备类型: 手动装配工位
技术要求: 提供导流片定位夹具
设备参数: 工作台高度800mm
安全要求: 防静电措施

ST15设备信息
设备类型: 半自动装配设备
技术要求: 充气器安装压力控制
设备参数: 压力范围50-100N
安全要求: 双手启动按钮
【设备部分结束】
"""
    
    equipment_stations = content_parser.extract_equipment_station_info(traditional_content)
    
    print(f"解析到 {len(equipment_stations)} 个设备工站")
    
    for station in equipment_stations:
        print(f"\n设备工站: ST{station['station_number']}")
        details = station.get('equipment_details', {})
        
        print(f"  设备类型: {details.get('equipment_type', '未识别')}")
        print(f"  技术要求: {details.get('technical_requirements', '未识别')}")
        print(f"  设备参数: {details.get('equipment_parameters', '未识别')}")
        print(f"  安全要求: {details.get('safety_requirements', '未识别')}")

def test_full_content_parsing():
    """测试完整内容解析（工艺+设备）"""
    
    print("\n" + "=" * 80)
    print("测试完整内容解析")
    print("=" * 80)
    
    # 完整的Markdown格式内容
    full_content = """
### 一、工艺部分

#### ST10 SAB-B-Pre-assembly
（一）
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: 导流片无错装、漏装、安装方向正确
   - 过程防错要求: 通过MSA

### 二、设备部分

#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
2. ST10工站配置支架压装、插线束高度检测

（二）电气要求:
1. 电控系统的电源供给为单一电源
2. 3个小分站的气动需要分开控制

（三）防错及点检要求:
要求1：导流片安装方向正确
方案1：导流片通过上方IV拍照识别
"""
    
    # 1. 解析内容分割
    parsed_content = content_parser.parse_content(full_content)
    
    print("1. 内容分割结果:")
    print(f"   工艺部分长度: {len(parsed_content.get('process_content', ''))}")
    print(f"   设备部分长度: {len(parsed_content.get('equipment_content', ''))}")
    
    # 2. 解析工艺工站
    if parsed_content.get('process_content'):
        process_stations = content_parser.extract_station_info(parsed_content['process_content'])
        print(f"\n2. 工艺工站: {len(process_stations)} 个")
        for station in process_stations:
            print(f"   ST{station['station_number']} - {station['station_name']}")
    
    # 3. 解析设备工站
    if parsed_content.get('equipment_content'):
        equipment_stations = content_parser.extract_equipment_station_info(parsed_content['equipment_content'])
        print(f"\n3. 设备工站: {len(equipment_stations)} 个")
        for station in equipment_stations:
            print(f"   ST{station['station_number']} - {station.get('station_name', '设备信息')}")
            details = station.get('equipment_details', {})
            if details.get('mechanical_requirements'):
                print(f"      机械要求: {details['mechanical_requirements'][:50]}...")
            if details.get('electrical_requirements'):
                print(f"      电气要求: {details['electrical_requirements'][:50]}...")

def test_mixed_format():
    """测试混合格式内容"""
    
    print("\n" + "=" * 80)
    print("测试混合格式内容")
    print("=" * 80)
    
    # 混合新旧格式的内容
    mixed_content = """
### 一、工艺部分
#### ST10 SAB-B-Pre-assembly
（一）
1. 工艺过程描述: 拿取导流片
   - 人or设备: 人

【设备部分】
ST10设备信息
设备类型: 手动装配工位
技术要求: 提供导流片定位夹具

#### ST15 SAB-B-Assembly
一、装配设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
【设备部分结束】
"""
    
    parsed_content = content_parser.parse_content(mixed_content)
    
    print("内容分割结果:")
    print(f"工艺部分长度: {len(parsed_content.get('process_content', ''))}")
    print(f"设备部分长度: {len(parsed_content.get('equipment_content', ''))}")
    
    if parsed_content.get('equipment_content'):
        equipment_stations = content_parser.extract_equipment_station_info(parsed_content['equipment_content'])
        print(f"\n设备工站数量: {len(equipment_stations)}")
        
        for station in equipment_stations:
            print(f"工站: ST{station['station_number']}")
            details = station.get('equipment_details', {})
            print(f"  设备类型: {details.get('equipment_type', '未识别')}")

if __name__ == "__main__":
    test_equipment_markdown_parsing()
    test_traditional_equipment_parsing()
    test_full_content_parsing()
    test_mixed_format()
    
    print("\n" + "=" * 80)
    print("设备工站解析测试完成")
    print("=" * 80)
