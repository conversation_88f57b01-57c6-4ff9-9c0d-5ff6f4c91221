"""
Linespec分析器 - 用于处理和分析技术规范数据
"""
import pandas as pd
import json
import os
from typing import Dict, List, Optional, Tuple


class LinespecAnalyzer:
    """技术规范分析器"""
    
    def __init__(self, excel_file_path: str = None):
        """
        初始化分析器
        
        Args:
            excel_file_path: Excel数据文件路径
        """
        self.excel_file_path = excel_file_path or "Linespec-data-SAB-V0.xlsx"
        self.process_types_data = {}
        self.station_details_data = {}
        self.load_data()
    
    def load_data(self):
        """加载Excel数据"""
        try:
            if not os.path.exists(self.excel_file_path):
                print(f"警告：Excel文件不存在: {self.excel_file_path}")
                return
            
            # 读取所有工作表
            excel_data = pd.read_excel(self.excel_file_path, sheet_name=None)
            
            # 处理工艺类型与零件对应关系 (list1)
            if 'list1' in excel_data:
                self._process_list1_data(excel_data['list1'])
            
            # 处理工艺流程信息 (list2)
            if 'list2' in excel_data:
                self._process_list2_data(excel_data['list2'])
            
            # 处理详细工艺规范 (SAB-A到SAB-F)
            for sheet_name in excel_data.keys():
                if sheet_name.startswith('SAB-'):
                    self._process_station_details(sheet_name, excel_data[sheet_name])
                    
            print("数据加载完成")
            
        except Exception as e:
            print(f"加载数据时出错: {e}")
    
    def _process_list1_data(self, df: pd.DataFrame):
        """处理工艺类型与零件对应关系数据"""
        for _, row in df.iterrows():
            if pd.notna(row.get('Process type')):
                process_type = f"SAB-{row['Process type']}"  # 转换为SAB-A格式
                components = []

                # 提取零件信息
                for col in df.columns:
                    if col not in ['Product family', 'Process type', 'CT'] and pd.notna(row.get(col)):
                        # 如果列值等于列名，说明该零件存在
                        if str(row[col]).strip() == col:
                            components.append(col)

                self.process_types_data[process_type] = {
                    'components': components,
                    'ct': row.get('CT', 0)
                }
    
    def _process_list2_data(self, df: pd.DataFrame):
        """处理工艺流程信息数据"""
        # 按工艺类型分组处理
        process_groups = {}

        for _, row in df.iterrows():
            if pd.notna(row.get('Process type')):
                process_type = row['Process type']
                if process_type not in process_groups:
                    process_groups[process_type] = {
                        'stations': [],
                        'processes': []
                    }

                # 添加工站和工艺名称
                if pd.notna(row.get('station')):
                    process_groups[process_type]['stations'].append(row['station'])
                if pd.notna(row.get('Process name')):
                    process_groups[process_type]['processes'].append(row['Process name'])

        # 更新到主数据结构
        for process_type, data in process_groups.items():
            if process_type in self.process_types_data:
                self.process_types_data[process_type].update(data)
            else:
                self.process_types_data[process_type] = data
    
    def _process_station_details(self, sheet_name: str, df: pd.DataFrame):
        """处理工站详细信息"""
        process_type = sheet_name  # SAB-A, SAB-B等

        if process_type not in self.station_details_data:
            self.station_details_data[process_type] = {}

        current_station = None

        for _, row in df.iterrows():
            # 检查是否是新的工站
            if pd.notna(row.get('station')):
                current_station = row['station']
                self.station_details_data[process_type][current_station] = {
                    'processName': row.get('Process type', ''),
                    'steps': [],
                    'equipment': {},
                    'fixtures': []
                }

            # 添加工艺步骤
            if current_station and pd.notna(row.get('process description')):
                step_info = {
                    'stepNumber': len(self.station_details_data[process_type][current_station]['steps']) + 1,
                    'description': str(row.get('process description', '')),
                    'operator': str(row.get('man/machine', '')),
                    'qualityRequirements': str(row.get('产品特性要求', '')) if pd.notna(row.get('产品特性要求')) else '',
                    'errorProofing': str(row.get('防错要求', '')) if pd.notna(row.get('防错要求')) else ''
                }
                self.station_details_data[process_type][current_station]['steps'].append(step_info)

            # 添加设备信息
            if current_station and pd.notna(row.get('设备名')):
                self.station_details_data[process_type][current_station]['equipment'] = {
                    'name': str(row.get('设备名', '')),
                    'mechanicalRequirements': str(row.get('设备机械要求', '')) if pd.notna(row.get('设备机械要求')) else '',
                    'electricalRequirements': str(row.get('设备电气要求', '')) if pd.notna(row.get('设备电气要求')) else '',
                    'errorProofingRequirements': str(row.get('设备防错&点检要求', '')) if pd.notna(row.get('设备防错&点检要求')) else ''
                }

            # 添加夹具信息
            if current_station and pd.notna(row.get('夹具名')):
                fixture_info = {
                    'name': str(row.get('夹具名', '')),
                    'mechanicalRequirements': str(row.get('夹具机械要求', '')) if pd.notna(row.get('夹具机械要求')) else '',
                    'electricalRequirements': str(row.get('夹具电气要求', '')) if pd.notna(row.get('夹具电气要求')) else '',
                    'errorProofingRequirements': str(row.get('夹具防错&点检要求', '')) if pd.notna(row.get('夹具防错&点检要求')) else ''
                }
                self.station_details_data[process_type][current_station]['fixtures'].append(fixture_info)
    
    def match_process_type(self, components: List[str]) -> Tuple[str, float]:
        """
        根据零件组合匹配工艺类型
        
        Args:
            components: 零件列表
            
        Returns:
            (工艺类型, 匹配置信度)
        """
        best_match = ""
        best_confidence = 0.0
        
        components_set = set(components)
        
        for process_type, data in self.process_types_data.items():
            if 'components' not in data:
                continue
                
            process_components_set = set(data['components'])
            
            # 计算匹配度
            intersection = len(components_set & process_components_set)
            union = len(components_set | process_components_set)
            
            if union > 0:
                confidence = intersection / union
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_match = process_type
        
        return best_match, best_confidence
    
    def get_process_info(self, process_type: str) -> Dict:
        """获取工艺类型的详细信息"""
        return self.process_types_data.get(process_type, {})
    
    def get_station_details(self, process_type: str, station: str = None) -> Dict:
        """获取工站详细信息"""
        if station:
            return self.station_details_data.get(process_type, {}).get(station, {})
        else:
            return self.station_details_data.get(process_type, {})
    
    def generate_linespec_response(self, product_family: str, components: List[str], 
                                 project_requirements: str = "") -> Dict:
        """
        生成技术规范分析结果
        
        Args:
            product_family: 产品族 (如SAB)
            components: 零件列表
            project_requirements: 项目特殊要求
            
        Returns:
            分析结果字典
        """
        # 匹配工艺类型
        process_type, confidence = self.match_process_type(components)
        
        if not process_type:
            return {
                'error': '未找到匹配的工艺类型',
                'suggestions': list(self.process_types_data.keys())
            }
        
        # 获取工艺信息
        process_info = self.get_process_info(process_type)
        station_details = self.get_station_details(process_type)
        
        # 构建响应
        response = {
            'matchResult': {
                'processType': process_type,
                'confidence': round(confidence * 100, 1),
                'components': components,
                'estimatedCT': process_info.get('ct', 0),
                'stations': process_info.get('stations', [])
            },
            'processFlow': {
                'stations': process_info.get('stations', []),
                'processes': process_info.get('processes', [])
            },
            'stationDetails': station_details,
            'mermaidDiagram': self._generate_mermaid_diagram(process_info.get('stations', []), 
                                                           process_info.get('processes', []))
        }
        
        return response
    
    def _generate_mermaid_diagram(self, stations: List[str], processes: List[str]) -> str:
        """生成Mermaid流程图"""
        if not stations:
            return ""
        
        diagram = "graph LR\n"
        
        for i, (station, process) in enumerate(zip(stations, processes)):
            node_id = f"ST{i+1}"
            node_label = f"{station}<br/>{process}"
            
            if i == 0:
                diagram += f"    {node_id}[{node_label}]\n"
            else:
                prev_node = f"ST{i}"
                diagram += f"    {prev_node} --> {node_id}[{node_label}]\n"
        
        return diagram

    def get_fixture_list(self, process_type):
        """获取夹具清单"""
        try:
            # 构建夹具清单工作表名称
            sheet_name = f'SAB-{process_type}设备夹具清单'

            # 重新读取Excel文件获取工作表名称
            xl = pd.ExcelFile(self.excel_file_path)
            if sheet_name not in xl.sheet_names:
                print(f"夹具清单工作表不存在: {sheet_name}")
                print(f"可用工作表: {xl.sheet_names}")
                return []

            # 读取夹具清单数据
            df = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)

            # 找到表头行（包含"工站"的行）
            header_row = None
            for i, row in df.iterrows():
                if pd.notna(row.iloc[0]) and '工站' in str(row.iloc[0]):
                    header_row = i
                    break

            if header_row is None:
                return []

            # 提取夹具数据
            fixture_list = []
            current_station = None

            for i in range(header_row + 1, len(df)):
                row = df.iloc[i]

                # 检查是否是新的工站
                if pd.notna(row.iloc[0]) and str(row.iloc[0]).strip():
                    station_text = str(row.iloc[0]).strip()
                    if 'ST' in station_text or '工站' in station_text:
                        current_station = station_text

                # 提取设备/夹具信息
                if pd.notna(row.iloc[2]):  # 设备/夹具/标准件列
                    equipment_name = str(row.iloc[2]).strip()
                    brand = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ''
                    model = str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else ''
                    quantity = str(row.iloc[5]).strip() if pd.notna(row.iloc[5]) else '1'
                    content = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ''

                    fixture_list.append({
                        'station': current_station or 'ST00',
                        'content': content,
                        'equipment_name': equipment_name,
                        'brand': brand,
                        'model': model,
                        'quantity': quantity
                    })

            return fixture_list

        except Exception as e:
            print(f"获取夹具清单时出错: {str(e)}")
            return []

    def get_all_fixture_data(self):
        """获取所有SAB类别的夹具数据"""
        try:
            all_fixture_data = {}
            sab_types = ['A', 'B', 'C', 'D', 'E', 'F']

            for sab_type in sab_types:
                fixture_list = self.get_fixture_list(sab_type)
                if fixture_list:
                    all_fixture_data[f'SAB-{sab_type}'] = fixture_list

            return all_fixture_data

        except Exception as e:
            print(f"获取所有夹具数据时出错: {str(e)}")
            return {}
    
    def get_all_process_types(self) -> List[str]:
        """获取所有工艺类型"""
        return list(self.process_types_data.keys())
    
    def get_components_for_process_type(self, process_type: str) -> List[str]:
        """获取指定工艺类型的零件列表"""
        return self.process_types_data.get(process_type, {}).get('components', [])
