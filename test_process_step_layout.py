#!/usr/bin/env python3
"""
测试工艺步骤布局修改
验证"人or设备"移到右边并缩小占比，标题和内容在同一行显示
"""

import re
import requests

def test_process_step_layout():
    """测试工艺步骤布局是否正确修改"""
    
    print("=== 测试工艺步骤布局修改 ===\n")
    
    try:
        # 读取JavaScript文件内容
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_generator.js文件")
        
        # 1. 检查是否移除了原来的grid布局
        print("\n1. 检查布局结构变更...")
        
        # 检查是否还有原来的2fr 1fr grid布局
        old_grid_pattern = r'grid-template-columns:\s*2fr\s+1fr'
        if re.search(old_grid_pattern, js_content):
            print("❌ 仍然存在原来的2fr 1fr grid布局")
            return False
        else:
            print("✅ 已移除原来的2fr 1fr grid布局")
        
        # 2. 检查新的flex布局
        print("\n2. 检查新的flex布局...")
        
        # 检查是否使用了flex布局
        flex_patterns = [
            r'display:\s*flex',
            r'align-items:\s*center',
            r'align-items:\s*flex-start'
        ]
        
        for pattern in flex_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                print(f"✅ 找到flex布局属性: {pattern} ({len(matches)}个)")
            else:
                print(f"❌ 未找到flex布局属性: {pattern}")
        
        # 3. 检查"人or设备"的新布局
        print("\n3. 检查'人or设备'布局...")
        
        # 检查是否有缩小的宽度设置
        operator_width_pattern = r'width:\s*120px.*flex-shrink:\s*0'
        if re.search(operator_width_pattern, js_content, re.DOTALL):
            print("✅ '人or设备'区域设置了固定宽度和flex-shrink: 0")
        else:
            print("❌ '人or设备'区域未正确设置宽度")
        
        # 检查select的宽度是否缩小
        select_width_pattern = r'width:\s*70px'
        if re.search(select_width_pattern, js_content):
            print("✅ '人or设备'选择器宽度已缩小到70px")
        else:
            print("❌ '人or设备'选择器宽度未缩小")
        
        # 4. 检查标题和内容是否在同一行
        print("\n4. 检查标题和内容布局...")
        
        # 检查工艺过程描述的布局
        process_desc_pattern = r'工艺过程描述:.*?textarea.*?flex:\s*1'
        if re.search(process_desc_pattern, js_content, re.DOTALL):
            print("✅ '工艺过程描述'标题和内容在同一行")
        else:
            print("❌ '工艺过程描述'标题和内容未在同一行")
        
        # 检查产品特性要求的布局
        quality_pattern = r'产品特性要求:.*?textarea.*?flex:\s*1'
        if re.search(quality_pattern, js_content, re.DOTALL):
            print("✅ '产品特性要求'标题和内容在同一行")
        else:
            print("❌ '产品特性要求'标题和内容未在同一行")
        
        # 检查过程防错要求的布局
        error_pattern = r'过程防错要求:.*?textarea.*?flex:\s*1'
        if re.search(error_pattern, js_content, re.DOTALL):
            print("✅ '过程防错要求'标题和内容在同一行")
        else:
            print("❌ '过程防错要求'标题和内容未在同一行")
        
        # 5. 检查white-space: nowrap设置
        print("\n5. 检查标题样式...")
        
        nowrap_count = len(re.findall(r'white-space:\s*nowrap', js_content))
        if nowrap_count >= 4:  # 应该有4个标题设置了nowrap
            print(f"✅ 找到{nowrap_count}个标题设置了white-space: nowrap")
        else:
            print(f"❌ 只找到{nowrap_count}个标题设置了white-space: nowrap，应该至少有4个")
        
        # 6. 检查textarea高度调整
        print("\n6. 检查textarea高度...")
        
        textarea_heights = re.findall(r'height:\s*(\d+)px.*?textarea', js_content)
        if textarea_heights:
            heights = [int(h) for h in textarea_heights]
            print(f"✅ textarea高度设置: {heights}px")
            if all(h <= 40 for h in heights):
                print("✅ 所有textarea高度都已优化（≤40px）")
            else:
                print("⚠️ 部分textarea高度可能过高")
        else:
            print("❌ 未找到textarea高度设置")
        
        print("\n=== 布局测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_layout_visual_structure():
    """测试布局的视觉结构"""
    print("\n=== 测试布局视觉结构 ===")
    
    try:
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 提取createProcessStepHtml函数的内容
        function_pattern = r'createProcessStepHtml\(.*?\{(.*?)\n\s{4}\}'
        function_match = re.search(function_pattern, js_content, re.DOTALL)
        
        if function_match:
            function_content = function_match.group(1)
            print("✅ 找到createProcessStepHtml函数")
            
            # 分析布局结构
            print("\n布局结构分析:")
            
            # 统计div层级
            div_count = len(re.findall(r'<div[^>]*>', function_content))
            print(f"- 总div数量: {div_count}")
            
            # 统计flex容器
            flex_containers = len(re.findall(r'display:\s*flex', function_content))
            print(f"- flex容器数量: {flex_containers}")
            
            # 统计标签和输入框
            labels = len(re.findall(r'<label[^>]*>', function_content))
            textareas = len(re.findall(r'<textarea[^>]*>', function_content))
            selects = len(re.findall(r'<select[^>]*>', function_content))
            
            print(f"- 标签数量: {labels}")
            print(f"- 文本框数量: {textareas}")
            print(f"- 选择器数量: {selects}")
            
            # 检查布局层次
            if '<!-- 第一行：工艺过程描述 + 人or设备' in function_content:
                print("✅ 包含第一行布局注释")
            if '<!-- 第二行：产品特性要求 -->' in function_content:
                print("✅ 包含第二行布局注释")
            if '<!-- 第三行：过程防错要求 -->' in function_content:
                print("✅ 包含第三行布局注释")
            
            return True
        else:
            print("❌ 未找到createProcessStepHtml函数")
            return False
            
    except Exception as e:
        print(f"❌ 视觉结构测试失败: {e}")
        return False

def generate_layout_preview():
    """生成布局预览HTML"""
    print("\n=== 生成布局预览 ===")
    
    try:
        # 创建一个简单的HTML预览文件
        preview_html = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺步骤布局预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .step-preview { 
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white; 
        }
        .step-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem; 
        }
        .step-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .row { 
            display: flex; 
            align-items: center; 
            gap: 0.5rem; 
            margin-bottom: 0.4rem; 
        }
        .row.first-row { 
            align-items: flex-start; 
        }
        .main-content { 
            flex: 1; 
        }
        .operator-section { 
            width: 120px; 
            flex-shrink: 0; 
        }
        label { 
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap; 
        }
        textarea { 
            flex: 1; 
            height: 35px; 
            padding: 0.3rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            resize: none; 
            font-size: 0.8rem; 
            line-height: 1.3; 
        }
        select { 
            width: 70px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 30px; 
        }
        .operator-row { 
            display: flex; 
            align-items: center; 
            gap: 0.3rem; 
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>工艺步骤布局预览</h2>
        <p>新的布局设计：标题和内容在同一行，"人or设备"移到右边并缩小占比</p>
        
        <div class="step-preview">
            <div class="step-header">
                <h6 class="step-title">步骤 1</h6>
                <button style="background: #ff7875; color: white; border: none; border-radius: 2px; padding: 1px 4px; cursor: pointer; font-size: 0.65rem;">×</button>
            </div>
            
            <!-- 第一行：工艺过程描述 + 人or设备 -->
            <div class="row first-row">
                <div class="main-content">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <label>工艺过程描述:</label>
                        <textarea placeholder="请输入工艺过程描述">人工检查包装材料完整性</textarea>
                    </div>
                </div>
                <div class="operator-section">
                    <div class="operator-row">
                        <label>人or设备:</label>
                        <select>
                            <option>人</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 第二行：产品特性要求 -->
            <div class="row">
                <label>产品特性要求:</label>
                <textarea placeholder="请输入产品特性要求">包装材料无破损，标签清晰</textarea>
            </div>
            
            <!-- 第三行：过程防错要求 -->
            <div class="row">
                <label>过程防错要求:</label>
                <textarea placeholder="请输入过程防错要求">目视检查，发现问题立即停止</textarea>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>布局改进说明：</h3>
            <ul>
                <li>✅ "人or设备"移到步骤右边，宽度缩小到120px</li>
                <li>✅ 选择器宽度缩小到70px，节省空间</li>
                <li>✅ 所有标题和内容在同一行显示</li>
                <li>✅ 使用flex布局，响应式更好</li>
                <li>✅ textarea高度统一为35px，更紧凑</li>
                <li>✅ 标题设置white-space: nowrap防止换行</li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        
        with open('layout_preview.html', 'w', encoding='utf-8') as f:
            f.write(preview_html)
        
        print("✅ 布局预览文件已生成: layout_preview.html")
        print("💡 可以在浏览器中打开此文件查看新布局效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成预览失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试工艺步骤布局修改...\n")
    
    # 运行布局测试
    layout_ok = test_process_step_layout()
    
    # 运行视觉结构测试
    structure_ok = test_layout_visual_structure()
    
    # 生成布局预览
    preview_ok = generate_layout_preview()
    
    print(f"\n{'='*60}")
    if layout_ok and structure_ok:
        print("🎉 所有测试通过！")
        print("✅ 工艺步骤布局已成功修改")
        print("✅ '人or设备'已移到右边并缩小占比")
        print("✅ 所有标题和内容都在同一行显示")
        print("✅ 布局更加紧凑和美观")
        
        if preview_ok:
            print("✅ 布局预览文件已生成")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    print(f"{'='*60}")
