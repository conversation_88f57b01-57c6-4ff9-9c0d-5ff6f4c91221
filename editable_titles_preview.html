
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可编辑标题预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .step-preview { 
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white; 
        }
        .step-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem; 
        }
        .step-title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .step-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .operator-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        .editable-title { 
            width: 100%; 
            padding: 0.4rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.85rem; 
            font-weight: 500; 
            color: #333; 
            background: #f8f9fa;
            margin-bottom: 0.4rem;
        }
        .editable-title:last-child {
            margin-bottom: 0;
        }
        .editable-title:focus {
            outline: none;
            border-color: #1a73e8;
            background: white;
        }
        select { 
            width: 80px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 28px; 
        }
        .delete-btn {
            background: #ff7875; 
            color: white; 
            border: none; 
            border-radius: 2px; 
            padding: 1px 4px; 
            cursor: pointer; 
            font-size: 0.65rem;
        }
        .operator-label {
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>可编辑标题预览</h2>
        <p>新的设计：移除重复内容，只保留可编辑的标题</p>
        
        <div class="step-preview">
            <!-- 步骤标题行：步骤号 + 人or设备 + 删除按钮 -->
            <div class="step-header">
                <div class="step-title-group">
                    <h6 class="step-title">步骤 1</h6>
                    <div class="operator-group">
                        <label class="operator-label">人or设备:</label>
                        <select>
                            <option>人</option>
                        </select>
                    </div>
                </div>
                <button class="delete-btn">×</button>
            </div>
            
            <!-- 可编辑标题 -->
            <input type="text" class="editable-title" value="人工检查包装材料完整性" placeholder="请输入工艺过程描述">
            <input type="text" class="editable-title" value="包装材料无破损，标签清晰" placeholder="请输入产品特性要求">
            <input type="text" class="editable-title" value="目视检查，发现问题立即停止" placeholder="请输入过程防错要求">
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>可编辑标题改进说明：</h3>
            <ul>
                <li>✅ 移除了标签+文本框的重复结构</li>
                <li>✅ 每个内容项变成可编辑的输入框</li>
                <li>✅ 输入框具有默认提示文字</li>
                <li>✅ 样式统一，视觉更简洁</li>
                <li>✅ 背景色区分，便于识别可编辑区域</li>
                <li>✅ 聚焦时背景变白，提供视觉反馈</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px;">
            <h3>布局对比：</h3>
            
            <h4>修改前（重复结构）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 步骤 1    人or设备: [选择器]                      × │
├─────────────────────────────────────────────────────────┤
│ 工艺过程描述: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求: [──────────文本框──────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
            
            <h4>修改后（简洁结构）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 步骤 1    人or设备: [选择器]                      × │
├─────────────────────────────────────────────────────────┤
│ [────────────可编辑标题1────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ [────────────可编辑标题2────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ [────────────可编辑标题3────────────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
    </div>
</body>
</html>
        