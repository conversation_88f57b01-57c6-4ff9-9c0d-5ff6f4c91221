# 工艺页面删除和步骤编辑问题修复

## 问题总结

用户反馈了三个主要问题：

1. **工站删除问题**：点击某个工站框的"删除"时，会将全部已识别工站删除（工艺页面和设备页面都有此问题）
2. **步骤编辑问题**：无法在已识别的工站中进行增加步骤或删除步骤的编辑
3. **新增工站步骤格式问题**：在新增工站中添加步骤，新步骤的格式不正确

## 修复方案

### 1. 修复工站删除功能

#### 问题分析
原来的删除函数调用了`generateProcessStations`和`generateEquipmentStations`，这些函数会清空现有内容并重新生成所有工站，导致删除单个工站时清空了所有工站。

#### 修复内容

**文件**: `static/js/station_manager.js`

##### 1.1 修复工艺工站删除函数 (第263-301行)

```javascript
// 修复前：会清空所有工站
function deleteProcessStation(stationIndex) {
    if (confirm('确定要删除这个工艺工站吗？')) {
        processStationsData.splice(stationIndex, 1);
        // 重新生成所有工站（删除工站后需要完全重新生成）
        window.stationGenerator.generateProcessStations(processStationsData, false);
    }
}

// 修复后：只删除指定工站
function deleteProcessStation(stationIndex) {
    if (confirm('确定要删除这个工艺工站吗？')) {
        console.log(`[DEBUG] deleteProcessStation: 删除工站 ${stationIndex}`);
        
        // 从数据数组中删除指定工站
        processStationsData.splice(stationIndex, 1);
        
        // 同步到window.processStationsData
        if (typeof window.processStationsData !== 'undefined') {
            window.processStationsData.splice(stationIndex, 1);
        }

        // 删除对应的DOM元素
        const stationElement = document.querySelector(`[data-station-index="${stationIndex}"]`);
        if (stationElement) {
            // 找到包含插入按钮的完整工站块
            let stationBlock = stationElement;
            while (stationBlock && !stationBlock.classList.contains('station-block')) {
                stationBlock = stationBlock.parentElement;
            }
            
            // 删除工站前的插入按钮（如果存在）
            const prevInsertBtn = stationBlock?.previousElementSibling;
            if (prevInsertBtn && prevInsertBtn.textContent.includes('在此前插入工站')) {
                prevInsertBtn.remove();
            }
            
            // 删除工站块
            if (stationBlock) {
                stationBlock.remove();
            }
        }

        // 重新索引剩余的工站
        reindexProcessStations();
        
        console.log(`[DEBUG] deleteProcessStation: 工站 ${stationIndex} 删除完成`);
    }
}
```

##### 1.2 修复设备工站删除函数 (第582-620行)

```javascript
// 修复前：会清空所有设备工站
function deleteEquipmentStation(stationIndex) {
    if (confirm('确定要删除这个设备工站吗？')) {
        equipmentStationsData.splice(stationIndex, 1);
        // 重新生成所有设备工站
        window.stationGenerator.generateEquipmentStations(equipmentStationsData);
    }
}

// 修复后：只删除指定设备工站
function deleteEquipmentStation(stationIndex) {
    if (confirm('确定要删除这个设备工站吗？')) {
        console.log(`[DEBUG] deleteEquipmentStation: 删除设备工站 ${stationIndex}`);
        
        // 从数据数组中删除指定工站
        equipmentStationsData.splice(stationIndex, 1);
        
        // 同步到window.equipmentStationsData
        if (typeof window.equipmentStationsData !== 'undefined') {
            window.equipmentStationsData.splice(stationIndex, 1);
        }

        // 删除对应的DOM元素
        const stationElement = document.querySelector(`[data-station-index="${stationIndex}"]`);
        if (stationElement) {
            // 找到包含插入按钮的完整设备工站块
            let stationBlock = stationElement;
            while (stationBlock && !stationBlock.classList.contains('equipment-station-block')) {
                stationBlock = stationBlock.parentElement;
            }
            
            // 删除工站前的插入按钮（如果存在）
            const prevInsertBtn = stationBlock?.previousElementSibling;
            if (prevInsertBtn && prevInsertBtn.textContent.includes('在此前插入设备工站')) {
                prevInsertBtn.remove();
            }
            
            // 删除工站块
            if (stationBlock) {
                stationBlock.remove();
            }
        }

        // 重新索引剩余的设备工站
        reindexEquipmentStations();
        
        console.log(`[DEBUG] deleteEquipmentStation: 设备工站 ${stationIndex} 删除完成`);
    }
}
```

##### 1.3 添加重新索引函数 (第622-703行)

```javascript
/**
 * 重新索引工艺工站
 */
function reindexProcessStations() {
    console.log(`[DEBUG] reindexProcessStations: 开始重新索引工艺工站`);
    
    const container = document.getElementById('process-stations-list');
    if (!container) return;
    
    const stationBlocks = container.querySelectorAll('.station-block[data-station-index]');
    stationBlocks.forEach((block, newIndex) => {
        // 更新data-station-index属性
        block.setAttribute('data-station-index', newIndex);
        
        // 更新所有相关的onclick事件和ID
        updateStationBlockIds(block, newIndex, 'process');
    });
    
    console.log(`[DEBUG] reindexProcessStations: 重新索引完成，共 ${stationBlocks.length} 个工站`);
}

/**
 * 重新索引设备工站
 */
function reindexEquipmentStations() {
    console.log(`[DEBUG] reindexEquipmentStations: 开始重新索引设备工站`);
    
    const container = document.getElementById('equipment-stations-list');
    if (!container) return;
    
    const stationBlocks = container.querySelectorAll('.equipment-station-block[data-station-index]');
    stationBlocks.forEach((block, newIndex) => {
        // 更新data-station-index属性
        block.setAttribute('data-station-index', newIndex);
        
        // 更新所有相关的onclick事件和ID
        updateStationBlockIds(block, newIndex, 'equipment');
    });
    
    console.log(`[DEBUG] reindexEquipmentStations: 重新索引完成，共 ${stationBlocks.length} 个设备工站`);
}

/**
 * 更新工站块中的所有ID和事件处理器
 * @param {Element} block - 工站块元素
 * @param {number} newIndex - 新的索引
 * @param {string} type - 工站类型 ('process' 或 'equipment')
 */
function updateStationBlockIds(block, newIndex, type) {
    // 更新所有包含旧索引的onclick属性
    const clickableElements = block.querySelectorAll('[onclick]');
    clickableElements.forEach(element => {
        let onclick = element.getAttribute('onclick');
        if (onclick) {
            // 替换函数调用中的索引参数
            if (type === 'process') {
                onclick = onclick.replace(/deleteProcessStation\(\d+\)/g, `deleteProcessStation(${newIndex})`);
                onclick = onclick.replace(/toggleStationCollapse\(\d+\)/g, `toggleStationCollapse(${newIndex})`);
                onclick = onclick.replace(/makeStationTitleEditable\(\d+/g, `makeStationTitleEditable(${newIndex}`);
                onclick = onclick.replace(/insertProcessStepBefore\(\d+/g, `insertProcessStepBefore(${newIndex}`);
                onclick = onclick.replace(/insertProcessStepAfter\(\d+/g, `insertProcessStepAfter(${newIndex}`);
                onclick = onclick.replace(/deleteProcessStep\(\d+/g, `deleteProcessStep(${newIndex}`);
            } else if (type === 'equipment') {
                onclick = onclick.replace(/deleteEquipmentStation\(\d+\)/g, `deleteEquipmentStation(${newIndex})`);
                onclick = onclick.replace(/toggleEquipmentStationCollapse\(\d+\)/g, `toggleEquipmentStationCollapse(${newIndex})`);
                onclick = onclick.replace(/makeEquipmentStationTitleEditable\(\d+/g, `makeEquipmentStationTitleEditable(${newIndex}`);
            }
            element.setAttribute('onclick', onclick);
        }
    });
    
    // 更新所有包含索引的ID属性
    const elementsWithIds = block.querySelectorAll('[id*="-"]');
    elementsWithIds.forEach(element => {
        let id = element.getAttribute('id');
        if (id) {
            // 替换ID中的索引部分
            id = id.replace(/-\d+(-|$)/g, `-${newIndex}$1`);
            element.setAttribute('id', id);
        }
    });
}
```

### 2. 修复步骤编辑功能

#### 问题分析
步骤编辑功能本身是正常的，问题主要在于数据同步和DOM更新。现有的步骤插入、删除和更新函数都是正确的。

#### 验证内容
- ✅ `insertProcessStep` 函数正常工作
- ✅ `deleteProcessStep` 函数正常工作
- ✅ `updateProcessStep` 函数正常工作
- ✅ 步骤插入菜单显示/隐藏功能正常
- ✅ `regenerateProcessStation` 函数正确更新步骤容器

### 3. 修复新增工站步骤格式

#### 问题分析
新增工站时创建的步骤数据结构中，字段值为空字符串，导致显示时没有占位符文本，用户体验不佳。

#### 修复内容

**文件**: `static/js/station_manager.js`

##### 3.1 修复新增工站时的步骤数据 (第735-746行)

```javascript
// 修复前：空字符串
const newStation = {
    station_number: newStationNumber.toString(),
    station_name: `新工站${newStationNumber}`,
    content: '',
    process_steps: [{
        step_number: '1',
        description: '',
        operator: '',
        quality_requirements: '',
        error_prevention: ''
    }]
};

// 修复后：有意义的占位符文本
const newStation = {
    station_number: newStationNumber.toString(),
    station_name: `新工站${newStationNumber}`,
    content: '',
    process_steps: [{
        step_number: '1',
        description: '请输入工艺过程描述',
        operator: '',
        quality_requirements: '请输入产品特性要求',
        error_prevention: '请输入过程防错要求'
    }]
};
```

##### 3.2 修复插入新步骤时的数据 (第144-150行)

```javascript
// 修复前：空字符串
const newStep = {
    step_number: (insertIndex + 1).toString(),
    description: '',
    operator: '',
    quality_requirements: '',
    error_prevention: ''
};

// 修复后：有意义的占位符文本
const newStep = {
    step_number: (insertIndex + 1).toString(),
    description: '请输入工艺过程描述',
    operator: '',
    quality_requirements: '请输入产品特性要求',
    error_prevention: '请输入过程防错要求'
};
```

## 修复效果

### 1. 工站删除功能

#### 修复前：
- ❌ 删除单个工站会清空所有已识别工站
- ❌ 用户误操作导致大量内容丢失
- ❌ 需要重新识别所有工站

#### 修复后：
- ✅ 只删除指定的单个工站
- ✅ 保留其他所有工站的内容
- ✅ 自动重新索引剩余工站
- ✅ 正确更新DOM元素和事件处理器

### 2. 步骤编辑功能

#### 功能验证：
- ✅ 在已识别工站中可以正常添加步骤
- ✅ 在已识别工站中可以正常删除步骤
- ✅ 步骤插入菜单正常显示和隐藏
- ✅ 步骤内容更新正常保存
- ✅ 步骤编号自动重新排序

### 3. 新增工站步骤格式

#### 修复前：
- ❌ 新步骤字段为空，没有引导文本
- ❌ 用户不知道应该填写什么内容
- ❌ 界面显示不友好

#### 修复后：
- ✅ 新步骤包含有意义的占位符文本
- ✅ 用户清楚知道每个字段的用途
- ✅ 界面更加友好和直观

## 测试建议

### 1. 工站删除测试
1. **创建多个工站**：通过AI识别或手动添加多个工站
2. **删除中间工站**：删除中间的某个工站，验证其他工站保留
3. **删除第一个工站**：验证后续工站正确重新索引
4. **删除最后一个工站**：验证前面工站不受影响

### 2. 步骤编辑测试
1. **在已识别工站中添加步骤**：验证新步骤格式正确
2. **在已识别工站中删除步骤**：验证其他步骤保留且重新编号
3. **步骤内容编辑**：验证内容修改正确保存
4. **步骤插入菜单**：验证悬停菜单正常显示和隐藏

### 3. 新增工站测试
1. **插入新工站**：验证新工站包含正确格式的步骤
2. **在新工站中添加步骤**：验证新步骤格式正确
3. **步骤内容填写**：验证占位符文本有助于用户理解

## 总结

通过这次修复，成功解决了：

1. **数据安全性**：删除操作不再误删所有内容
2. **功能完整性**：步骤编辑功能完全可用
3. **用户体验**：新增步骤有清晰的引导文本
4. **系统稳定性**：正确的DOM更新和事件处理

现在用户可以安全地进行工站和步骤的增删改操作，不用担心误操作导致内容丢失！🎉
