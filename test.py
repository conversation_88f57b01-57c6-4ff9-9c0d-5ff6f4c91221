from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.user_credential import UserCredential
import time

def get_person_name(person_data):
    """从人员数据中提取姓名"""
    print(f"解析人员数据: {person_data}")  # 添加调试输出
    print(f"数据类型: {type(person_data)}")  # 添加类型信息
    
    try:
        # 如果是字符串形式的JSON，尝试解析
        if isinstance(person_data, str):
            try:
                import json
                person_data = json.loads(person_data)
                print("已将字符串解析为JSON")
            except:
                pass
                
        # 检查是否为列表类型且不为空
        if isinstance(person_data, list) and person_data:
            print(f"列表中第一个元素: {person_data[0]}")
            # 检查第一个元素是否为字典且包含title键
            if isinstance(person_data[0], dict):
                print(f"可用的键: {person_data[0].keys()}")
                if 'title' in person_data[0]:
                    return person_data[0]['title']
                elif 'Title' in person_data[0]:  # 检查大写的Title
                    return person_data[0]['Title']
        
        # 如果是字典类型
        if isinstance(person_data, dict):
            print(f"字典中的键: {person_data.keys()}")
            if 'title' in person_data:
                return person_data['title']
            elif 'Title' in person_data:
                return person_data['Title']
                
        print("未能找到有效的姓名信息")
        return ''
    except Exception as e:
        print(f"解析人员数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()  # 打印完整的错误堆栈
        return ''

def get_sharepoint_data():
    """从 SharePoint 获取项目数据"""
    try:
        # 初始化客户端和输出列表
        output = []
        ctx = ClientContext("https://autoliv.sharepoint.com/sites/msteams_a5f551").with_credentials(
            UserCredential("<EMAIL>", "autoliv0708")
        )

        # 获取列表
        list_title = "Airbag New Project"
        sp_list = ctx.web.lists.get_by_title(list_title)
        
        # 使用 select 和 expand 获取所需字段
        items = sp_list.items.select([
            "Title",  # 项目名称
            "field_2",  # 产品系列
            "field_1",  # 客户
            "Plant",   # 生产基地
            "field_38",  # SOP日期
            "field_9",  # 线号
            "field_8",  # 生产线类型
            "APP_x002d_P/Title",  # APP负责人
            "APP_x002d_P/Id",
            "AEP_x002d_P/Title",  # AEP负责人
            "AEP_x002d_P/Id",
            "field_26",  # 产线预验收日期
            "LineonsiteinstallationDate"  # 离线调试完成日期
        ]).expand(["APP_x002d_P", "AEP_x002d_P"]).get_all(5000).execute_query()

        print(f"从SharePoint加载了 {len(items)} 个项目")
        
        # 提取和处理数据
        seen_titles = set()
        for item in items:
            print(f"处理项目: {item.properties.get('Title')}")
            
            # 获取负责人信息（兼容列表和字典）
            app_person = item.properties.get("APP_x002d_P", [{}])
            if isinstance(app_person, list) and app_person:
                app_person = app_person[0]
            aep_person = item.properties.get("AEP_x002d_P", [{}])
            if isinstance(aep_person, list) and aep_person:
                aep_person = aep_person[0]
            
            project_name = item.properties.get("Title", "")
            if project_name and project_name not in seen_titles:
                # 在 project_data 字典中添加新字段
                project_data = {
                    "project-name": project_name,
                    "product-family": item.properties.get("field_2", ""),
                    "customer": item.properties.get("field_1", ""),
                    "production-site": item.properties.get("Plant", ""),
                    "SOP-date": item.properties.get("field_38", ""),
                    "APP-responsible": app_person.get("Title", ""),
                    "AEP-responsible": aep_person.get("Title", ""),
                    "APP-responsible-name": app_person.get("title", ""),
                    "APP-responsible-email": app_person.get("email", ""),
                    "APP-responsible-phone": app_person.get("sip", ""),
                    "AEP-responsible-name": aep_person.get("title", ""),
                    "AEP-responsible-email": aep_person.get("email", ""),
                    "AEP-responsible-phone": aep_person.get("sip", ""),
                    "Line-number": item.properties.get("field_9", ""),
                    "line-type": item.properties.get("field_8", ""),
                    "pre-acceptance-date": item.properties.get("field_26", ""),  # 添加产线预验收日期
                    "offline-debug-date": item.properties.get("LineonsiteinstallationDate", "")  # 添加离线调试完成日期
                }
                output.append(project_data)
                seen_titles.add(project_name)
            elif not project_name:
                print("警告：跳过无名称项目")

        print(f"有效项目总数: {len(output)}")
        return output
        
    except Exception as e:
        print(f"Error in get_sharepoint_data: {str(e)}")
        return []

# 如果直接运行此文件，则测试函数
if __name__ == "__main__":
    data = get_sharepoint_data()
    print(f"获取了 {len(data)} 个项目")
    if data:
        print("前3个项目示例:")
        for i, item in enumerate(data[:3]):
            print(f"{i+1}. {item['project-name']} - {item['offline-debug-date']}")