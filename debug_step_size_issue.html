<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤框大小问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-title {
            color: #1a73e8;
            margin-bottom: 15px;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .debug-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #1557b0;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2 class="debug-title">步骤框大小问题调试工具</h2>
        <p>此工具用于检查工艺页面中步骤框大小不一致的问题。</p>
        
        <button class="debug-button" onclick="analyzeStepElements()">分析步骤元素</button>
        <button class="debug-button" onclick="checkCSSStyles()">检查CSS样式</button>
        <button class="debug-button" onclick="compareStepSizes()">对比步骤大小</button>
        <button class="debug-button" onclick="fixStepStyles()">修复步骤样式</button>
        
        <div id="debug-output"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }

        function analyzeStepElements() {
            log('开始分析步骤元素...');
            
            // 查找所有步骤元素
            const allSteps = document.querySelectorAll('.process-step');
            log(`找到 ${allSteps.length} 个步骤元素`);
            
            if (allSteps.length === 0) {
                log('未找到任何步骤元素，请确保在工艺页面运行此脚本', 'error');
                return;
            }
            
            allSteps.forEach((step, index) => {
                const info = {
                    index: index,
                    className: step.className,
                    hasCompactClass: step.classList.contains('compact'),
                    computedStyles: {
                        padding: getComputedStyle(step).padding,
                        marginBottom: getComputedStyle(step).marginBottom,
                        border: getComputedStyle(step).border,
                        borderRadius: getComputedStyle(step).borderRadius,
                        background: getComputedStyle(step).background
                    },
                    inlineStyles: step.getAttribute('style') || '无',
                    dimensions: {
                        width: step.offsetWidth,
                        height: step.offsetHeight
                    }
                };
                
                log(`步骤 ${index + 1}:`, 'info');
                log(`  类名: ${info.className}`, 'info');
                log(`  是否有compact类: ${info.hasCompactClass}`, info.hasCompactClass ? 'success' : 'warning');
                log(`  内联样式: ${info.inlineStyles}`, 'info');
                log(`  计算样式 - padding: ${info.computedStyles.padding}`, 'info');
                log(`  计算样式 - margin-bottom: ${info.computedStyles.marginBottom}`, 'info');
                log(`  尺寸: ${info.dimensions.width}x${info.dimensions.height}px`, 'info');
                log('---', 'info');
            });
        }

        function checkCSSStyles() {
            log('检查CSS样式规则...');
            
            // 检查相关的CSS规则
            const stylesheets = Array.from(document.styleSheets);
            const relevantRules = [];
            
            stylesheets.forEach(sheet => {
                try {
                    const rules = Array.from(sheet.cssRules || sheet.rules || []);
                    rules.forEach(rule => {
                        if (rule.selectorText && rule.selectorText.includes('process-step')) {
                            relevantRules.push({
                                selector: rule.selectorText,
                                styles: rule.style.cssText
                            });
                        }
                    });
                } catch (e) {
                    log(`无法访问样式表: ${e.message}`, 'warning');
                }
            });
            
            log(`找到 ${relevantRules.length} 个相关CSS规则:`);
            relevantRules.forEach(rule => {
                log(`  ${rule.selector}: ${rule.styles}`, 'info');
            });
        }

        function compareStepSizes() {
            log('对比步骤大小...');
            
            const allSteps = document.querySelectorAll('.process-step');
            if (allSteps.length < 2) {
                log('需要至少2个步骤才能进行对比', 'warning');
                return;
            }
            
            const sizes = Array.from(allSteps).map((step, index) => ({
                index: index,
                width: step.offsetWidth,
                height: step.offsetHeight,
                padding: getComputedStyle(step).padding,
                marginBottom: getComputedStyle(step).marginBottom,
                hasCompact: step.classList.contains('compact')
            }));
            
            // 检查是否有大小不一致的步骤
            const firstSize = sizes[0];
            const inconsistentSteps = sizes.filter(size => 
                size.height !== firstSize.height || 
                size.padding !== firstSize.padding ||
                size.marginBottom !== firstSize.marginBottom
            );
            
            if (inconsistentSteps.length > 0) {
                log(`发现 ${inconsistentSteps.length} 个大小不一致的步骤:`, 'warning');
                inconsistentSteps.forEach(step => {
                    log(`  步骤 ${step.index + 1}: ${step.width}x${step.height}px, padding: ${step.padding}, margin-bottom: ${step.marginBottom}, compact: ${step.hasCompact}`, 'warning');
                });
            } else {
                log('所有步骤大小一致', 'success');
            }
            
            // 显示详细对比
            log('详细尺寸对比:');
            sizes.forEach(size => {
                log(`  步骤 ${size.index + 1}: ${size.width}x${size.height}px, padding: ${size.padding}, compact: ${size.hasCompact}`, 'info');
            });
        }

        function fixStepStyles() {
            log('尝试修复步骤样式...');
            
            const allSteps = document.querySelectorAll('.process-step');
            let fixedCount = 0;
            
            allSteps.forEach((step, index) => {
                // 确保有compact类
                if (!step.classList.contains('compact')) {
                    step.classList.add('compact');
                    log(`为步骤 ${index + 1} 添加了compact类`, 'success');
                    fixedCount++;
                }
                
                // 应用强制样式
                const forceStyles = {
                    'border': '1px solid #e8e8e8 !important',
                    'border-radius': '4px !important',
                    'padding': '0.5rem !important',
                    'margin-bottom': '0.5rem !important',
                    'background': 'white !important'
                };
                
                Object.entries(forceStyles).forEach(([property, value]) => {
                    step.style.setProperty(property, value.replace(' !important', ''), 'important');
                });
                
                // 修复内部元素
                const innerDivs = step.querySelectorAll('div[style*="margin-bottom"]');
                innerDivs.forEach(div => {
                    div.style.setProperty('margin-bottom', '0.4rem', 'important');
                });
                
                const lastDiv = step.querySelector('div:last-child');
                if (lastDiv) {
                    lastDiv.style.setProperty('margin-bottom', '0', 'important');
                }
            });
            
            log(`修复完成，共处理 ${allSteps.length} 个步骤，修复 ${fixedCount} 个问题`, 'success');
            
            // 重新检查
            setTimeout(() => {
                compareStepSizes();
            }, 100);
        }

        // 页面加载完成后自动运行分析
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('页面加载完成，开始自动分析...', 'info');
                analyzeStepElements();
            }, 1000);
        });
    </script>
</body>
</html>
