#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤插入菜单功能测试脚本
验证新的悬停菜单插入功能是否正确实现
"""

import os
import re
import sys

def test_step_insert_menu_implementation():
    """测试步骤插入菜单实现"""
    print("🧪 测试步骤插入菜单实现...")
    
    try:
        # 读取station_generator.js文件
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_generator.js文件")
        
        # 1. 检查是否移除了中间的插入按钮
        print("\n1. 检查中间插入按钮移除...")
        
        removed_elements = [
            '↑ 插入步骤',
            'insertProcessStepBefore.*↑ 插入步骤',
            '步骤前插入按钮'
        ]
        
        for element in removed_elements:
            if re.search(element, js_content):
                print(f"❌ 仍然存在应移除的元素: {element}")
                return False
            else:
                print(f"✅ 已移除: {element}")
        
        # 2. 检查悬停菜单HTML结构
        print("\n2. 检查悬停菜单HTML结构...")
        
        required_menu_elements = [
            'step-insert-menu',
            'step-insert-btn',
            'step-insert-dropdown',
            'onmouseenter="showStepInsertMenu',
            'onmouseleave="hideStepInsertMenu',
            '在此步骤前插入',
            '在此步骤后插入'
        ]
        
        for element in required_menu_elements:
            if element in js_content:
                print(f"✅ 菜单元素存在: {element}")
            else:
                print(f"❌ 菜单元素不存在: {element}")
                return False
        
        # 3. 检查JavaScript函数
        print("\n3. 检查JavaScript函数...")
        
        required_functions = [
            'function showStepInsertMenu(',
            'function hideStepInsertMenu(',
            'function hideAllStepInsertMenus('
        ]
        
        for func in required_functions:
            if func in js_content:
                print(f"✅ 函数存在: {func}")
            else:
                print(f"❌ 函数不存在: {func}")
                return False
        
        # 4. 检查菜单样式
        print("\n4. 检查菜单样式...")
        
        style_elements = [
            'position: relative',
            'position: absolute',
            'z-index: 1000',
            'box-shadow:',
            'display: none'
        ]
        
        for style in style_elements:
            if style in js_content:
                print(f"✅ 样式存在: {style}")
            else:
                print(f"❌ 样式不存在: {style}")
                return False
        
        # 5. 检查是否保留了标题行的按钮
        print("\n5. 检查标题行按钮保留...")

        preserved_elements = [
            '↑插入',
            '\\+添加',
            'insertProcessStep.*0',
            'addProcessStep'
        ]

        for element in preserved_elements:
            if re.search(element, js_content):
                print(f"✅ 保留的元素存在: {element}")
            else:
                print(f"❌ 保留的元素不存在: {element}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_menu_interaction_logic():
    """测试菜单交互逻辑"""
    print("\n🧪 测试菜单交互逻辑...")
    
    try:
        # 读取station_generator.js文件
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 检查鼠标事件处理
        mouse_events = [
            'onmouseenter=',
            'onmouseleave=',
            'onmouseover=',
            'onmouseout='
        ]
        
        for event in mouse_events:
            if event in js_content:
                print(f"✅ 鼠标事件存在: {event}")
            else:
                print(f"⚠️  鼠标事件可能缺失: {event}")
        
        # 检查延迟处理逻辑
        if 'setTimeout' in js_content and '100' in js_content:
            print("✅ 延迟处理逻辑存在")
        else:
            print("❌ 延迟处理逻辑不存在")
            return False
        
        # 检查悬停状态检查
        if ':hover' in js_content:
            print("✅ 悬停状态检查存在")
        else:
            print("❌ 悬停状态检查不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_test_html():
    """生成测试HTML页面"""
    print("\n📄 生成测试HTML页面...")
    
    test_html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤插入菜单测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 步骤插入菜单测试页面</h1>
        
        <div class="test-section">
            <h2>测试说明</h2>
            <p>1. 每个步骤框右边只有一个"+"按钮</p>
            <p>2. 鼠标悬停在"+"按钮上时显示菜单</p>
            <p>3. 菜单包含"在此步骤前插入"和"在此步骤后插入"两个选项</p>
            <p>4. 保留标题行的"↑插入"和"+添加"按钮</p>
        </div>
        
        <div class="test-section">
            <div id="stations-list"></div>
        </div>
        
        <div class="test-section">
            <button onclick="initTestData()">初始化测试数据</button>
            <button onclick="testInsertMenu()">测试插入菜单</button>
        </div>
    </div>

    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>
    
    <script>
        function initTestData() {
            const testStation = {
                station_number: '10',
                station_name: '测试工站',
                content: '测试内容',
                process_steps: [
                    {
                        step_number: '1',
                        description: '第一个步骤',
                        operator: '人',
                        quality_requirements: '质量要求1',
                        error_prevention: '防错要求1'
                    },
                    {
                        step_number: '2', 
                        description: '第二个步骤',
                        operator: '设备',
                        quality_requirements: '质量要求2',
                        error_prevention: '防错要求2'
                    }
                ]
            };
            
            if (typeof StationGenerator !== 'undefined') {
                const generator = new StationGenerator();
                generator.generateProcessStations([testStation]);
                console.log('测试数据初始化完成');
            } else {
                console.error('StationGenerator未定义');
            }
        }
        
        function testInsertMenu() {
            console.log('测试插入菜单功能...');
            
            // 检查菜单元素是否存在
            const menus = document.querySelectorAll('.step-insert-menu');
            console.log(`找到 ${menus.length} 个插入菜单`);
            
            const dropdowns = document.querySelectorAll('.step-insert-dropdown');
            console.log(`找到 ${dropdowns.length} 个下拉菜单`);
            
            // 检查函数是否存在
            const functions = ['showStepInsertMenu', 'hideStepInsertMenu', 'hideAllStepInsertMenus'];
            functions.forEach(funcName => {
                console.log(`${funcName}: ${typeof window[funcName] === 'function' ? '存在' : '不存在'}`);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>"""
    
    with open('test_step_menu.html', 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    print("✅ 测试页面已生成: test_step_menu.html")

def main():
    """主测试函数"""
    print("🚀 开始测试步骤插入菜单功能...")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_step_insert_menu_implementation,
        test_menu_interaction_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    # 生成测试页面
    generate_test_html()
    
    # 输出测试结果
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 步骤插入菜单功能实现完成！")
        print("\n✅ 实现的功能:")
        print("1. 移除了步骤中间的'↑插入步骤'按钮")
        print("2. 为每个步骤右边的'+'按钮添加了悬停菜单")
        print("3. 菜单包含'在此步骤前插入'和'在此步骤后插入'选项")
        print("4. 保留了标题行的'↑插入'和'+添加'按钮")
        print("5. 添加了完整的鼠标交互逻辑")
        
        print("\n🔧 测试方法:")
        print("1. 打开 test_step_menu.html 进行交互测试")
        print("2. 点击'初始化测试数据'按钮")
        print("3. 将鼠标悬停在步骤右边的'+'按钮上")
        print("4. 验证菜单是否正确显示和工作")
        
        return True
    else:
        print("❌ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
