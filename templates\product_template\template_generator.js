document.addEventListener('DOMContentLoaded', function() {
    const partsTableBody = document.getElementById('partsTableBody');
    const addPartRowBtn = document.getElementById('addPartRow');
    const generatePromptBtn = document.getElementById('generatePrompt');
    
    // 添加零件行
    function addPartRow() {
        const row = document.createElement('tr');
        row.className = 'part-row';
        row.innerHTML = `
            <td><input type="text" class="project-name"></td>
            <td><input type="text" class="part-number"></td>
            <td><input type="text" class="part-name"></td>
            <td><input type="text" class="unit-quantity"></td>
            <td><input type="text" class="package-size"></td>
            <td><input type="text" class="package-quantity"></td>
            <td>
                <select class="part-status">
                    <option value="新制">新制</option>
                    <option value="共用">共用</option>
                </select>
            </td>
            <td><input type="text" class="shared-project"></td>
            <td><input type="text" class="remarks"></td>
            <td>
                <button class="btn delete-row">删除</button>
            </td>
        `;
        
        // 添加删除行事件
        row.querySelector('.delete-row').addEventListener('click', () => {
            row.remove();
        });
        
        partsTableBody.appendChild(row);
    }
    
    // 生成提示语
    function generatePrompt() {
        const productType = document.getElementById('productType').value;
        const templateType = document.getElementById('templateType').value;
        
        if (!productType || !templateType) {
            alert('请选择产品类型和模板分类');
            return;
        }
        
        const parts = [];
        document.querySelectorAll('.part-row').forEach(row => {
            parts.push({
                projectName: row.querySelector('.project-name').value,
                partNumber: row.querySelector('.part-number').value,
                partName: row.querySelector('.part-name').value,
                unitQuantity: row.querySelector('.unit-quantity').value,
                packageSize: row.querySelector('.package-size').value,
                packageQuantity: row.querySelector('.package-quantity').value,
                partStatus: row.querySelector('.part-status').value,
                sharedProject: row.querySelector('.shared-project').value,
                remarks: row.querySelector('.remarks').value
            });
        });
        
        const prompt = `请帮我完成一个${productType}产品的${templateType}类型模板的零件信息审核。

产品信息：
- 产品类型：${productType}
- 模板分类：${templateType}

零件清单：
${parts.map(part => `
• 零件信息：
  - 项目名称：${part.projectName}
  - 零件编号：${part.partNumber}
  - 零件名称：${part.partName}
  - 单位用量：${part.unitQuantity}
  - 包装尺寸：${part.packageSize}
  - 单位包装数量：${part.packageQuantity}
  - 零件状态：${part.partStatus}
  - 共享项目：${part.sharedProject}
  - 备注：${part.remarks}
`).join('')}

请检查以上信息是否完整，并提供相关建议。`;
        
        // 将提示语复制到剪贴板
        navigator.clipboard.writeText(prompt).then(() => {
            alert('提示语已生成并复制到剪贴板！');
        });
    }
    
    // 添加事件监听
    addPartRowBtn.addEventListener('click', addPartRow);
    generatePromptBtn.addEventListener('click', generatePrompt);
    
    // 初始添加一行
    addPartRow();
});