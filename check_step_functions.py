#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
步骤功能检查脚本
全面检查步骤添加和删除功能的实现
"""

import os
import re
import sys

def check_function_definitions():
    """检查函数定义"""
    print("🔍 检查函数定义...")
    
    try:
        # 读取station_manager.js文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            manager_content = f.read()
        
        print("✅ 成功读取station_manager.js文件")
        
        # 检查核心函数定义
        required_functions = [
            ('insertProcessStep', r'function insertProcessStep\s*\('),
            ('insertProcessStepBefore', r'function insertProcessStepBefore\s*\('),
            ('insertProcessStepAfter', r'function insertProcessStepAfter\s*\('),
            ('deleteProcessStep', r'function deleteProcessStep\s*\('),
            ('addProcessStep', r'function addProcessStep\s*\('),
            ('regenerateProcessStation', r'function regenerateProcessStation\s*\(')
        ]
        
        missing_functions = []
        for func_name, pattern in required_functions:
            if re.search(pattern, manager_content):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
                missing_functions.append(func_name)
        
        return len(missing_functions) == 0, missing_functions
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, []

def check_function_implementations():
    """检查函数实现细节"""
    print("\n🔧 检查函数实现...")
    
    try:
        # 读取station_manager.js文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 1. 检查insertProcessStep函数实现
        print("\n1. 检查insertProcessStep函数...")
        insert_pattern = r'function insertProcessStep\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}'
        insert_match = re.search(insert_pattern, content, re.DOTALL)
        
        if insert_match:
            insert_body = insert_match.group(1)
            
            # 检查关键实现点
            checks = [
                ('数据验证', r'if\s*\(\s*!processStationsData\[stationIndex\]'),
                ('步骤数组初始化', r'process_steps\s*=\s*\[\]'),
                ('索引边界检查', r'Math\.max.*Math\.min'),
                ('步骤插入', r'splice\s*\(\s*insertIndex\s*,\s*0'),
                ('重新编号', r'forEach.*step_number.*index'),
                ('重新生成', r'regenerateProcessStation')
            ]
            
            for check_name, pattern in checks:
                if re.search(pattern, insert_body):
                    print(f"  ✅ {check_name}")
                else:
                    print(f"  ❌ {check_name}")
                    issues.append(f"insertProcessStep缺少{check_name}")
        else:
            print("  ❌ 无法找到insertProcessStep函数体")
            issues.append("insertProcessStep函数体不完整")
        
        # 2. 检查deleteProcessStep函数实现
        print("\n2. 检查deleteProcessStep函数...")
        delete_pattern = r'function deleteProcessStep\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}'
        delete_match = re.search(delete_pattern, content, re.DOTALL)
        
        if delete_match:
            delete_body = delete_match.group(1)
            
            checks = [
                ('数据验证', r'if\s*\([^)]*processStationsData\[stationIndex\]'),
                ('用户确认', r'confirm\s*\('),
                ('步骤删除', r'splice\s*\(\s*stepIndex\s*,\s*1'),
                ('重新编号', r'forEach.*step_number.*index'),
                ('重新生成', r'regenerateProcessStation')
            ]
            
            for check_name, pattern in checks:
                if re.search(pattern, delete_body):
                    print(f"  ✅ {check_name}")
                else:
                    print(f"  ❌ {check_name}")
                    issues.append(f"deleteProcessStep缺少{check_name}")
        else:
            print("  ❌ 无法找到deleteProcessStep函数体")
            issues.append("deleteProcessStep函数体不完整")
        
        # 3. 检查regenerateProcessStation函数
        print("\n3. 检查regenerateProcessStation函数...")
        regen_pattern = r'function regenerateProcessStation\s*\([^)]*\)\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}'
        regen_match = re.search(regen_pattern, content, re.DOTALL)
        
        if regen_match:
            regen_body = regen_match.group(1)
            
            checks = [
                ('DOM查询', r'document\.querySelector'),
                ('数据验证', r'processStationsData\[stationIndex\]'),
                ('HTML生成', r'createProcessStationHtml'),
                ('DOM更新', r'outerHTML')
            ]
            
            for check_name, pattern in checks:
                if re.search(pattern, regen_body):
                    print(f"  ✅ {check_name}")
                else:
                    print(f"  ❌ {check_name}")
                    issues.append(f"regenerateProcessStation缺少{check_name}")
        else:
            print("  ❌ 无法找到regenerateProcessStation函数体")
            issues.append("regenerateProcessStation函数体不完整")
        
        return len(issues) == 0, issues
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, [str(e)]

def check_html_integration():
    """检查HTML集成"""
    print("\n🌐 检查HTML集成...")
    
    try:
        # 读取station_generator.js文件
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            generator_content = f.read()
        
        issues = []
        
        # 检查HTML模板中的函数调用
        print("\n1. 检查HTML模板中的函数调用...")
        
        function_calls = [
            ('insertProcessStepBefore', r'onclick="insertProcessStepBefore\('),
            ('insertProcessStepAfter', r'onclick="insertProcessStepAfter\('),
            ('deleteProcessStep', r'onclick="deleteProcessStep\('),
            ('addProcessStep', r'onclick="addProcessStep\('),
            ('insertProcessStep', r'onclick="insertProcessStep\(')
        ]
        
        for func_name, pattern in function_calls:
            if re.search(pattern, generator_content):
                print(f"  ✅ {func_name} 在HTML中被调用")
            else:
                print(f"  ⚠️  {func_name} 在HTML中未找到调用")
        
        # 检查悬停菜单实现
        print("\n2. 检查悬停菜单实现...")
        
        menu_elements = [
            ('菜单容器', r'step-insert-menu'),
            ('菜单按钮', r'step-insert-btn'),
            ('下拉菜单', r'step-insert-dropdown'),
            ('鼠标进入事件', r'onmouseenter="showStepInsertMenu'),
            ('鼠标离开事件', r'onmouseleave="hideStepInsertMenu'),
            ('前插入选项', r'在此步骤前插入'),
            ('后插入选项', r'在此步骤后插入')
        ]
        
        for element_name, pattern in menu_elements:
            if re.search(pattern, generator_content):
                print(f"  ✅ {element_name}")
            else:
                print(f"  ❌ {element_name}")
                issues.append(f"悬停菜单缺少{element_name}")
        
        # 检查全局函数暴露
        print("\n3. 检查全局函数暴露...")
        
        global_functions = [
            ('showStepInsertMenu', r'window\.showStepInsertMenu'),
            ('hideStepInsertMenu', r'window\.hideStepInsertMenu'),
            ('hideAllStepInsertMenus', r'window\.hideAllStepInsertMenus')
        ]
        
        for func_name, pattern in global_functions:
            if re.search(pattern, generator_content):
                print(f"  ✅ {func_name} 已暴露到全局")
            else:
                print(f"  ❌ {func_name} 未暴露到全局")
                issues.append(f"{func_name}未暴露到全局作用域")
        
        return len(issues) == 0, issues
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, [str(e)]

def check_data_flow():
    """检查数据流"""
    print("\n📊 检查数据流...")
    
    try:
        # 读取station_manager.js文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 检查全局变量使用
        print("\n1. 检查全局变量...")
        
        if 'processStationsData' in content:
            print("  ✅ processStationsData 被使用")
        else:
            print("  ❌ processStationsData 未被使用")
            issues.append("processStationsData未被使用")
        
        if 'stationGenerator' in content:
            print("  ✅ stationGenerator 被使用")
        else:
            print("  ❌ stationGenerator 未被使用")
            issues.append("stationGenerator未被使用")
        
        # 检查错误处理
        print("\n2. 检查错误处理...")
        
        error_patterns = [
            ('数据验证', r'if\s*\(\s*!.*processStationsData'),
            ('边界检查', r'Math\.max.*Math\.min'),
            ('用户确认', r'confirm\s*\('),
            ('调试日志', r'console\.log')
        ]
        
        for check_name, pattern in error_patterns:
            if re.search(pattern, content):
                print(f"  ✅ {check_name}")
            else:
                print(f"  ⚠️  {check_name} 可能缺失")
        
        return len(issues) == 0, issues
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, [str(e)]

def generate_diagnostic_report(all_issues):
    """生成诊断报告"""
    print("\n📋 生成诊断报告...")
    
    report = f"""
# 步骤功能诊断报告

## 📊 检查结果总览

- 函数定义检查: {'✅ 通过' if not any('函数不存在' in issue for issue in all_issues) else '❌ 失败'}
- 函数实现检查: {'✅ 通过' if not any('缺少' in issue for issue in all_issues) else '❌ 失败'}
- HTML集成检查: {'✅ 通过' if not any('HTML' in issue or '暴露' in issue for issue in all_issues) else '❌ 失败'}
- 数据流检查: {'✅ 通过' if not any('数据' in issue for issue in all_issues) else '❌ 失败'}

## 🚨 发现的问题

"""
    
    if all_issues:
        for i, issue in enumerate(all_issues, 1):
            report += f"{i}. {issue}\n"
    else:
        report += "✅ 未发现问题，所有功能应该正常工作\n"
    
    report += """
## 🔧 建议的测试步骤

1. 打开 test_step_functionality.html 进行交互测试
2. 点击"初始化测试环境"按钮
3. 点击"运行所有测试"按钮
4. 观察测试结果和日志输出
5. 手动测试悬停菜单功能

## 🐛 如果功能仍不工作

1. 检查浏览器控制台是否有JavaScript错误
2. 确认所有JS文件正确加载
3. 验证processStationsData数据结构
4. 检查DOM元素是否正确生成

## 📁 相关文件

- static/js/station_manager.js - 核心功能实现
- static/js/station_generator.js - HTML生成和菜单
- test_step_functionality.html - 完整功能测试页面
"""
    
    with open('步骤功能诊断报告.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✅ 诊断报告已生成: 步骤功能诊断报告.md")

def main():
    """主检查函数"""
    print("🚀 开始检查步骤功能...")
    print("=" * 60)
    
    all_issues = []
    
    # 运行所有检查
    checks = [
        check_function_definitions,
        check_function_implementations,
        check_html_integration,
        check_data_flow
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        success, issues = check()
        if success:
            passed += 1
        all_issues.extend(issues)
        print()
    
    # 生成诊断报告
    generate_diagnostic_report(all_issues)
    
    # 输出最终结果
    print("=" * 60)
    print(f"📊 检查结果: {passed}/{total} 通过")
    print(f"🚨 发现问题: {len(all_issues)} 个")
    
    if len(all_issues) == 0:
        print("🎉 所有检查通过！步骤功能应该正常工作。")
        print("\n✅ 建议:")
        print("1. 打开 test_step_functionality.html 进行最终验证")
        print("2. 测试所有添加和删除功能")
        print("3. 检查悬停菜单是否正常工作")
        return True
    else:
        print("❌ 发现一些问题，可能影响功能正常工作。")
        print("\n🔧 建议:")
        print("1. 查看 步骤功能诊断报告.md 了解详细问题")
        print("2. 根据报告修复发现的问题")
        print("3. 重新运行检查确认修复")
        
        print("\n🚨 主要问题:")
        for issue in all_issues[:5]:  # 显示前5个问题
            print(f"  - {issue}")
        if len(all_issues) > 5:
            print(f"  ... 还有 {len(all_issues) - 5} 个问题")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
