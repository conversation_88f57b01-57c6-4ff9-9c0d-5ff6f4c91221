"""
生成完整的SAB A-F类数据
"""
import pandas as pd
import os

def create_sab_complete_data():
    """创建包含所有SAB类别的完整数据"""
    
    # SAB产品类别定义
    sab_categories = {
        'A': {
            'name': 'SAB-A-Pre-assembly',
            'description': '预装配工艺',
            'components': ['Inflator', 'Housing', 'Connector', 'Bracket'],
            'ct': 45,
            'stations': ['ST10', 'ST15', 'ST20', 'ST25'],
            'processes': ['预装配', '连接器安装', '支架固定', '质量检测']
        },
        'B': {
            'name': 'SAB-B-Main-assembly', 
            'description': '主装配工艺',
            'components': ['Module', 'Cover', 'Sensor', 'Wiring'],
            'ct': 60,
            'stations': ['ST10', 'ST15', 'ST20', 'ST25', 'ST30'],
            'processes': ['模块装配', '盖板安装', '传感器集成', '线束连接', '功能测试']
        },
        'C': {
            'name': 'SAB-C-Final-assembly',
            'description': '最终装配工艺', 
            'components': ['Assembly', 'Label', 'Package', 'Documentation'],
            'ct': 35,
            'stations': ['ST10', 'ST15', 'ST20'],
            'processes': ['最终装配', '标签贴附', '包装封装']
        },
        'D': {
            'name': 'SAB-D-Testing',
            'description': '测试验证工艺',
            'components': ['TestFixture', 'Sensor', 'DataLogger', 'Calibration'],
            'ct': 90,
            'stations': ['ST10', 'ST15', 'ST20', 'ST25', 'ST30', 'ST35'],
            'processes': ['功能测试', '性能验证', '数据记录', '校准检测', '质量确认', '报告生成']
        },
        'E': {
            'name': 'SAB-E-Packaging',
            'description': '包装工艺',
            'components': ['Box', 'Foam', 'Label', 'Manual'],
            'ct': 25,
            'stations': ['ST10', 'ST15'],
            'processes': ['产品包装', '标识贴附']
        },
        'F': {
            'name': 'SAB-F-Quality-control',
            'description': '质量控制工艺',
            'components': ['Gauge', 'Tester', 'Certificate', 'Report'],
            'ct': 40,
            'stations': ['ST10', 'ST15', 'ST20'],
            'processes': ['质量检测', '认证测试', '报告生成']
        }
    }
    
    # 创建list1工作表 - 工艺类型与零件对应关系
    list1_data = []
    all_components = set()
    
    for category, info in sab_categories.items():
        all_components.update(info['components'])
    
    all_components = sorted(list(all_components))
    
    for category, info in sab_categories.items():
        row = {
            'Product family': 'SAB',
            'Process type': category,
            'CT': info['ct']
        }
        
        # 为每个零件添加列
        for component in all_components:
            row[component] = component if component in info['components'] else ''
            
        list1_data.append(row)
    
    # 创建list2工作表 - 工艺流程信息
    list2_data = []
    
    for category, info in sab_categories.items():
        for station, process in zip(info['stations'], info['processes']):
            list2_data.append({
                'Process type': f'SAB-{category}',
                'station': station,
                'Process name': process
            })
    
    # 创建Excel文件
    with pd.ExcelWriter('Linespec-data-SAB-Complete.xlsx', engine='openpyxl') as writer:
        # 写入list1和list2
        pd.DataFrame(list1_data).to_excel(writer, sheet_name='list1', index=False)
        pd.DataFrame(list2_data).to_excel(writer, sheet_name='list2', index=False)
        
        # 为每个SAB类别创建详细工作表
        for category, info in sab_categories.items():
            create_sab_detail_sheet(writer, f'SAB-{category}', info)
    
    print("完整SAB数据文件已生成: Linespec-data-SAB-Complete.xlsx")

def create_sab_detail_sheet(writer, sheet_name, category_info):
    """为每个SAB类别创建详细工作表"""
    
    # 根据不同类别生成不同的详细数据
    detail_data = []
    
    if 'A' in sheet_name:  # SAB-A 预装配
        detail_data = [
            {
                'station': 'ST10',
                'Process type': 'Inflator安装',
                'process description': '将Inflator组件安装到Housing中',
                'man/machine': '人',
                '产品特性要求': '安装扭矩15-20Nm，无损伤',
                '防错要求': '扭矩监控，位置检测',
                '设备名': 'Inflator安装设备',
                '设备机械要求': '扭矩精度±2%，重复定位精度±0.1mm',
                '设备电气要求': '24V DC供电，扭矩传感器信号输出',
                '设备防错&点检要求': '每班次扭矩校准，传感器功能检查',
                '夹具名': 'Housing定位夹具',
                '夹具机械要求': '定位精度±0.05mm，夹紧力100-150N',
                '夹具电气要求': '气动控制，压力传感器监控',
                '夹具防错&点检要求': '每日气压检查，定位销磨损检查'
            },
            {
                'station': 'ST10',
                'process description': '检查Inflator安装质量',
                'man/machine': '设备',
                '产品特性要求': '安装深度符合图纸要求',
                '防错要求': '激光测距检测，不合格品自动剔除'
            },
            {
                'station': 'ST15',
                'Process type': 'Connector连接',
                'process description': '连接电气连接器',
                'man/machine': '人',
                '产品特性要求': '连接牢固，接触电阻<10mΩ',
                '防错要求': '插入力监控，电阻测试',
                '设备名': 'Connector测试设备',
                '设备机械要求': '插拔力测试范围0-100N',
                '设备电气要求': '电阻测试精度0.1mΩ',
                '设备防错&点检要求': '每周校准，探针清洁',
                '夹具名': 'Connector定位夹具',
                '夹具机械要求': '导向精度±0.02mm',
                '夹具电气要求': '无',
                '夹具防错&点检要求': '导向销润滑检查'
            }
        ]
    
    elif 'B' in sheet_name:  # SAB-B 主装配
        detail_data = [
            {
                'station': 'ST10',
                'Process type': 'Module装配',
                'process description': '装配主要模块组件',
                'man/machine': '设备',
                '产品特性要求': '装配精度±0.1mm，无异物',
                '防错要求': '视觉检测，力反馈监控',
                '设备名': 'Module装配机',
                '设备机械要求': '定位精度±0.05mm，装配力控制',
                '设备电气要求': '伺服控制系统，视觉系统',
                '设备防错&点检要求': '每日精度校准，视觉系统清洁',
                '夹具名': 'Module定位夹具',
                '夹具机械要求': '多点定位，夹紧力可调',
                '夹具电气要求': '位置传感器，夹紧监控',
                '夹具防错&点检要求': '定位精度检查，传感器校准'
            },
            {
                'station': 'ST15',
                'Process type': 'Cover安装',
                'process description': '安装保护盖板',
                'man/machine': '人',
                '产品特性要求': '密封性良好，螺钉扭矩规范',
                '防错要求': '扭矩监控，密封性测试'
            }
        ]
    
    elif 'C' in sheet_name:  # SAB-C 最终装配
        detail_data = [
            {
                'station': 'ST10',
                'Process type': '最终装配',
                'process description': '完成产品最终装配',
                'man/machine': '人',
                '产品特性要求': '外观无缺陷，功能正常',
                '防错要求': '外观检查，功能测试',
                '设备名': '最终测试设备',
                '设备机械要求': '测试夹具精度要求',
                '设备电气要求': '综合测试功能',
                '设备防错&点检要求': '测试程序验证',
                '夹具名': '最终装配夹具',
                '夹具机械要求': '通用性强，操作便利',
                '夹具电气要求': '测试接口完整',
                '夹具防错&点检要求': '接口导通检查'
            }
        ]
    
    elif 'D' in sheet_name:  # SAB-D 测试验证
        detail_data = [
            {
                'station': 'ST10',
                'Process type': '功能测试',
                'process description': '执行产品功能测试',
                'man/machine': '设备',
                '产品特性要求': '所有功能参数符合规范',
                '防错要求': '自动测试，数据记录',
                '设备名': '综合测试台',
                '设备机械要求': '测试精度高，稳定性好',
                '设备电气要求': '多通道测试，数据采集',
                '设备防错&点检要求': '定期校准，软件更新',
                '夹具名': '测试夹具',
                '夹具机械要求': '快速装夹，重复性好',
                '夹具电气要求': '信号传输可靠',
                '夹具防错&点检要求': '接触电阻检查'
            }
        ]
    
    elif 'E' in sheet_name:  # SAB-E 包装
        detail_data = [
            {
                'station': 'ST10',
                'Process type': '产品包装',
                'process description': '将产品装入包装盒',
                'man/machine': '人',
                '产品特性要求': '包装完整，标识清晰',
                '防错要求': '条码扫描，重量检查',
                '设备名': '包装检重设备',
                '设备机械要求': '称重精度±1g',
                '设备电气要求': '条码扫描器，显示屏',
                '设备防错&点检要求': '称重校准，扫描器清洁',
                '夹具名': '包装辅助夹具',
                '夹具机械要求': '操作便利，防护到位',
                '夹具电气要求': '无',
                '夹具防错&点检要求': '外观检查'
            }
        ]
    
    elif 'F' in sheet_name:  # SAB-F 质量控制
        detail_data = [
            {
                'station': 'ST10',
                'Process type': '质量检测',
                'process description': '执行质量控制检测',
                'man/machine': '设备',
                '产品特性要求': '所有质量指标合格',
                '防错要求': '自动检测，不合格品隔离',
                '设备名': '质量检测设备',
                '设备机械要求': '检测精度符合要求',
                '设备电气要求': '多参数检测能力',
                '设备防错&点检要求': '检测精度验证',
                '夹具名': '检测夹具',
                '夹具机械要求': '定位准确，操作安全',
                '夹具电气要求': '检测信号传输',
                '夹具防错&点检要求': '信号完整性检查'
            }
        ]
    
    # 写入工作表
    pd.DataFrame(detail_data).to_excel(writer, sheet_name=sheet_name, index=False)

if __name__ == "__main__":
    create_sab_complete_data()
