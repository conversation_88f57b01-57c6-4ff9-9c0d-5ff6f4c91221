"""
测试产品附件上传功能
"""
import base64
import json
from PIL import Image, ImageDraw, ImageFont
import io

def create_test_images():
    """创建测试用的图片文件"""
    
    # 创建测试图片的配置
    test_images = {
        'explosion_diagram.png': {
            'title': '产品爆炸图',
            'description': '展示产品各部件分解状态',
            'color': '#FF6B6B'
        },
        'folding_diagram.png': {
            'title': '产品折叠图', 
            'description': '展示产品折叠状态',
            'color': '#4ECDC4'
        },
        'assembly_diagram.png': {
            'title': '产品总成图',
            'description': '展示产品完整装配状态',
            'color': '#45B7D1'
        }
    }
    
    for filename, config in test_images.items():
        # 创建图片
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制背景色块
        draw.rectangle([50, 50, 750, 550], fill=config['color'], outline='black', width=3)
        
        # 绘制标题
        try:
            # 尝试使用系统字体
            font_title = ImageFont.truetype("arial.ttf", 48)
            font_desc = ImageFont.truetype("arial.ttf", 24)
        except:
            # 如果没有找到字体，使用默认字体
            font_title = ImageFont.load_default()
            font_desc = ImageFont.load_default()
        
        # 计算文本位置
        title_bbox = draw.textbbox((0, 0), config['title'], font=font_title)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (800 - title_width) // 2
        
        desc_bbox = draw.textbbox((0, 0), config['description'], font=font_desc)
        desc_width = desc_bbox[2] - desc_bbox[0]
        desc_x = (800 - desc_width) // 2
        
        # 绘制文本
        draw.text((title_x, 200), config['title'], fill='white', font=font_title)
        draw.text((desc_x, 280), config['description'], fill='white', font=font_desc)
        
        # 绘制一些装饰性元素
        for i in range(5):
            x = 100 + i * 120
            y = 350
            draw.ellipse([x, y, x+80, y+80], fill='white', outline='black', width=2)
            draw.text((x+25, y+25), f'P{i+1}', fill='black', font=font_desc)
        
        # 保存图片
        img.save(filename)
        print(f"✅ 创建测试图片: {filename}")

def generate_test_data():
    """生成测试用的附件数据"""
    
    # 创建测试图片
    create_test_images()
    
    # 生成测试数据
    test_attachments = {}
    
    image_files = [
        ('explosion_diagram.png', 'explosion-diagram', '这是产品爆炸图，展示了所有零部件的分解状态和装配关系。'),
        ('folding_diagram.png', 'folding-diagram', '这是产品折叠图，展示了产品在折叠状态下的结构和尺寸。'),
        ('assembly_diagram.png', 'assembly-diagram', '这是产品总成图，展示了产品完整装配后的最终状态。')
    ]
    
    for filename, attachment_type, notes in image_files:
        try:
            # 读取图片文件
            with open(filename, 'rb') as f:
                image_data = f.read()
            
            # 转换为base64
            base64_data = base64.b64encode(image_data).decode('utf-8')
            data_url = f"data:image/png;base64,{base64_data}"
            
            # 创建附件数据
            test_attachments[attachment_type] = {
                'file': {
                    'name': filename,
                    'type': 'image/png',
                    'size': len(image_data),
                    'data': data_url,
                    'uploadTime': '2025-06-27T08:30:00.000Z'
                },
                'notes': notes
            }
            
            print(f"✅ 生成附件数据: {attachment_type}")
            
        except Exception as e:
            print(f"❌ 生成附件数据失败 {filename}: {str(e)}")
    
    # 保存测试数据到JSON文件
    with open('test_attachments_data.json', 'w', encoding='utf-8') as f:
        json.dump(test_attachments, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试数据已保存到: test_attachments_data.json")
    
    return test_attachments

def generate_javascript_test():
    """生成JavaScript测试代码"""
    
    js_test_code = '''
// 产品附件功能测试代码
// 在浏览器控制台中运行此代码来测试附件功能

console.log("开始测试产品附件功能...");

// 测试数据
const testAttachments = {
    "explosion-diagram": {
        "file": {
            "name": "explosion_diagram.png",
            "type": "image/png",
            "size": 15234,
            "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "uploadTime": "2025-06-27T08:30:00.000Z"
        },
        "notes": "这是产品爆炸图的测试数据"
    },
    "folding-diagram": {
        "file": {
            "name": "folding_diagram.png", 
            "type": "image/png",
            "size": 12456,
            "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "uploadTime": "2025-06-27T08:30:00.000Z"
        },
        "notes": "这是产品折叠图的测试数据"
    }
};

// 测试函数
function testProductAttachments() {
    console.log("1. 测试设置附件数据...");
    
    if (typeof setAllAttachments === 'function') {
        setAllAttachments(testAttachments);
        console.log("✅ 附件数据设置成功");
    } else {
        console.log("❌ setAllAttachments 函数不存在");
    }
    
    console.log("2. 测试获取附件数据...");
    
    if (typeof getAllAttachments === 'function') {
        const attachments = getAllAttachments();
        console.log("✅ 获取到附件数据:", attachments);
    } else {
        console.log("❌ getAllAttachments 函数不存在");
    }
    
    console.log("3. 测试导出附件数据...");
    
    if (typeof exportAttachmentsData === 'function') {
        const exportData = exportAttachmentsData();
        console.log("✅ 导出数据成功:", exportData);
    } else {
        console.log("❌ exportAttachmentsData 函数不存在");
    }
    
    console.log("4. 测试文件大小格式化...");
    
    if (typeof formatFileSize === 'function') {
        console.log("✅ 1024 bytes =", formatFileSize(1024));
        console.log("✅ 1048576 bytes =", formatFileSize(1048576));
    } else {
        console.log("❌ formatFileSize 函数不存在");
    }
}

// 运行测试
testProductAttachments();

console.log("产品附件功能测试完成！");
'''
    
    with open('test_attachments.js', 'w', encoding='utf-8') as f:
        f.write(js_test_code)
    
    print("✅ JavaScript测试代码已保存到: test_attachments.js")

def main():
    """主函数"""
    print("=== 产品附件功能测试 ===\\n")
    
    try:
        # 生成测试数据
        test_data = generate_test_data()
        
        # 生成JavaScript测试代码
        generate_javascript_test()
        
        print("\\n=== 测试完成 ===")
        print("📁 生成的文件:")
        print("  - explosion_diagram.png (产品爆炸图)")
        print("  - folding_diagram.png (产品折叠图)")
        print("  - assembly_diagram.png (产品总成图)")
        print("  - test_attachments_data.json (测试数据)")
        print("  - test_attachments.js (JavaScript测试代码)")
        
        print("\\n🔧 使用方法:")
        print("1. 打开浏览器访问 http://localhost:5000")
        print("2. 切换到工艺页面")
        print("3. 在产品附件区域上传测试图片")
        print("4. 在浏览器控制台运行 test_attachments.js 中的代码")
        print("5. 测试导出功能是否包含附件数据")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
