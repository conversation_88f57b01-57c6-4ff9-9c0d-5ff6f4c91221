/**
 * 智能匹配功能
 */

// 智能匹配相关的DOM元素
let smartMatchElements = {};
let analysisHistory = [];
let allStationsExpanded = false;
let currentAnalysisResult = null;

// 预设配置
const presetConfigurations = {
    'sab-basic': {
        productFamily: 'SAB',
        components: ['Deflector', 'inflator', 'cushion', 'soft cover'],
        name: 'SAB基础型'
    },
    'sab-harness': {
        productFamily: 'SAB',
        components: ['Deflector', 'inflator', 'cushion', 'Harness', 'soft cover'],
        name: 'SAB带线束'
    },
    'sab-full': {
        productFamily: 'SAB',
        components: ['Deflector', 'inflator', 'cushion', 'Harness', 'soft cover'],
        name: 'SAB完整型'
    }
};

// 初始化智能匹配功能
function initSmartMatch() {
    smartMatchElements = {
        productFamily: document.getElementById('smart-product-family'),
        analyzeBtn: document.getElementById('analyze-btn'),
        projectRequirements: document.getElementById('project-requirements'),
        matchResultSection: document.getElementById('match-result-section'),
        matchResultContent: document.getElementById('match-result-content'),
        processFlowSection: document.getElementById('process-flow-section'),
        processFlowContent: document.getElementById('process-flow-content'),
        stationDetailsSection: document.getElementById('station-details-section'),
        stationDetailsContent: document.getElementById('station-details-content'),
        loadingOverlay: document.getElementById('loading-overlay'),
        loadingText: document.getElementById('loading-text'),
        statsGrid: document.getElementById('stats-grid'),
        historySection: document.getElementById('history-section'),
        historyList: document.getElementById('history-list')
    };

    // 绑定事件
    if (smartMatchElements.analyzeBtn) {
        smartMatchElements.analyzeBtn.addEventListener('click', performSmartAnalysis);
    }

    // 绑定快速选择按钮
    bindQuickSelectButtons();

    // 加载历史记录
    loadAnalysisHistory();
}

// 绑定快速选择按钮
function bindQuickSelectButtons() {
    const quickSelectBtns = document.querySelectorAll('.quick-select-btn');
    quickSelectBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const preset = this.dataset.preset;
            if (preset === 'custom') {
                clearAllSelections();
            } else if (presetConfigurations[preset]) {
                applyPresetConfiguration(presetConfigurations[preset]);
            }
        });
    });
}

// 应用预设配置
function applyPresetConfiguration(config) {
    // 设置产品族
    if (smartMatchElements.productFamily) {
        smartMatchElements.productFamily.value = config.productFamily;
    }

    // 清除所有选择
    clearAllSelections();

    // 选择对应的零件
    config.components.forEach(component => {
        const checkbox = document.querySelector(`input[value="${component}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });

    // 显示提示
    showToast(`已应用预设配置：${config.name}`);
}

// 清除所有选择
function clearAllSelections() {
    const checkboxes = document.querySelectorAll('.component-item input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);
}

// 获取选中的零件
function getSelectedComponents() {
    const checkboxes = document.querySelectorAll('.component-item input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 显示提示消息
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #1a73e8;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        z-index: 10000;
        font-size: 0.9rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        animation: slideInRight 0.3s ease;
    `;
    toast.textContent = message;

    // 添加到页面
    document.body.appendChild(toast);

    // 3秒后移除
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 执行智能分析
async function performSmartAnalysis() {
    try {
        const productFamily = smartMatchElements.productFamily.value;
        const components = getSelectedComponents();
        const projectRequirements = smartMatchElements.projectRequirements.value;

        if (!productFamily) {
            showToast('请选择产品族');
            return;
        }

        if (components.length === 0) {
            showToast('请至少选择一个零件');
            return;
        }

        // 显示加载遮罩
        showLoadingOverlay('正在智能分析中...');

        // 发送分析请求
        const response = await fetch('/analyze_linespec', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_family: productFamily,
                components: components,
                project_requirements: projectRequirements
            })
        });

        const result = await response.json();

        if (response.ok) {
            // 保存到历史记录
            saveToHistory({
                productFamily,
                components,
                projectRequirements,
                result,
                timestamp: new Date()
            });

            displayAnalysisResult(result);
        } else {
            throw new Error(result.error || '分析失败');
        }

    } catch (error) {
        console.error('智能分析失败:', error);
        showToast('分析失败: ' + error.message);
    } finally {
        // 隐藏加载遮罩
        hideLoadingOverlay();
    }
}

// 显示/隐藏加载遮罩
function showLoadingOverlay(text = '加载中...') {
    if (smartMatchElements.loadingOverlay) {
        smartMatchElements.loadingText.textContent = text;
        smartMatchElements.loadingOverlay.style.display = 'flex';
    }
}

function hideLoadingOverlay() {
    if (smartMatchElements.loadingOverlay) {
        smartMatchElements.loadingOverlay.style.display = 'none';
    }
}

// 历史记录管理
function saveToHistory(analysisData) {
    analysisHistory.unshift(analysisData);

    // 限制历史记录数量
    if (analysisHistory.length > 10) {
        analysisHistory = analysisHistory.slice(0, 10);
    }

    // 保存到localStorage
    localStorage.setItem('smartMatchHistory', JSON.stringify(analysisHistory));

    // 更新历史记录显示
    updateHistoryDisplay();
}

function loadAnalysisHistory() {
    const saved = localStorage.getItem('smartMatchHistory');
    if (saved) {
        try {
            analysisHistory = JSON.parse(saved);
            updateHistoryDisplay();
        } catch (e) {
            console.error('加载历史记录失败:', e);
        }
    }
}

function updateHistoryDisplay() {
    if (!smartMatchElements.historyList || analysisHistory.length === 0) {
        if (smartMatchElements.historySection) {
            smartMatchElements.historySection.style.display = 'none';
        }
        return;
    }

    smartMatchElements.historySection.style.display = 'block';

    const html = analysisHistory.map((item, index) => `
        <div class="history-item" onclick="loadHistoryItem(${index})">
            <div class="history-info">
                <div class="history-title">
                    ${item.productFamily} - ${item.components.join(', ')}
                </div>
                <div class="history-details">
                    ${item.result.matchResult?.processType || '未知类型'} |
                    ${new Date(item.timestamp).toLocaleString()}
                </div>
            </div>
            <div class="history-actions">
                <button class="history-btn" onclick="event.stopPropagation(); loadHistoryItem(${index})">加载</button>
                <button class="history-btn" onclick="event.stopPropagation(); deleteHistoryItem(${index})">删除</button>
            </div>
        </div>
    `).join('');

    smartMatchElements.historyList.innerHTML = html;
}

function loadHistoryItem(index) {
    const item = analysisHistory[index];
    if (!item) return;

    // 恢复输入状态
    smartMatchElements.productFamily.value = item.productFamily;
    smartMatchElements.projectRequirements.value = item.projectRequirements || '';

    // 恢复零件选择
    clearAllSelections();
    item.components.forEach(component => {
        const checkbox = document.querySelector(`input[value="${component}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });

    // 显示结果
    displayAnalysisResult(item.result);

    showToast('已加载历史记录');
}

function deleteHistoryItem(index) {
    analysisHistory.splice(index, 1);
    localStorage.setItem('smartMatchHistory', JSON.stringify(analysisHistory));
    updateHistoryDisplay();
    showToast('已删除历史记录');
}

// 显示分析结果
function displayAnalysisResult(result) {
    if (result.error) {
        displayError(result.error, result.suggestions);
        return;
    }

    // 保存当前分析结果
    currentAnalysisResult = result;

    // 显示统计卡片
    displayStatsCards(result.matchResult);

    // 显示匹配结果
    displayMatchResult(result.matchResult);

    // 显示工艺流程
    displayProcessFlow(result.processFlow, result.mermaidDiagram);

    // 显示工站详情
    displayStationDetails(result.stationDetails);
}

// 显示统计卡片
function displayStatsCards(matchResult) {
    const html = `
        <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-value">${matchResult.confidence}%</div>
            <div class="stat-label">匹配置信度</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">⏱️</div>
            <div class="stat-value">${matchResult.estimatedCT}</div>
            <div class="stat-label">预计CT时间(分钟)</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">🏭</div>
            <div class="stat-value">${matchResult.stations.length}</div>
            <div class="stat-label">工站数量</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">📦</div>
            <div class="stat-value">${matchResult.components.length}</div>
            <div class="stat-label">零件数量</div>
        </div>
    `;

    smartMatchElements.statsGrid.innerHTML = html;
}

// 显示匹配结果
function displayMatchResult(matchResult) {
    const html = `
        <div class="match-result-card">
            <div class="match-result-header">
                <div class="match-result-title">
                    🎯 匹配工艺类型: ${matchResult.processType}
                </div>
                <div class="confidence-badge">
                    置信度: ${matchResult.confidence}%
                </div>
            </div>
            <div class="match-result-details">
                <div class="detail-item">
                    <span class="detail-icon">📦</span>
                    <span>零件组合: ${matchResult.components.join(', ')}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-icon">⏱️</span>
                    <span>预计CT时间: ${matchResult.estimatedCT}分钟</span>
                </div>
                <div class="detail-item">
                    <span class="detail-icon">🏭</span>
                    <span>工站数量: ${matchResult.stations.length}个</span>
                </div>
                <div class="detail-item">
                    <span class="detail-icon">🔧</span>
                    <span>工站配置: ${matchResult.stations.join(' → ')}</span>
                </div>
            </div>
        </div>
    `;

    smartMatchElements.matchResultContent.innerHTML = html;
    smartMatchElements.matchResultSection.style.display = 'block';
}

// 显示工艺流程
function displayProcessFlow(processFlow, mermaidDiagram) {
    let html = '<div class="stations-flow">';

    processFlow.stations.forEach((station, index) => {
        if (index > 0) {
            html += '<div class="flow-arrow">→</div>';
        }

        html += `
            <div class="station-node">
                <div class="station-node-id">${station}</div>
                <div class="station-node-name">${processFlow.processes[index] || ''}</div>
            </div>
        `;
    });

    html += '</div>';

    // 添加Mermaid流程图
    if (mermaidDiagram) {
        const mermaidId = 'mermaid-' + Date.now();
        html += `
            <div style="margin-top: 1.5rem;">
                <h5 style="color: #1a73e8; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                    📊 工艺流程图
                    <button onclick="toggleMermaidCode()" style="padding: 0.3rem 0.6rem; font-size: 0.8rem; background: #e8f0fe; border: 1px solid #1a73e8; color: #1a73e8; border-radius: 4px; cursor: pointer;">
                        查看代码
                    </button>
                </h5>
                <div id="${mermaidId}" class="mermaid-diagram" style="background: white; padding: 1rem; border-radius: 8px; border: 1px solid #e0e0e0; text-align: center;">
                    ${mermaidDiagram}
                </div>
                <div id="mermaid-code" style="display: none; margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                    <h6 style="color: #1a73e8; margin-bottom: 0.5rem;">Mermaid代码：</h6>
                    <pre style="background: white; padding: 1rem; border-radius: 4px; overflow-x: auto; font-size: 0.85rem; margin: 0;"><code>${mermaidDiagram}</code></pre>
                </div>
            </div>
        `;
    }

    smartMatchElements.processFlowContent.innerHTML = html;
    smartMatchElements.processFlowSection.style.display = 'block';

    // 渲染Mermaid图表
    if (mermaidDiagram && typeof mermaid !== 'undefined') {
        setTimeout(() => {
            mermaid.init();
        }, 100);
    }
}

// 显示工站详情
function displayStationDetails(stationDetails) {
    let html = '';

    Object.keys(stationDetails).forEach((stationId, index) => {
        const station = stationDetails[stationId];
        const isExpanded = index === 0; // 默认展开第一个工站

        html += `
            <div class="collapsible-section">
                <div class="collapsible-header" onclick="toggleStationDetails('${stationId}')">
                    <h5>🏭 ${stationId} - ${station.processName}</h5>
                    <span class="collapsible-toggle" id="toggle-${stationId}">${isExpanded ? '▲' : '▼'}</span>
                </div>
                <div class="collapsible-content ${isExpanded ? 'expanded' : ''}" id="content-${stationId}">
                    ${generateStationSteps(station.steps)}
                    ${generateEquipmentInfo(station.equipment)}
                    ${generateFixturesInfo(station.fixtures)}
                </div>
            </div>
        `;
    });

    smartMatchElements.stationDetailsContent.innerHTML = html;
    smartMatchElements.stationDetailsSection.style.display = 'block';
}

// 生成工艺步骤HTML
function generateStationSteps(steps) {
    if (!steps || steps.length === 0) return '';

    const sectionId = 'steps-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

    let html = `
        <div class="detail-section">
            <div class="collapsible-header" onclick="toggleSection('${sectionId}')">
                <h5>🔄 工艺步骤 (${steps.length}个)</h5>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content expanded" id="${sectionId}">
    `;

    steps.forEach((step, index) => {
        // 根据操作方式设置不同的图标和颜色
        const operatorIcon = step.operator === '人' ? '👤' : step.operator === '设备' ? '🤖' : '⚙️';
        const operatorClass = step.operator === '人' ? 'manual-step' : 'auto-step';

        html += `
            <div class="step-item ${operatorClass}">
                <div class="step-header">
                    <div class="step-number">${step.stepNumber}</div>
                    <div class="step-title">
                        <span class="operator-icon">${operatorIcon}</span>
                        ${step.description}
                    </div>
                </div>
                <div class="step-details">
                    ${step.operator ? `
                        <div class="detail-tag operator-tag">
                            <span class="tag-icon">👤</span>
                            <span class="tag-label">操作方式:</span>
                            <span class="tag-value">${step.operator}</span>
                        </div>
                    ` : ''}
                    ${step.qualityRequirements ? `
                        <div class="detail-tag quality-tag">
                            <span class="tag-icon">✅</span>
                            <span class="tag-label">质量要求:</span>
                            <span class="tag-value">${step.qualityRequirements}</span>
                        </div>
                    ` : ''}
                    ${step.errorProofing ? `
                        <div class="detail-tag error-proofing-tag">
                            <span class="tag-icon">🛡️</span>
                            <span class="tag-label">防错措施:</span>
                            <span class="tag-value">${step.errorProofing}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    });

    html += '</div></div>';
    return html;
}

// 生成设备信息HTML
function generateEquipmentInfo(equipment) {
    if (!equipment || !equipment.name) return '';

    const sectionId = 'equipment-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

    return `
        <div class="detail-section">
            <div class="collapsible-header" onclick="toggleSection('${sectionId}')">
                <h5>🔧 设备要求 - ${equipment.name}</h5>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content" id="${sectionId}">
                <div class="equipment-info">
                    <div class="equipment-name-card">
                        <h6>📋 设备名称</h6>
                        <p>${equipment.name}</p>
                    </div>

                    ${equipment.mechanicalRequirements ? `
                        <div class="requirement-card mechanical-req">
                            <div class="req-header">
                                <span class="req-icon">⚙️</span>
                                <h6>机械要求</h6>
                            </div>
                            <div class="req-content">
                                ${formatRequirementText(equipment.mechanicalRequirements)}
                            </div>
                        </div>
                    ` : ''}

                    ${equipment.electricalRequirements ? `
                        <div class="requirement-card electrical-req">
                            <div class="req-header">
                                <span class="req-icon">⚡</span>
                                <h6>电气要求</h6>
                            </div>
                            <div class="req-content">
                                ${formatRequirementText(equipment.electricalRequirements)}
                            </div>
                        </div>
                    ` : ''}

                    ${equipment.errorProofingRequirements ? `
                        <div class="requirement-card error-proofing-req">
                            <div class="req-header">
                                <span class="req-icon">🛡️</span>
                                <h6>防错要求</h6>
                            </div>
                            <div class="req-content">
                                ${formatRequirementText(equipment.errorProofingRequirements)}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// 生成夹具信息HTML
function generateFixturesInfo(fixtures) {
    if (!fixtures || fixtures.length === 0) return '';

    const sectionId = 'fixtures-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

    let html = `
        <div class="detail-section">
            <div class="collapsible-header" onclick="toggleSection('${sectionId}')">
                <h5>🔩 夹具要求 (${fixtures.length}个)</h5>
                <span class="collapsible-toggle">▼</span>
            </div>
            <div class="collapsible-content" id="${sectionId}">
    `;

    fixtures.forEach((fixture, index) => {
        html += `
            <div class="fixture-info">
                <div class="fixture-header">
                    <span class="fixture-number">${index + 1}</span>
                    <h6>📋 ${fixture.name}</h6>
                </div>

                ${fixture.mechanicalRequirements ? `
                    <div class="requirement-card mechanical-req">
                        <div class="req-header">
                            <span class="req-icon">⚙️</span>
                            <h6>机械要求</h6>
                        </div>
                        <div class="req-content">
                            ${formatRequirementText(fixture.mechanicalRequirements)}
                        </div>
                    </div>
                ` : ''}

                ${fixture.electricalRequirements ? `
                    <div class="requirement-card electrical-req">
                        <div class="req-header">
                            <span class="req-icon">⚡</span>
                            <h6>电气要求</h6>
                        </div>
                        <div class="req-content">
                            ${formatRequirementText(fixture.electricalRequirements)}
                        </div>
                    </div>
                ` : ''}

                ${fixture.errorProofingRequirements ? `
                    <div class="requirement-card error-proofing-req">
                        <div class="req-header">
                            <span class="req-icon">🛡️</span>
                            <h6>防错要求</h6>
                        </div>
                        <div class="req-content">
                            ${formatRequirementText(fixture.errorProofingRequirements)}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    });

    html += '</div></div>';
    return html;
}

// 显示错误信息
function displayError(error, suggestions) {
    let html = `
        <div style="background: #fff3e0; border: 1px solid #ff9800; border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
            <h4 style="color: #f57c00; margin-bottom: 0.5rem;">⚠️ 分析失败</h4>
            <p style="margin-bottom: 1rem;">${error}</p>
    `;
    
    if (suggestions && suggestions.length > 0) {
        html += `
            <p><strong>建议的工艺类型:</strong></p>
            <ul>
                ${suggestions.map(s => `<li>${s}</li>`).join('')}
            </ul>
        `;
    }
    
    html += '</div>';
    
    smartMatchElements.matchResultContent.innerHTML = html;
    smartMatchElements.matchResultSection.style.display = 'block';
    
    // 隐藏其他区域
    smartMatchElements.processFlowSection.style.display = 'none';
    smartMatchElements.stationDetailsSection.style.display = 'none';
}

// 格式化要求文本
function formatRequirementText(text) {
    if (!text) return '';

    // 将长文本按句号分割成列表
    const sentences = text.split(/[。；]/);
    if (sentences.length <= 1) {
        return `<p>${text}</p>`;
    }

    let html = '<ul class="requirement-list">';
    sentences.forEach(sentence => {
        const trimmed = sentence.trim();
        if (trimmed) {
            // 检查是否包含数字编号
            if (/^\d+\./.test(trimmed)) {
                html += `<li class="numbered-item">${trimmed}</li>`;
            } else {
                html += `<li>${trimmed}</li>`;
            }
        }
    });
    html += '</ul>';

    return html;
}

// 通用折叠展开功能
function toggleSection(sectionId) {
    console.log('Toggling section:', sectionId); // 调试日志

    const content = document.getElementById(sectionId);
    if (!content) {
        console.error('Content element not found:', sectionId);
        return;
    }

    const header = content.previousElementSibling;
    const toggle = header?.querySelector('.collapsible-toggle');

    if (!toggle) {
        console.error('Toggle element not found for:', sectionId);
        return;
    }

    const isExpanded = content.classList.contains('expanded');
    console.log('Current state expanded:', isExpanded);

    if (isExpanded) {
        content.classList.remove('expanded');
        toggle.textContent = '▼';
        console.log('Collapsed section:', sectionId);
    } else {
        content.classList.add('expanded');
        toggle.textContent = '▲';
        console.log('Expanded section:', sectionId);
    }
}

// 工站详情折叠展开功能
function toggleStationDetails(stationId) {
    console.log('Toggling station details:', stationId); // 调试日志

    const content = document.getElementById(`content-${stationId}`);
    const toggle = document.getElementById(`toggle-${stationId}`);

    if (!content) {
        console.error('Station content not found:', `content-${stationId}`);
        return;
    }

    if (!toggle) {
        console.error('Station toggle not found:', `toggle-${stationId}`);
        return;
    }

    const isExpanded = content.classList.contains('expanded');
    console.log('Station current state expanded:', isExpanded);

    if (isExpanded) {
        content.classList.remove('expanded');
        toggle.textContent = '▼';
        console.log('Collapsed station:', stationId);
    } else {
        content.classList.add('expanded');
        toggle.textContent = '▲';
        console.log('Expanded station:', stationId);
    }
}

// Mermaid代码显示切换
function toggleMermaidCode() {
    const codeDiv = document.getElementById('mermaid-code');
    if (codeDiv) {
        codeDiv.style.display = codeDiv.style.display === 'none' ? 'block' : 'none';
    }
}

// 应用到工艺页面
function applyToProcessPage() {
    if (!currentAnalysisResult) {
        showToast('请先进行智能分析');
        return;
    }

    try {
        // 显示加载状态
        showLoadingOverlay('正在生成工艺页面...');

        // 清空现有工站（静默清空，不显示确认对话框）
        clearStationsQuietly();

        // 生成工站数据
        const stationData = generateStationData(currentAnalysisResult);

        // 应用工站数据到工艺页面
        applyStationData(stationData);

        // 切换到工艺页面
        switchToProcessPage();

        showToast('已成功应用到工艺页面！');

    } catch (error) {
        console.error('应用到工艺页面失败:', error);
        showToast('应用失败: ' + error.message);
    } finally {
        hideLoadingOverlay();
    }
}

// 生成工站数据
function generateStationData(analysisResult) {
    const { matchResult, stationDetails } = analysisResult;
    const stations = [];

    Object.keys(stationDetails).forEach((stationId, index) => {
        const station = stationDetails[stationId];

        // 转换步骤格式以兼容现有系统
        const processSteps = station.steps ? station.steps.map((step, stepIndex) => ({
            step_number: step.stepNumber || stepIndex + 1,
            step_description: step.description || '',
            operator: step.operator || '',
            quality_requirements: step.qualityRequirements || '',
            error_proofing: step.errorProofing || ''
        })) : [];

        // 生成工站基本信息（兼容现有格式）
        const stationData = {
            station_number: index + 1,
            station_name: station.processName || `${stationId}工站`,
            station_description: `${matchResult.processType} - ${station.processName}`,
            process_steps: processSteps,
            equipment: station.equipment || {},
            fixtures: station.fixtures || [],
            processType: matchResult.processType,
            originalId: stationId
        };

        stations.push(stationData);
    });

    return stations;
}

// 应用工站数据到工艺页面
function applyStationData(stationData) {
    // 更新全局工站数据
    if (typeof processStationsData !== 'undefined') {
        processStationsData = stationData;
    }

    // 使用现有的StationGenerator生成工站（清空现有内容，因为这是AI生成的新内容）
    if (typeof StationGenerator !== 'undefined') {
        const generator = new StationGenerator();
        generator.generateProcessStations(stationData, false); // false表示清空现有内容
    } else {
        // 备用方案：直接生成HTML
        const stationsList = document.getElementById('stations-list');
        if (!stationsList) {
            throw new Error('找不到工站列表容器');
        }

        stationsList.innerHTML = '';
        stationData.forEach((station, index) => {
            const stationHtml = generateProcessStationHtml(station, index);
            stationsList.insertAdjacentHTML('beforeend', stationHtml);
        });
    }
}

// 生成工艺工站HTML（备用方案）
function generateProcessStationHtml(station, index) {
    const stepsHtml = station.process_steps ? station.process_steps.map((step, stepIndex) => `
        <div class="process-step">
            <div class="step-header">
                <span class="step-number">${step.step_number || stepIndex + 1}</span>
                <span class="step-title">${step.step_description}</span>
            </div>
            <div class="step-details">
                ${step.operator ? `<div class="step-detail"><strong>操作方式:</strong> ${step.operator}</div>` : ''}
                ${step.quality_requirements ? `<div class="step-detail"><strong>质量要求:</strong> ${step.quality_requirements}</div>` : ''}
                ${step.error_proofing ? `<div class="step-detail"><strong>防错措施:</strong> ${step.error_proofing}</div>` : ''}
            </div>
        </div>
    `).join('') : '';

    return `
        <div class="station-block compact" data-station-index="${index}">
            <div class="station-header">
                <div class="station-title">
                    <span class="station-icon">🏭</span>
                    <span class="station-name">ST${station.station_number} - ${station.station_name}</span>
                    <span class="station-badge">${station.processType}</span>
                </div>
            </div>
            <div class="station-content">
                <div class="process-steps-section">
                    <h5>工艺步骤</h5>
                    <div class="steps-container">
                        ${stepsHtml}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// 静默清空工站（不显示确认对话框）
function clearStationsQuietly() {
    // 清空全局数据
    if (typeof processStationsData !== 'undefined') {
        processStationsData = [];
    }
    if (typeof equipmentStationsData !== 'undefined') {
        equipmentStationsData = [];
    }

    // 清空容器
    const processContainer = document.getElementById('stations-list');
    const equipmentContainer = document.getElementById('equipment-stations-list');

    if (processContainer) processContainer.innerHTML = '';
    if (equipmentContainer) equipmentContainer.innerHTML = '';

    console.log('已静默清空所有工站数据');
}

// 切换到工艺页面
function switchToProcessPage() {
    // 找到工艺页面的菜单项并点击
    const menuItems = document.querySelectorAll('.menu-item');
    const processMenuItem = Array.from(menuItems).find(item =>
        item.textContent.includes('工艺要求') || item.dataset.page === 'process'
    );

    if (processMenuItem) {
        processMenuItem.click();
    }
}

// ===== 导出功能 =====

// 导出为PDF
async function exportToPDF() {
    if (!currentAnalysisResult) {
        showToast('请先进行智能分析');
        return;
    }

    try {
        showLoadingOverlay('正在生成PDF...');

        // 创建一个临时的打印友好的HTML内容
        const printContent = generatePrintableContent(currentAnalysisResult);

        // 创建临时容器
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = printContent;
        tempDiv.style.cssText = `
            position: absolute;
            left: -9999px;
            top: 0;
            width: 210mm;
            background: white;
            padding: 20mm;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
        `;
        document.body.appendChild(tempDiv);

        // 使用html2canvas转换为图片
        const canvas = await html2canvas(tempDiv, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
        });

        // 创建PDF
        const { jsPDF } = window.jspdf;
        const pdf = new jsPDF('p', 'mm', 'a4');

        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;
        let position = 0;

        // 添加第一页
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        // 如果内容超过一页，添加更多页面
        while (heightLeft >= 0) {
            position = heightLeft - imgHeight;
            pdf.addPage();
            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;
        }

        // 下载PDF
        const fileName = `SAB工艺规范_${currentAnalysisResult.matchResult.processType}_${new Date().toISOString().slice(0, 10)}.pdf`;
        pdf.save(fileName);

        // 清理临时元素
        document.body.removeChild(tempDiv);

        showToast('PDF导出成功！');

    } catch (error) {
        console.error('PDF导出失败:', error);
        showToast('PDF导出失败: ' + error.message);
    } finally {
        hideLoadingOverlay();
    }
}

// 导出为Word文档（使用HTML格式）
async function exportToWord() {
    if (!currentAnalysisResult) {
        showToast('请先进行智能分析');
        return;
    }

    try {
        showLoadingOverlay('正在生成Word文档...');

        // 生成Word兼容的HTML内容
        const wordContent = generateWordContent(currentAnalysisResult);

        // 创建Word文档的HTML格式
        const htmlContent = `
            <html xmlns:o='urn:schemas-microsoft-com:office:office'
                  xmlns:w='urn:schemas-microsoft-com:office:word'
                  xmlns='http://www.w3.org/TR/REC-html40'>
            <head>
                <meta charset='utf-8'>
                <title>SAB工艺规范分析报告</title>
                <!--[if gte mso 9]>
                <xml>
                    <w:WordDocument>
                        <w:View>Print</w:View>
                        <w:Zoom>90</w:Zoom>
                        <w:DoNotPromptForConvert/>
                        <w:DoNotShowInsertionsAndDeletions/>
                    </w:WordDocument>
                </xml>
                <![endif]-->
                <style>
                    body {
                        font-family: 'Microsoft YaHei', '宋体', Arial, sans-serif;
                        font-size: 12pt;
                        line-height: 1.6;
                        margin: 2cm;
                        color: #333;
                    }
                    .title {
                        font-size: 18pt;
                        font-weight: bold;
                        text-align: center;
                        color: #1a73e8;
                        margin-bottom: 20pt;
                        border-bottom: 2pt solid #1a73e8;
                        padding-bottom: 10pt;
                    }
                    .subtitle {
                        font-size: 14pt;
                        text-align: center;
                        color: #666;
                        margin-bottom: 20pt;
                    }
                    .section-title {
                        font-size: 14pt;
                        font-weight: bold;
                        color: #1a73e8;
                        margin-top: 20pt;
                        margin-bottom: 10pt;
                        border-bottom: 1pt solid #e0e0e0;
                        padding-bottom: 5pt;
                    }
                    .station-title {
                        font-size: 13pt;
                        font-weight: bold;
                        color: #1a73e8;
                        margin-top: 15pt;
                        margin-bottom: 8pt;
                    }
                    .subsection-title {
                        font-size: 12pt;
                        font-weight: bold;
                        color: #333;
                        margin-top: 10pt;
                        margin-bottom: 5pt;
                    }
                    .info-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 15pt;
                    }
                    .info-table td {
                        border: 1pt solid #e0e0e0;
                        padding: 8pt;
                        vertical-align: top;
                    }
                    .info-table .label {
                        background-color: #f8f9fa;
                        font-weight: bold;
                        width: 25%;
                    }
                    .step-item {
                        margin-bottom: 10pt;
                        padding: 8pt;
                        background-color: #f8f9fa;
                        border-left: 3pt solid #1a73e8;
                    }
                    .step-title {
                        font-weight: bold;
                        margin-bottom: 5pt;
                    }
                    .step-detail {
                        margin-left: 15pt;
                        font-size: 11pt;
                        color: #666;
                        margin-bottom: 3pt;
                    }
                    .equipment-section, .fixture-section {
                        margin-bottom: 15pt;
                        padding: 10pt;
                        border: 1pt solid #e0e0e0;
                        background-color: #fafbfc;
                    }
                    .flow-diagram {
                        text-align: center;
                        padding: 15pt;
                        background-color: #f8f9fa;
                        border: 1pt solid #e0e0e0;
                        margin: 10pt 0;
                    }
                    .flow-step {
                        display: inline-block;
                        padding: 5pt 10pt;
                        background-color: #1a73e8;
                        color: white;
                        margin: 0 5pt;
                        border-radius: 3pt;
                    }
                    .flow-arrow {
                        margin: 0 8pt;
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                ${wordContent}
            </body>
            </html>
        `;

        // 创建Blob并下载
        const blob = new Blob([htmlContent], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });

        const fileName = `SAB工艺规范_${currentAnalysisResult.matchResult.processType}_${new Date().toISOString().slice(0, 10)}.doc`;

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.click();

        showToast('Word文档导出成功！');

    } catch (error) {
        console.error('Word导出失败:', error);
        showToast('Word导出失败: ' + error.message);
    } finally {
        hideLoadingOverlay();
    }
}

// 导出为JSON数据
function exportToJSON() {
    if (!currentAnalysisResult) {
        showToast('请先进行智能分析');
        return;
    }

    try {
        // 获取产品附件数据
        const attachments = typeof getAllAttachments === 'function' ? getAllAttachments() : {};

        // 添加导出时间戳和元数据
        const exportData = {
            exportTime: new Date().toISOString(),
            exportVersion: '1.1',
            analysisResult: currentAnalysisResult,
            productAttachments: attachments,
            metadata: {
                productFamily: smartMatchElements.productFamily?.value || '',
                components: getSelectedComponents(),
                projectRequirements: smartMatchElements.projectRequirements?.value || ''
            }
        };

        // 格式化JSON
        const jsonString = JSON.stringify(exportData, null, 2);

        // 创建下载链接
        const blob = new Blob([jsonString], { type: 'application/json' });
        const fileName = `SAB工艺数据_${currentAnalysisResult.matchResult.processType}_${new Date().toISOString().slice(0, 10)}.json`;

        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.click();

        showToast('JSON数据导出成功！');

    } catch (error) {
        console.error('JSON导出失败:', error);
        showToast('JSON导出失败: ' + error.message);
    }
}

// 打印报告
function printReport() {
    if (!currentAnalysisResult) {
        showToast('请先进行智能分析');
        return;
    }

    try {
        // 创建打印内容
        const printContent = generatePrintableContent(currentAnalysisResult);

        // 创建新窗口进行打印
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>SAB工艺规范分析报告</title>
                <style>
                    body {
                        font-family: 'Microsoft YaHei', Arial, sans-serif;
                        font-size: 12px;
                        line-height: 1.6;
                        color: #333;
                        margin: 20px;
                        background: white;
                    }
                    .print-header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #1a73e8;
                        padding-bottom: 20px;
                    }
                    .print-title {
                        font-size: 24px;
                        font-weight: bold;
                        color: #1a73e8;
                        margin-bottom: 10px;
                    }
                    .print-subtitle {
                        font-size: 14px;
                        color: #666;
                    }
                    .section {
                        margin-bottom: 25px;
                        page-break-inside: avoid;
                    }
                    .section-title {
                        font-size: 18px;
                        font-weight: bold;
                        color: #1a73e8;
                        margin-bottom: 15px;
                        border-bottom: 1px solid #e0e0e0;
                        padding-bottom: 5px;
                    }
                    .station-section {
                        margin-bottom: 20px;
                        border: 1px solid #e0e0e0;
                        border-radius: 6px;
                        padding: 15px;
                    }
                    .station-title {
                        font-size: 16px;
                        font-weight: bold;
                        color: #1a73e8;
                        margin-bottom: 10px;
                    }
                    .step-item {
                        margin-bottom: 10px;
                        padding: 10px;
                        background: #f8f9fa;
                        border-radius: 4px;
                    }
                    .step-title {
                        font-weight: bold;
                        margin-bottom: 5px;
                    }
                    .step-detail {
                        margin-left: 15px;
                        font-size: 11px;
                        color: #666;
                    }
                    .info-grid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 15px;
                        margin-bottom: 20px;
                    }
                    .info-item {
                        padding: 10px;
                        background: #f8f9fa;
                        border-radius: 4px;
                    }
                    .info-label {
                        font-weight: bold;
                        color: #1a73e8;
                    }
                    @media print {
                        body { margin: 0; }
                        .section { page-break-inside: avoid; }
                        .station-section { page-break-inside: avoid; }
                    }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
            </html>
        `);

        printWindow.document.close();

        // 等待内容加载完成后打印
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);

        showToast('正在准备打印...');

    } catch (error) {
        console.error('打印失败:', error);
        showToast('打印失败: ' + error.message);
    }
}

// 生成打印友好的内容
function generatePrintableContent(analysisResult) {
    const { matchResult, stationDetails } = analysisResult;

    let html = `
        <div class="print-header">
            <div class="print-title">SAB工艺规范分析报告</div>
            <div class="print-subtitle">工艺类型: ${matchResult.processType} | 生成时间: ${new Date().toLocaleString()}</div>
        </div>
    `;

    // 添加产品附件部分
    const attachments = typeof getAllAttachments === 'function' ? getAllAttachments() : {};
    if (Object.keys(attachments).length > 0) {
        html += `
        <div class="section">
            <div class="section-title">📎 产品附件</div>
            <div class="attachments-summary">
        `;

        Object.keys(attachments).forEach(type => {
            const attachment = attachments[type];
            const typeName = {
                'explosion-diagram': '产品爆炸图',
                'folding-diagram': '产品折叠图',
                'assembly-diagram': '产品总成图'
            }[type] || type;

            html += `
                <div class="attachment-summary">
                    <div class="attachment-name">${typeName}</div>
                    <div class="attachment-file">${attachment.file.name}</div>
                    ${attachment.notes ? `<div class="attachment-notes">备注: ${attachment.notes}</div>` : ''}
                </div>
            `;
        });

        html += `
            </div>
        </div>
        `;
    }

    html += `
        <div class="section">
            <div class="section-title">📊 分析结果概览</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">工艺类型</div>
                    <div>${matchResult.processType}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">匹配置信度</div>
                    <div>${matchResult.confidence}%</div>
                </div>
                <div class="info-item">
                    <div class="info-label">预计CT时间</div>
                    <div>${matchResult.estimatedCT}分钟</div>
                </div>
                <div class="info-item">
                    <div class="info-label">工站数量</div>
                    <div>${matchResult.stations.length}个</div>
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">零件组合</div>
                <div>${matchResult.components.join(', ')}</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">🏭 工艺流程</div>
            <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                ${matchResult.stations.map((station, index) =>
                    `<span style="display: inline-block; padding: 8px 16px; background: #1a73e8; color: white; border-radius: 4px; margin: 0 5px;">${station}</span>
                    ${index < matchResult.stations.length - 1 ? '<span style="margin: 0 10px;">→</span>' : ''}`
                ).join('')}
            </div>
        </div>
    `;

    // 添加工站详情
    Object.keys(stationDetails).forEach(stationId => {
        const station = stationDetails[stationId];

        html += `
            <div class="section">
                <div class="section-title">🔧 ${stationId} - ${station.processName}</div>
                <div class="station-section">
        `;

        // 工艺步骤
        if (station.steps && station.steps.length > 0) {
            html += `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1a73e8; margin-bottom: 10px;">工艺步骤</h4>
            `;

            station.steps.forEach(step => {
                html += `
                    <div class="step-item">
                        <div class="step-title">步骤${step.stepNumber}: ${step.description}</div>
                        ${step.operator ? `<div class="step-detail">操作方式: ${step.operator}</div>` : ''}
                        ${step.qualityRequirements ? `<div class="step-detail">质量要求: ${step.qualityRequirements}</div>` : ''}
                        ${step.errorProofing ? `<div class="step-detail">防错措施: ${step.errorProofing}</div>` : ''}
                    </div>
                `;
            });

            html += '</div>';
        }

        // 设备信息
        if (station.equipment && station.equipment.name) {
            html += `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1a73e8; margin-bottom: 10px;">设备信息</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 4px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">设备名称: ${station.equipment.name}</div>
                        ${station.equipment.mechanicalRequirements ? `
                            <div style="margin-bottom: 10px;">
                                <strong>机械要求:</strong><br>
                                <div style="margin-left: 15px; font-size: 11px;">${station.equipment.mechanicalRequirements}</div>
                            </div>
                        ` : ''}
                        ${station.equipment.electricalRequirements ? `
                            <div style="margin-bottom: 10px;">
                                <strong>电气要求:</strong><br>
                                <div style="margin-left: 15px; font-size: 11px;">${station.equipment.electricalRequirements}</div>
                            </div>
                        ` : ''}
                        ${station.equipment.errorProofingRequirements ? `
                            <div style="margin-bottom: 10px;">
                                <strong>防错要求:</strong><br>
                                <div style="margin-left: 15px; font-size: 11px;">${station.equipment.errorProofingRequirements}</div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }

        // 夹具信息
        if (station.fixtures && station.fixtures.length > 0) {
            html += `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #1a73e8; margin-bottom: 10px;">夹具信息</h4>
            `;

            station.fixtures.forEach((fixture, index) => {
                html += `
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 10px;">
                        <div style="font-weight: bold; margin-bottom: 10px;">夹具${index + 1}: ${fixture.name}</div>
                        ${fixture.mechanicalRequirements ? `
                            <div style="margin-bottom: 8px;">
                                <strong>机械要求:</strong><br>
                                <div style="margin-left: 15px; font-size: 11px;">${fixture.mechanicalRequirements}</div>
                            </div>
                        ` : ''}
                        ${fixture.electricalRequirements ? `
                            <div style="margin-bottom: 8px;">
                                <strong>电气要求:</strong><br>
                                <div style="margin-left: 15px; font-size: 11px;">${fixture.electricalRequirements}</div>
                            </div>
                        ` : ''}
                        ${fixture.errorProofingRequirements ? `
                            <div style="margin-bottom: 8px;">
                                <strong>防错要求:</strong><br>
                                <div style="margin-left: 15px; font-size: 11px;">${fixture.errorProofingRequirements}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            });

            html += '</div>';
        }

        html += '</div></div>';
    });

    return html;
}

// 生成Word文档内容
function generateWordContent(analysisResult) {
    const { matchResult, stationDetails } = analysisResult;

    let html = `
        <div class="title">SAB工艺规范分析报告</div>
        <div class="subtitle">工艺类型: ${matchResult.processType} | 生成时间: ${new Date().toLocaleString()}</div>
    `;

    // 添加产品附件部分
    const attachments = typeof getAllAttachments === 'function' ? getAllAttachments() : {};
    if (Object.keys(attachments).length > 0) {
        html += `
        <div class="section-title">产品附件</div>
        <table class="info-table">
        `;

        Object.keys(attachments).forEach(type => {
            const attachment = attachments[type];
            const typeName = {
                'explosion-diagram': '产品爆炸图',
                'folding-diagram': '产品折叠图',
                'assembly-diagram': '产品总成图'
            }[type] || type;

            html += `
            <tr>
                <td class="label">${typeName}</td>
                <td>${attachment.file.name}</td>
                <td>${attachment.notes || '无备注'}</td>
            </tr>
            `;
        });

        html += `
        </table>
        `;
    }

    html += `
        <div class="section-title">分析结果概览</div>
        <table class="info-table">
            <tr>
                <td class="label">工艺类型</td>
                <td>${matchResult.processType}</td>
                <td class="label">匹配置信度</td>
                <td>${matchResult.confidence}%</td>
            </tr>
            <tr>
                <td class="label">预计CT时间</td>
                <td>${matchResult.estimatedCT}分钟</td>
                <td class="label">工站数量</td>
                <td>${matchResult.stations.length}个</td>
            </tr>
            <tr>
                <td class="label">零件组合</td>
                <td colspan="3">${matchResult.components.join(', ')}</td>
            </tr>
        </table>

        <div class="section-title">工艺流程</div>
        <div class="flow-diagram">
            ${matchResult.stations.map((station, index) =>
                `<span class="flow-step">${station}</span>${index < matchResult.stations.length - 1 ? '<span class="flow-arrow">→</span>' : ''}`
            ).join('')}
        </div>
    `;

    // 添加工站详情
    Object.keys(stationDetails).forEach(stationId => {
        const station = stationDetails[stationId];

        html += `
            <div class="station-title">${stationId} - ${station.processName}</div>
        `;

        // 工艺步骤
        if (station.steps && station.steps.length > 0) {
            html += `
                <div class="subsection-title">工艺步骤</div>
            `;

            station.steps.forEach(step => {
                html += `
                    <div class="step-item">
                        <div class="step-title">步骤${step.stepNumber}: ${step.description}</div>
                        ${step.operator ? `<div class="step-detail"><strong>操作方式:</strong> ${step.operator}</div>` : ''}
                        ${step.qualityRequirements ? `<div class="step-detail"><strong>质量要求:</strong> ${step.qualityRequirements}</div>` : ''}
                        ${step.errorProofing ? `<div class="step-detail"><strong>防错措施:</strong> ${step.errorProofing}</div>` : ''}
                    </div>
                `;
            });
        }

        // 设备信息
        if (station.equipment && station.equipment.name) {
            html += `
                <div class="subsection-title">设备信息</div>
                <div class="equipment-section">
                    <div style="font-weight: bold; margin-bottom: 8pt;">设备名称: ${station.equipment.name}</div>
                    ${station.equipment.mechanicalRequirements ? `
                        <div style="margin-bottom: 8pt;">
                            <strong>机械要求:</strong><br>
                            <div style="margin-left: 15pt; font-size: 11pt;">${station.equipment.mechanicalRequirements}</div>
                        </div>
                    ` : ''}
                    ${station.equipment.electricalRequirements ? `
                        <div style="margin-bottom: 8pt;">
                            <strong>电气要求:</strong><br>
                            <div style="margin-left: 15pt; font-size: 11pt;">${station.equipment.electricalRequirements}</div>
                        </div>
                    ` : ''}
                    ${station.equipment.errorProofingRequirements ? `
                        <div style="margin-bottom: 8pt;">
                            <strong>防错要求:</strong><br>
                            <div style="margin-left: 15pt; font-size: 11pt;">${station.equipment.errorProofingRequirements}</div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // 夹具信息
        if (station.fixtures && station.fixtures.length > 0) {
            html += `
                <div class="subsection-title">夹具信息</div>
            `;

            station.fixtures.forEach((fixture, index) => {
                html += `
                    <div class="fixture-section">
                        <div style="font-weight: bold; margin-bottom: 8pt;">夹具${index + 1}: ${fixture.name}</div>
                        ${fixture.mechanicalRequirements ? `
                            <div style="margin-bottom: 6pt;">
                                <strong>机械要求:</strong><br>
                                <div style="margin-left: 15pt; font-size: 11pt;">${fixture.mechanicalRequirements}</div>
                            </div>
                        ` : ''}
                        ${fixture.electricalRequirements ? `
                            <div style="margin-bottom: 6pt;">
                                <strong>电气要求:</strong><br>
                                <div style="margin-left: 15pt; font-size: 11pt;">${fixture.electricalRequirements}</div>
                            </div>
                        ` : ''}
                        ${fixture.errorProofingRequirements ? `
                            <div style="margin-bottom: 6pt;">
                                <strong>防错要求:</strong><br>
                                <div style="margin-left: 15pt; font-size: 11pt;">${fixture.errorProofingRequirements}</div>
                            </div>
                        ` : ''}
                    </div>
                `;
            });
        }
    });

    return html;
}

function toggleAllStations() {
    const allContents = document.querySelectorAll('[id^="content-ST"]');
    const allToggles = document.querySelectorAll('[id^="toggle-ST"]');
    const mainToggle = document.getElementById('all-stations-toggle');

    allStationsExpanded = !allStationsExpanded;

    allContents.forEach(content => {
        if (allStationsExpanded) {
            content.classList.add('expanded');
        } else {
            content.classList.remove('expanded');
        }
    });

    allToggles.forEach(toggle => {
        toggle.textContent = allStationsExpanded ? '▲' : '▼';
    });

    if (mainToggle) {
        mainToggle.textContent = allStationsExpanded ? '▲' : '▼';
    }
}

// 添加CSS动画样式
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        .collapsible-content {
            transition: all 0.3s ease;
            overflow: hidden;
            max-height: 0;
        }

        .collapsible-content.expanded {
            max-height: none;
            display: block;
        }

        .stat-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
        }
    `;
    document.head.appendChild(style);
}

// 初始化Mermaid
function initMermaid() {
    if (typeof mermaid !== 'undefined') {
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            themeVariables: {
                primaryColor: '#1a73e8',
                primaryTextColor: '#333',
                primaryBorderColor: '#1a73e8',
                lineColor: '#666',
                secondaryColor: '#f8f9fa',
                tertiaryColor: '#e8f0fe'
            },
            flowchart: {
                htmlLabels: true,
                curve: 'basis'
            }
        });
    }
}

// ===== SAB类别快速选择功能 =====

// SAB类别与零件的映射关系（基于真实数据）
const SAB_CATEGORIES = {
    'A': ['Deflector', 'inflator', 'cushion', 'soft cover'],
    'B': ['Deflector', 'inflator', 'cushion', 'Harness', 'soft cover'],
    'C': ['Deflector', 'inflator', 'cushion', 'Harness', 'Bracket', 'Nuts', 'soft cover'],
    'D': ['Deflector', 'inflator', 'cushion', 'Harness', 'hard cover'],
    'E': ['Deflector', 'inflator', 'cushion', 'Bracket', 'Nuts', 'housing'],
    'F': ['Deflector', 'inflator', 'cushion', 'hard cover', 'housing', '3D heat']
};

// 选择指定SAB类别的所有零件
function selectSABCategory(category) {
    // 先清空所有选择
    clearAllComponents();

    // 获取该类别的零件列表
    const components = SAB_CATEGORIES[category];
    if (!components) {
        console.error('未知的SAB类别:', category);
        return;
    }

    // 选中该类别的所有零件
    components.forEach(component => {
        const checkbox = document.querySelector(`input[value="${component}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });

    showToast(`已选择SAB-${category}类别的所有零件`);
}

// 清空所有零件选择
function clearAllComponents() {
    const checkboxes = document.querySelectorAll('.components-selection input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(() => {
        initSmartMatch();
        addAnimationStyles();
        initMermaid();
    }, 100);
});
