<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品模板生成器</title>
    <link rel="stylesheet" href="template_generator.css">
</head>
<body>
    <div class="container">
        <div class="template-panel">
            <h2>产品模板生成器</h2>
            <div class="product-selection">
                <div class="form-group">
                    <label>产品类型</label>
                    <select id="productType">
                        <option value="">请选择产品类型...</option>
                        <option value="SAB">SAB</option>
                        <option value="IC">IC</option>
                        <option value="DAB">DAB</option>
                        <option value="PAB">PAB</option>
                        <option value="FCA">FCA</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>模板分类</label>
                    <select id="templateType">
                        <option value="">请选择模板分类...</option>
                        <option value="A">A</option>
                        <option value="B">B</option>
                        <option value="C">C</option>
                        <option value="D">D</option>
                        <option value="E">E</option>
                        <option value="F">F</option>
                    </select>
                </div>
            </div>
            
            <div class="parts-table">
                <table>
                    <thead>
                        <tr>
                            <th>新项目名称</th>
                            <th>零件编号</th>
                            <th>零件名称</th>
                            <th>单位用量</th>
                            <th>包装尺寸(mm)</th>
                            <th>单位包装数量</th>
                            <th>零件状态</th>
                            <th>共享项目名称</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="partsTableBody">
                    </tbody>
                </table>
                <button id="addPartRow" class="btn">添加零件</button>
            </div>
            
            <button id="generatePrompt" class="btn primary">一键生成</button>
        </div>
    </div>
    <script src="template_generator.js"></script>
</body>
</html>