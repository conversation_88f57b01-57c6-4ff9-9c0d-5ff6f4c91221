SAB工艺步骤完整性要求说明

本文档专门用于确保AI生成完整的工艺步骤，解决输出内容过短的问题。

## 问题描述
当前AI在生成SAB工艺步骤时，经常只输出3-4个步骤，但实际上每个工站都有5-15个详细的工艺步骤。

## 各SAB类别实际工艺步骤数量统计

### SAB-A类工艺步骤数量
- ST10 Pre-assembly: 8个工艺步骤
- ST20 Folding: 6个工艺步骤  
- ST30 Package: 4个工艺步骤
总计：18个工艺步骤

### SAB-B类工艺步骤数量
- ST10 Harness Assembly: 7个工艺步骤
- ST20 Folding: 6个工艺步骤
- ST30 E-check: 6个工艺步骤
- ST40 Package: 4个工艺步骤
总计：23个工艺步骤

### SAB-C类工艺步骤数量
- ST10 Harness Assembly: 4个工艺步骤
- ST20 Folding: 8个工艺步骤
- ST30 Torque: 8个工艺步骤
- ST40 E-check: 6个工艺步骤
- ST50 Package: 4个工艺步骤
总计：30个工艺步骤

### SAB-D类工艺步骤数量
- ST10 Harness Assembly: 7个工艺步骤
- ST20 Folding: 6个工艺步骤
- ST30 Cover closure: 8个工艺步骤
- ST40 E-check: 6个工艺步骤
- ST50 Package: 4个工艺步骤
总计：31个工艺步骤

### SAB-E类工艺步骤数量
- ST10 Pre-assembly: 12个工艺步骤
- ST20 Torque: 8个工艺步骤
- ST30 Cover closure: 8个工艺步骤
- ST40 Package: 4个工艺步骤
总计：32个工艺步骤

### SAB-F类工艺步骤数量
- ST10 Pre-assembly: 6个工艺步骤
- ST20 Pre-Torque: 5个工艺步骤
- ST30 Folding: 6个工艺步骤
- ST40 3D-heat press: 4个工艺步骤
- ST50 Cover closure: 4个工艺步骤
- ST60 Package: 1个工艺步骤
总计：26个工艺步骤

## 强制要求
1. **绝对不能省略工艺步骤**：必须输出知识库中记录的所有工艺步骤
2. **数量验证**：输出前请对照上述统计数量进行验证
3. **完整性检查**：每个工艺步骤必须包含完整的三要素：
   - 工艺过程描述
   - 人or设备
   - 产品特性要求
   - 过程防错要求
4. **设备部分也必须完整**：包括所有Equipment和Fixture的详细要求

## 输出验证清单
在输出前请检查：
□ 工艺步骤总数是否符合上述统计
□ 每个工站的步骤数是否完整
□ 每个步骤是否包含完整的四个要素
□ 设备部分是否包含所有机械、电气、防错要求
□ 是否有任何内容被省略或简化

## 如果输出被截断的解决方案
1. 分段输出：先输出工艺部分，再输出设备部分
2. 按工站分别输出：每次输出一个工站的完整信息
3. 明确告知用户需要继续输出剩余内容

记住：宁可输出过长，也不能遗漏任何工艺步骤！
