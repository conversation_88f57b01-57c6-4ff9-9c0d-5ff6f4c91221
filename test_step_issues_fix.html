<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤问题修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1a73e8;
            margin-bottom: 15px;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
        }
        .test-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #1557b0;
        }
        .test-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">步骤问题修复测试</h2>
        <p>此工具用于测试和验证步骤相关问题的修复效果。</p>
        
        <h3>问题1: 新增步骤会增大所有步骤框的长度</h3>
        <button class="test-button" onclick="testStepSizeConsistency()">测试步骤大小一致性</button>
        <button class="test-button" onclick="measureStepHeights()">测量步骤高度</button>
        
        <h3>问题2: 步骤框中有两个步骤后无法新增</h3>
        <button class="test-button" onclick="testStepInsertion()">测试步骤插入功能</button>
        <button class="test-button" onclick="testInsertionButtons()">测试插入按钮</button>
        
        <h3>修复验证</h3>
        <button class="test-button" onclick="runAllTests()">运行所有测试</button>
        <button class="test-button" onclick="fixStepIssues()">应用修复</button>
        
        <div id="test-output"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('test-output');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }

        function testStepSizeConsistency() {
            log('测试步骤大小一致性...');
            
            const allSteps = document.querySelectorAll('.process-step');
            if (allSteps.length === 0) {
                log('未找到步骤元素，请在工艺页面运行此测试', 'error');
                return;
            }
            
            log(`找到 ${allSteps.length} 个步骤`);
            
            const heights = [];
            const widths = [];
            
            allSteps.forEach((step, index) => {
                const rect = step.getBoundingClientRect();
                heights.push(rect.height);
                widths.push(rect.width);
                
                log(`步骤 ${index + 1}: ${rect.width.toFixed(1)}x${rect.height.toFixed(1)}px`, 'info');
            });
            
            // 检查高度一致性（允许小幅差异）
            const minHeight = Math.min(...heights);
            const maxHeight = Math.max(...heights);
            const heightDiff = maxHeight - minHeight;
            
            if (heightDiff > 50) {
                log(`❌ 步骤高度不一致！最大差异: ${heightDiff.toFixed(1)}px`, 'error');
                log(`最小高度: ${minHeight.toFixed(1)}px, 最大高度: ${maxHeight.toFixed(1)}px`, 'error');
            } else {
                log(`✅ 步骤高度基本一致，最大差异: ${heightDiff.toFixed(1)}px`, 'success');
            }
            
            // 检查宽度一致性
            const minWidth = Math.min(...widths);
            const maxWidth = Math.max(...widths);
            const widthDiff = maxWidth - minWidth;
            
            if (widthDiff > 10) {
                log(`❌ 步骤宽度不一致！最大差异: ${widthDiff.toFixed(1)}px`, 'error');
            } else {
                log(`✅ 步骤宽度一致`, 'success');
            }
        }

        function measureStepHeights() {
            log('详细测量步骤高度...');
            
            const allSteps = document.querySelectorAll('.process-step');
            if (allSteps.length === 0) {
                log('未找到步骤元素', 'error');
                return;
            }
            
            allSteps.forEach((step, index) => {
                const computedStyle = getComputedStyle(step);
                const rect = step.getBoundingClientRect();
                
                log(`步骤 ${index + 1} 详细信息:`, 'info');
                log(`  实际尺寸: ${rect.width.toFixed(1)}x${rect.height.toFixed(1)}px`, 'info');
                log(`  padding: ${computedStyle.padding}`, 'info');
                log(`  margin: ${computedStyle.margin}`, 'info');
                log(`  border: ${computedStyle.border}`, 'info');
                log(`  display: ${computedStyle.display}`, 'info');
                log(`  内容高度: ${step.scrollHeight}px`, 'info');
                log('---', 'info');
            });
        }

        function testStepInsertion() {
            log('测试步骤插入功能...');
            
            // 查找插入按钮
            const insertButtons = document.querySelectorAll('button[onclick*="insertProcessStep"]');
            const addButtons = document.querySelectorAll('button[onclick*="addProcessStep"]');
            
            log(`找到 ${insertButtons.length} 个插入按钮`);
            log(`找到 ${addButtons.length} 个添加按钮`);
            
            // 查找步骤插入菜单
            const insertMenus = document.querySelectorAll('.step-insert-dropdown');
            log(`找到 ${insertMenus.length} 个插入菜单`);
            
            // 查找步骤插入按钮（+按钮）
            const stepInsertButtons = document.querySelectorAll('.step-insert-btn');
            log(`找到 ${stepInsertButtons.length} 个步骤插入按钮`);
            
            if (insertButtons.length === 0 && addButtons.length === 0 && stepInsertButtons.length === 0) {
                log('❌ 未找到任何插入相关按钮', 'error');
                return;
            }
            
            // 测试按钮点击事件
            let workingButtons = 0;
            let totalButtons = 0;
            
            insertButtons.forEach((button, index) => {
                totalButtons++;
                try {
                    const onclick = button.getAttribute('onclick');
                    if (onclick && onclick.includes('insertProcessStep')) {
                        workingButtons++;
                        log(`✅ 插入按钮 ${index + 1} 配置正确: ${onclick}`, 'success');
                    } else {
                        log(`❌ 插入按钮 ${index + 1} 配置错误`, 'error');
                    }
                } catch (e) {
                    log(`❌ 插入按钮 ${index + 1} 测试失败: ${e.message}`, 'error');
                }
            });
            
            addButtons.forEach((button, index) => {
                totalButtons++;
                try {
                    const onclick = button.getAttribute('onclick');
                    if (onclick && onclick.includes('addProcessStep')) {
                        workingButtons++;
                        log(`✅ 添加按钮 ${index + 1} 配置正确: ${onclick}`, 'success');
                    } else {
                        log(`❌ 添加按钮 ${index + 1} 配置错误`, 'error');
                    }
                } catch (e) {
                    log(`❌ 添加按钮 ${index + 1} 测试失败: ${e.message}`, 'error');
                }
            });
            
            log(`按钮测试结果: ${workingButtons}/${totalButtons} 个按钮配置正确`, 
                workingButtons === totalButtons ? 'success' : 'warning');
        }

        function testInsertionButtons() {
            log('测试插入按钮交互...');
            
            const stepInsertButtons = document.querySelectorAll('.step-insert-btn');
            if (stepInsertButtons.length === 0) {
                log('未找到步骤插入按钮', 'warning');
                return;
            }
            
            stepInsertButtons.forEach((button, index) => {
                log(`测试步骤插入按钮 ${index + 1}...`);
                
                // 检查悬停菜单
                const menu = button.parentElement.querySelector('.step-insert-dropdown');
                if (menu) {
                    log(`  ✅ 找到对应的下拉菜单`, 'success');
                    
                    // 检查菜单选项
                    const menuOptions = menu.querySelectorAll('div[onclick]');
                    log(`  找到 ${menuOptions.length} 个菜单选项`, 'info');
                    
                    menuOptions.forEach((option, optIndex) => {
                        const onclick = option.getAttribute('onclick');
                        if (onclick) {
                            log(`    选项 ${optIndex + 1}: ${onclick}`, 'info');
                        }
                    });
                } else {
                    log(`  ❌ 未找到对应的下拉菜单`, 'error');
                }
                
                // 检查事件处理
                const onmouseenter = button.getAttribute('onmouseenter');
                const onmouseleave = button.getAttribute('onmouseleave');
                
                if (onmouseenter) {
                    log(`  ✅ 鼠标进入事件: ${onmouseenter}`, 'success');
                } else {
                    log(`  ❌ 缺少鼠标进入事件`, 'error');
                }
                
                if (onmouseleave) {
                    log(`  ✅ 鼠标离开事件: ${onmouseleave}`, 'success');
                } else {
                    log(`  ❌ 缺少鼠标离开事件`, 'error');
                }
            });
        }

        function fixStepIssues() {
            log('应用步骤问题修复...');
            
            // 修复步骤大小
            const allSteps = document.querySelectorAll('.process-step');
            allSteps.forEach((step, index) => {
                // 重置可能导致问题的样式
                step.style.removeProperty('height');
                step.style.removeProperty('min-height');
                step.style.removeProperty('max-height');
                
                // 应用正确的样式
                step.style.setProperty('display', 'block', 'important');
                step.style.setProperty('width', '100%', 'important');
                step.style.setProperty('margin-bottom', '0.5rem', 'important');
                step.style.setProperty('padding', '0.5rem', 'important');
                step.style.setProperty('border', '1px solid #e8e8e8', 'important');
                step.style.setProperty('border-radius', '4px', 'important');
                step.style.setProperty('background', 'white', 'important');
                
                // 确保内部flex布局正常
                const flexDivs = step.querySelectorAll('div[style*="display: flex"]');
                flexDivs.forEach(div => {
                    div.style.setProperty('display', 'flex', 'important');
                });
                
                log(`修复步骤 ${index + 1}`, 'success');
            });
            
            // 修复步骤容器
            const stepsContainers = document.querySelectorAll('.process-steps-container');
            stepsContainers.forEach((container, index) => {
                container.style.setProperty('display', 'flex', 'important');
                container.style.setProperty('flex-direction', 'column', 'important');
                container.style.setProperty('width', '100%', 'important');
                
                log(`修复步骤容器 ${index + 1}`, 'success');
            });
            
            log('修复完成！', 'success');
            
            // 重新测试
            setTimeout(() => {
                testStepSizeConsistency();
            }, 100);
        }

        function runAllTests() {
            log('=== 开始运行所有测试 ===');
            
            testStepSizeConsistency();
            setTimeout(() => {
                testStepInsertion();
                setTimeout(() => {
                    testInsertionButtons();
                    log('=== 所有测试完成 ===');
                }, 500);
            }, 500);
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('页面加载完成，开始自动测试...', 'info');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
