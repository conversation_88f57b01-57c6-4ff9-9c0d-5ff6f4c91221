"""
测试真实SAB数据的智能匹配功能
"""
import requests
import json

def test_sab_matching():
    """测试各个SAB类别的匹配"""
    
    base_url = "http://localhost:5000"
    
    # 测试用例
    test_cases = [
        {
            "name": "SAB-A 预装配",
            "components": ["Deflector", "inflator", "cushion", "soft cover"],
            "expected": "SAB-A"
        },
        {
            "name": "SAB-B 线束装配", 
            "components": ["Deflector", "inflator", "cushion", "Harness", "soft cover"],
            "expected": "SAB-B"
        },
        {
            "name": "SAB-C 扭矩检测",
            "components": ["Deflector", "inflator", "cushion", "Harness", "Bracket", "Nuts", "soft cover"],
            "expected": "SAB-C"
        },
        {
            "name": "SAB-D 硬盖装配",
            "components": ["Deflector", "inflator", "cushion", "Harness", "hard cover"],
            "expected": "SAB-D"
        },
        {
            "name": "SAB-E 外壳装配",
            "components": ["Deflector", "inflator", "cushion", "Bracket", "Nuts", "housing"],
            "expected": "SAB-E"
        },
        {
            "name": "SAB-F 3D热成型",
            "components": ["Deflector", "inflator", "cushion", "hard cover", "housing", "3D heat"],
            "expected": "SAB-F"
        }
    ]
    
    print("=== SAB智能匹配测试 ===\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. 测试 {test_case['name']}")
        print(f"   零件组合: {test_case['components']}")
        
        # 发送请求
        payload = {
            "product_family": "SAB",
            "components": test_case['components'],
            "project_requirements": f"测试{test_case['name']}的智能匹配"
        }
        
        try:
            response = requests.post(f"{base_url}/analyze_linespec", 
                                   json=payload, 
                                   timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'matchResult' in result:
                    match_result = result['matchResult']
                    process_type = match_result.get('processType', 'Unknown')
                    confidence = match_result.get('confidence', 0)
                    
                    print(f"   ✅ 匹配结果: {process_type}")
                    print(f"   📊 置信度: {confidence}%")
                    
                    if process_type == test_case['expected']:
                        print(f"   🎯 匹配正确!")
                    else:
                        print(f"   ❌ 匹配错误! 期望: {test_case['expected']}")
                        
                    # 显示工站信息
                    if 'stations' in match_result:
                        stations = match_result['stations']
                        print(f"   🏭 工站配置: {' → '.join(stations)}")
                        
                    # 显示CT时间
                    if 'estimatedCT' in match_result:
                        ct_time = match_result['estimatedCT']
                        print(f"   ⏱️ 预计CT: {ct_time}分钟")
                        
                else:
                    print(f"   ❌ 响应格式错误: {result}")
                    
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {str(e)}")
            
        print()

def test_partial_matching():
    """测试部分零件匹配"""
    
    print("=== 部分零件匹配测试 ===\n")
    
    base_url = "http://localhost:5000"
    
    # 测试部分零件组合
    partial_tests = [
        {
            "name": "基础零件组合",
            "components": ["Deflector", "inflator", "cushion"],
            "description": "只包含基础零件"
        },
        {
            "name": "包含线束",
            "components": ["Deflector", "inflator", "Harness"],
            "description": "包含线束的组合"
        },
        {
            "name": "包含硬盖",
            "components": ["Deflector", "hard cover"],
            "description": "包含硬盖的组合"
        }
    ]
    
    for i, test_case in enumerate(partial_tests, 1):
        print(f"{i}. 测试 {test_case['name']}")
        print(f"   零件组合: {test_case['components']}")
        print(f"   描述: {test_case['description']}")
        
        payload = {
            "product_family": "SAB",
            "components": test_case['components'],
            "project_requirements": test_case['description']
        }
        
        try:
            response = requests.post(f"{base_url}/analyze_linespec", 
                                   json=payload, 
                                   timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'matchResult' in result:
                    match_result = result['matchResult']
                    process_type = match_result.get('processType', 'Unknown')
                    confidence = match_result.get('confidence', 0)
                    
                    print(f"   ✅ 匹配结果: {process_type}")
                    print(f"   📊 置信度: {confidence}%")
                    
                else:
                    print(f"   ❌ 响应格式错误")
                    
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {str(e)}")
            
        print()

if __name__ == "__main__":
    print("开始测试真实SAB数据的智能匹配功能...\n")
    
    try:
        # 测试完整匹配
        test_sab_matching()
        
        # 测试部分匹配
        test_partial_matching()
        
        print("测试完成!")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
