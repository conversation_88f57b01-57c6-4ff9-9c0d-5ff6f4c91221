
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工艺步骤布局预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .step-preview { 
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white; 
        }
        .step-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem; 
        }
        .step-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .row { 
            display: flex; 
            align-items: center; 
            gap: 0.5rem; 
            margin-bottom: 0.4rem; 
        }
        .row.first-row { 
            align-items: flex-start; 
        }
        .main-content { 
            flex: 1; 
        }
        .operator-section { 
            width: 120px; 
            flex-shrink: 0; 
        }
        label { 
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap; 
        }
        textarea { 
            flex: 1; 
            height: 35px; 
            padding: 0.3rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            resize: none; 
            font-size: 0.8rem; 
            line-height: 1.3; 
        }
        select { 
            width: 70px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 30px; 
        }
        .operator-row { 
            display: flex; 
            align-items: center; 
            gap: 0.3rem; 
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>工艺步骤布局预览</h2>
        <p>新的布局设计：标题和内容在同一行，"人or设备"移到右边并缩小占比</p>
        
        <div class="step-preview">
            <div class="step-header">
                <h6 class="step-title">步骤 1</h6>
                <button style="background: #ff7875; color: white; border: none; border-radius: 2px; padding: 1px 4px; cursor: pointer; font-size: 0.65rem;">×</button>
            </div>
            
            <!-- 第一行：工艺过程描述 + 人or设备 -->
            <div class="row first-row">
                <div class="main-content">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <label>工艺过程描述:</label>
                        <textarea placeholder="请输入工艺过程描述">人工检查包装材料完整性</textarea>
                    </div>
                </div>
                <div class="operator-section">
                    <div class="operator-row">
                        <label>人or设备:</label>
                        <select>
                            <option>人</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 第二行：产品特性要求 -->
            <div class="row">
                <label>产品特性要求:</label>
                <textarea placeholder="请输入产品特性要求">包装材料无破损，标签清晰</textarea>
            </div>
            
            <!-- 第三行：过程防错要求 -->
            <div class="row">
                <label>过程防错要求:</label>
                <textarea placeholder="请输入过程防错要求">目视检查，发现问题立即停止</textarea>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>布局改进说明：</h3>
            <ul>
                <li>✅ "人or设备"移到步骤右边，宽度缩小到120px</li>
                <li>✅ 选择器宽度缩小到70px，节省空间</li>
                <li>✅ 所有标题和内容在同一行显示</li>
                <li>✅ 使用flex布局，响应式更好</li>
                <li>✅ textarea高度统一为35px，更紧凑</li>
                <li>✅ 标题设置white-space: nowrap防止换行</li>
            </ul>
        </div>
    </div>
</body>
</html>
        