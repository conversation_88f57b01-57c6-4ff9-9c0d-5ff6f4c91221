#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Prompt Lab知识库内容
将SAB知识库中的完整内容正确转换到Prompt Lab格式
"""

import os
import re
from pathlib import Path

class FixedPromptLabConverter:
    def __init__(self):
        self.input_dir = "SAB知识库"
        self.output_dir = "Prompt_Lab知识库_完整版"
        self.sab_categories = ['A', 'B', 'C', 'D', 'E', 'F']
        
    def create_output_directory(self):
        """创建输出目录"""
        Path(self.output_dir).mkdir(exist_ok=True)
        print(f"创建输出目录: {self.output_dir}")
    
    def read_markdown_file(self, category):
        """读取Markdown文件内容"""
        input_file = f"{self.input_dir}/SAB-{category}类.md"
        
        if not os.path.exists(input_file):
            print(f"❌ 输入文件不存在: {input_file}")
            return None
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except Exception as e:
            print(f"❌ 读取文件失败 {input_file}: {e}")
            return None
    
    def extract_parts_section(self, content):
        """提取零件清单部分"""
        parts_match = re.search(r'## 零件清单\n(.*?)(?=\n## |\n### |$)', content, re.DOTALL)
        if parts_match:
            parts_content = parts_match.group(1).strip()
            return parts_content
        return "零件信息待补充"
    
    def extract_process_section(self, content):
        """提取工艺部分"""
        process_match = re.search(r'### 工艺部分\n(.*?)(?=\n### 设备部分|\n## |$)', content, re.DOTALL)
        if process_match:
            process_content = process_match.group(1).strip()
            return process_content
        return "工艺流程信息待补充"
    
    def extract_equipment_section(self, content):
        """提取设备部分"""
        equipment_match = re.search(r'### 设备部分\n(.*?)$', content, re.DOTALL)
        if equipment_match:
            equipment_content = equipment_match.group(1).strip()
            return equipment_content
        return "设备配置信息待补充"
    
    def clean_content(self, content):
        """清理内容格式"""
        # 移除Markdown标记但保持结构
        content = re.sub(r'^\*\*(.*?)\*\*', r'\1', content, flags=re.MULTILINE)  # 移除粗体
        content = re.sub(r'`(.*?)`', r'\1', content)  # 移除代码标记
        # 保持标题结构
        return content
    
    def create_individual_knowledge_base(self, category):
        """创建单个SAB类别的完整知识库"""
        
        content = self.read_markdown_file(category)
        if not content:
            return False
        
        # 提取各部分内容
        parts_section = self.extract_parts_section(content)
        process_section = self.extract_process_section(content)
        equipment_section = self.extract_equipment_section(content)
        
        # 清理内容
        parts_section = self.clean_content(parts_section)
        process_section = self.clean_content(process_section)
        equipment_section = self.clean_content(equipment_section)
        
        # 构建TXT格式内容
        txt_content = f"""SAB-{category}类产品工艺设备完整知识库

=== 产品信息 ===
产品族: SAB
工艺类型: {category}
适用场景: SAB-{category}类产品的完整工艺流程和设备配置

=== 零件组成 ===
{parts_section}

=== 完整工艺流程 ===
{process_section}

=== 完整设备配置 ===
{equipment_section}

=== 应用说明 ===
本知识库适用于SAB-{category}类产品的工艺设计和设备配置。
当输入的零件组合匹配SAB-{category}类特征时，应参考此知识库生成相应的工艺流程和设备要求。
输出格式必须严格按照Markdown格式，包含工艺部分和设备部分的完整对应关系。

=== 输出格式要求 ===
必须按照以下结构输出：

### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

=== 重要提醒 ===
1. 必须确保工艺部分和设备部分的工站号完全对应
2. 每个工站的设备部分必须包含Equipment和Fixture两个子部分
3. 每个子部分必须包含机械要求、电气要求、防错及点检要求三个方面
4. 严格按照Markdown格式输出，使用正确的标题层级

"""
        
        # 保存文件
        output_file = f"{self.output_dir}/SAB-{category}类完整知识库.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(txt_content)
            
            file_size = os.path.getsize(output_file)
            print(f"✅ 已创建: SAB-{category}类完整知识库.txt ({file_size:,} bytes)")
            return True
            
        except Exception as e:
            print(f"❌ 创建文件失败 {output_file}: {e}")
            return False
    
    def create_comprehensive_knowledge_base(self):
        """创建综合的完整知识库"""
        
        # SAB类别零件清单数据
        SAB_PARTS_DATA = {
            'A': ['Deflector', 'inflator', 'cushion', 'soft cover'],
            'B': ['Deflector', 'inflator', 'cushion', 'Harness', 'soft cover'],
            'C': ['Deflector', 'inflator', 'cushion', 'Harness', 'Bracket', 'Nuts', 'soft cover'],
            'D': ['Deflector', 'inflator', 'cushion', 'Harness', 'hard cover'],
            'E': ['Deflector', 'inflator', 'cushion', 'Bracket', 'Nuts', 'housing'],
            'F': ['Deflector', 'inflator', 'cushion', 'hard cover', 'housing', '3D heat']
        }
        
        content = """SAB产品族完整工艺设备知识库

本知识库包含SAB产品族A-F所有类别的完整工艺流程和设备配置信息，基于真实的工程数据。

=== 核心功能 ===
1. 根据输入零件组合自动识别SAB类别
2. 生成对应的完整工艺流程（包含真实工艺步骤）
3. 提供详细的设备和夹具配置要求（包含真实设备参数）
4. 确保工艺部分和设备部分完全对应

=== SAB类别识别规则 ===
根据零件组合精确识别SAB类别：

"""
        
        # 添加类别识别规则
        for category, parts in SAB_PARTS_DATA.items():
            content += f"SAB-{category}类: {', '.join(parts)} ({len(parts)}个零件)\n"
        
        content += """
=== 零件功能说明 ===
- Deflector: 导流片，用于气流导向，所有SAB类别必备
- inflator: 发生器，气袋充气装置，所有SAB类别必备  
- cushion: 气袋，主要缓冲组件，所有SAB类别必备
- Harness: 线束，电气连接组件，用于B/C/D类
- Bracket: 支架，结构支撑组件，用于C/E类
- Nuts: 螺母，紧固件，与支架配套使用
- soft cover: 软包布，柔性覆盖材料，用于A/B/C类
- hard cover: 硬盖，刚性覆盖组件，用于D/F类
- housing: 外壳，保护性外罩，用于E/F类
- 3D heat: 3D加热元件，温控组件，仅F类使用

=== 输出格式要求 ===
必须严格按照以下Markdown格式输出，确保工艺部分和设备部分的工站号完全对应：

### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

"""
        
        # 添加每个SAB类别的完整详细信息
        for category in self.sab_categories:
            md_content = self.read_markdown_file(category)
            if md_content:
                content += f"\n{'='*80}\n"
                content += f"SAB-{category}类完整详细信息\n"
                content += f"{'='*80}\n\n"
                
                # 添加零件信息
                parts_section = self.extract_parts_section(md_content)
                content += f"零件组成:\n{self.clean_content(parts_section)}\n\n"
                
                # 添加完整工艺信息
                process_section = self.extract_process_section(md_content)
                content += f"完整工艺流程:\n{self.clean_content(process_section)}\n\n"
                
                # 添加完整设备信息
                equipment_section = self.extract_equipment_section(md_content)
                content += f"完整设备配置:\n{self.clean_content(equipment_section)}\n\n"
        
        # 保存综合文件
        output_file = f"{self.output_dir}/SAB产品族完整知识库_含真实数据.txt"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            file_size = os.path.getsize(output_file)
            print(f"✅ 已创建综合知识库: SAB产品族完整知识库_含真实数据.txt ({file_size:,} bytes)")
            return True
            
        except Exception as e:
            print(f"❌ 创建综合知识库失败: {e}")
            return False
    
    def run(self):
        """运行修复流程"""
        
        print("🔧 修复Prompt Lab知识库内容...")
        print("=" * 60)
        
        # 检查输入目录
        if not os.path.exists(self.input_dir):
            print(f"❌ 输入目录不存在: {self.input_dir}")
            return False
        
        # 创建输出目录
        self.create_output_directory()
        
        success_count = 0
        
        # 创建单独的完整知识库文件
        for category in self.sab_categories:
            print(f"\n📝 处理 SAB-{category}类...")
            if self.create_individual_knowledge_base(category):
                success_count += 1
        
        # 创建综合的完整知识库
        print(f"\n📝 创建综合完整知识库...")
        if self.create_comprehensive_knowledge_base():
            success_count += 1
        
        print(f"\n{'=' * 60}")
        print(f"📊 修复完成统计:")
        print(f"✅ 成功创建: {success_count} 个完整文件")
        
        return success_count > 0

def main():
    """主函数"""
    
    print("🚀 修复Prompt Lab知识库内容工具")
    print("=" * 60)
    print("目标: 将SAB知识库中的完整内容正确转换到Prompt Lab格式")
    
    converter = FixedPromptLabConverter()
    
    if converter.run():
        print(f"\n✅ 修复成功完成！")
        print(f"📁 输出目录: {converter.output_dir}")
        
        # 列出生成的文件
        if os.path.exists(converter.output_dir):
            print(f"📄 生成的完整文件:")
            for file in os.listdir(converter.output_dir):
                if file.endswith('.txt'):
                    file_path = os.path.join(converter.output_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"   - {file} ({file_size:,} bytes)")
        
        print(f"\n🎯 推荐上传:")
        print(f"1. 主要推荐: SAB产品族完整知识库_含真实数据.txt")
        print(f"2. 或分别上传各个SAB类别的完整知识库文件")
        print(f"3. 这些文件包含了真实的工艺流程和设备配置数据")
        
    else:
        print(f"\n❌ 修复过程中出现错误")

if __name__ == "__main__":
    main()
