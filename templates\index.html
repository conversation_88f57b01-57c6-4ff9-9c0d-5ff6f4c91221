<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linespec 问答系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.8;
            color: #2c3e50;
            background-color: #f8f9fa;
            font-size: 16px;
        }

        .container {
            width: 100vw;
            max-width: 100vw;
            min-width: 0;
            margin: 0;
            padding: 0;
            display: flex;
            gap: 0;
            position: relative;
            background: none;
            box-shadow: none;
        }

        /* 聊天界面放右边，占比更小 */
        .chat-container {
            flex: 1;
            min-width: 320px;
            max-width: 800px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 2rem;
        }

        .preview-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #f1f1f1;
        }

        .preview-header h2 {
            color: #1a73e8;
            font-size: 1.2rem; /* 从1.5rem减小到1.2rem */
            font-weight: 600;
            margin-bottom: 0.8rem; /* 减小底部间距 */
        }

        .preview-content {
            height: 600px;
            overflow-y: auto;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            background-color: #fafafa;
        }

        .preview-section {
            margin-bottom: 2rem;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .preview-section h3 {
            color: #1a73e8;
            font-size: 1.1rem; /* 减小字号 */
            margin-bottom: 0.8rem;
            padding-bottom: 0.4rem;
            border-bottom: 1px solid #f1f1f1;
        }

        .project-info-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .form-group {
            width: 100%;
            margin-bottom: 0.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #5f6368;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .form-group input:focus {
            outline: none;
            border-color: #1a73e8;
        }

        .form-group select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .form-group select:focus {
            outline: none;
            border-color: #1a73e8;
        }

        .form-select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 0.9rem;
            background-color: white;
            cursor: pointer;
        }

        .form-select:focus {
            outline: none;
            border-color: #1a73e8;
        }

        .form-select option {
            padding: 0.5rem;
        }

        .section-content {
            min-height: 100px;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: #fff;
        }

        .preview-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .preview-button {
            padding: 0.8rem 1.5rem;
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .preview-button:hover {
            background-color: #1557b0;
        }

        .preview-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 2px solid #f1f1f1;
        }

        .header h1 {
            color: #1a73e8;
            font-size: 1.5rem; /* 从2rem减小到1.5rem */
            font-weight: 600;
            margin-bottom: 1rem; /* 减小底部间距 */
        }

        .chat-box {
            height: 600px;
            overflow-y: auto;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            background-color: #fafafa;
        }

        .message {
            margin-bottom: 1.5rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            width: 100%;
            word-wrap: break-word;
            font-size: 0.95rem;
            line-height: 1.7;
        }

        .user-message {
            background-color: #e8f0fe;
            color: #1967d2;
            border: 1px solid #d2e3fc;
        }

        .ai-message {
            background-color: #ffffff;
            color: #202124;
            border: 1px solid #e8eaed;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
        }

        /* 表格样式 */
        .message table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background-color: white;
        }

        .message table th,
        .message table td {
            padding: 0.75rem;
            border: 1px solid #e0e0e0;
            text-align: left;
        }

        .message table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #1a73e8;
        }

        .message table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .message table tr:hover {
            background-color: #f1f3f4;
        }

        .input-container {
            display: flex;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin-top: 1rem;
        }

        #question-input {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e8eaed;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: white;
        }

        #question-input:focus {
            outline: none;
            border-color: #1a73e8;
        }

        #send-button {
            padding: 0 2rem;
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        #send-button:hover {
            background-color: #1557b0;
            transform: translateY(-1px);
        }

        #send-button:active {
            transform: translateY(1px);
        }

        .loading {
            display: none;
            text-align: center;
            margin: 1rem 0;
            color: #5f6368;
            font-size: 0.9rem;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            margin-left: 8px;
            border: 2px solid #5f6368;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .error {
            color: #d93025;
            padding: 1rem;
            background-color: #fce8e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        /* 自定义滚动条样式 */
        .chat-box::-webkit-scrollbar {
            width: 8px;
        }

        .chat-box::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .chat-box::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .chat-box::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 响应式设计 */
        @media (max-width: 1100px) {
            .container {
                flex-direction: column;
                gap: 1rem;
            }
            .preview-container, .chat-container {
                min-width: unset;
                max-width: unset;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 1rem auto;
                padding: 0 1rem;
            }

            .chat-container {
                padding: 1rem;
            }

            .message {
                padding: 0.8rem 1rem;
            }

            .message table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .input-container {
                flex-direction: column;
            }

            #send-button {
                width: 100%;
                padding: 1rem;
            }
        }

        /* 新增样式：侧边栏和内容区域 */
        .preview-container {
            display: flex;
            flex-direction: row;
            padding: 0;
        }

        .sidebar {
            width: 180px; /* 从210px减小到180px */
            min-width: 40px; /* 从50px减小到40px */
            background: #f6f8fa;
            border-right: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        /* 折叠时的样式 */
        .sidebar.collapsed {
            width: 40px; /* 从50px减小到40px */
        }

        /* 折叠按钮样式 */
        .sidebar-toggle {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: #f0f0f0;
        }

        .toggle-icon {
            font-style: normal;
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .toggle-icon {
            transform: rotate(180deg);
        }

        /* 菜单容器样式 */
        .menu-container {
            overflow: hidden;
            transition: all 0.3s ease;
        }

        /* 折叠时的菜单项样式 */
        .sidebar.collapsed .menu-container {
            opacity: 0;
            visibility: hidden;
        }

        .sidebar.collapsed:hover {
            width: 180px; /* 从210px减小到180px */
        }

        .sidebar.collapsed:hover .menu-container {
            opacity: 1;
            visibility: visible;
        }

        /* 菜单项样式调整 */
        .menu-item {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 6px 10px; /* 减小内边距 */
            margin: 2px 0; /* 减小外边距 */
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 12px; /* 减小字号到最小12px */
            line-height: 1.2; /* 减小行高 */
        }

        .menu-item:hover {
            background: rgba(26, 115, 232, 0.1);
        }

        .menu-item.active {
            background: #e3f0fd !important;
            color: #1976d2 !important;
            font-weight: bold;
            transition: background 0.2s;
        }

        .content-area {
            flex: 1;
            padding: 2rem;
        }

        .template-panel {
            margin-bottom: 2rem;
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
        }

        .template-panel h3 {
            color: #1a73e8;
            margin-bottom: 1rem;
        }

        .product-selection {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .product-selection .form-group {
            flex: 1;
        }

        .parts-table {
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .parts-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .parts-table th,
        .parts-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .parts-table th {
            background: #f5f5f5;
        }

        .parts-table input,
        .parts-table select {
            width: 100%;
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        /* 按钮样式 */
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #e0e0e0;
        }

        .btn.primary {
            background: #1a73e8;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
        }

        /* 修改预览容器样式 */
        .preview-container {
            position: relative;
            min-width: 800px; /* 增加最小宽度,确保基本操作空间 */
            width: 100vw;
            max-width: 100vw;
            flex: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 0;  /* 移除padding，避免影响拖动手柄 */
            display: flex;
        }

        /* 更新拖动手柄样式 */
        .resize-handle {
            position: absolute;
            top: 0;
            right: -5px;
            width: 10px;
            height: 100%;
            cursor: col-resize;
            background-color: transparent;
            transition: background-color 0.3s;
            z-index: 1000;
        }

        .resize-handle:hover,
        .resize-handle.dragging {
            background-color: rgba(26, 115, 232, 0.2);
        }

        /* 添加拖动时的样式 */
        .container.dragging {
            user-select: none;
            cursor: col-resize;
        }

        .container.dragging .resize-handle {
            background-color: rgba(26, 115, 232, 0.3);
        }

        .terms-section {
            margin-bottom: 2rem;
        }

        .terms-section h4 {
            color: #1a73e8;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .terms-section ol {
            padding-left: 1.5rem;
            margin-bottom: 1rem;
        }

        .terms-section li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }

        .safety-requirements .note {
            background-color: #fff3e0;
            padding: 0.8rem;
            margin-bottom: 1rem;
            border-left: 4px solid #ff9800;
        }

        .safety-section {
            margin-top: 1.5rem;
        }

        .safety-section h5 {
            color: #1a73e8;
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }

        .safety-section p {
            margin-bottom: 1rem;
        }

        /* 新增：目录高亮动画 */
        .menu-item.active {
            background: #e3f0fd !important;
            color: #1976d2 !important;
            font-weight: bold;
            transition: background 0.2s;
        }

        /* AI助手唤醒按钮样式 */
        .ai-assistant-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #1a73e8, #4285f4);
            border-radius: 50%;
            box-shadow: 0 4px 20px rgba(26, 115, 232, 0.4);
            z-index: 999;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .ai-assistant-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(26, 115, 232, 0.5);
            background: linear-gradient(135deg, #1557b0, #3367d6);
        }

        .ai-assistant-trigger.hidden {
            display: none;
        }

        /* AI助手脉冲动画 */
        .ai-pulse {
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            background: rgba(26, 115, 232, 0.3);
            animation: pulse 2s infinite;
            pointer-events: none;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.1);
                opacity: 0.7;
            }
            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        /* 首次访问时显示脉冲动画 */
        .ai-assistant-trigger.first-visit .ai-pulse {
            animation: pulse 2s infinite;
        }

        .ai-icon {
            font-size: 24px;
            margin-bottom: 2px;
        }

        .ai-text {
            font-size: 10px;
            color: white;
            font-weight: 500;
            text-align: center;
            line-height: 1;
        }

        /* AI助手按钮响应式设计 */
        @media (max-width: 768px) {
            .ai-assistant-trigger {
                width: 60px;
                height: 60px;
                bottom: 15px;
                right: 15px;
            }

            .ai-icon {
                font-size: 20px;
            }

            .ai-text {
                font-size: 9px;
            }
        }

        @media (max-width: 480px) {
            .ai-assistant-trigger {
                width: 50px;
                height: 50px;
                bottom: 10px;
                right: 10px;
            }

            .ai-icon {
                font-size: 18px;
            }

            .ai-text {
                font-size: 8px;
            }
        }

        /* 新增：浮动聊天窗样式 */
        .floating-chat {
            position: fixed;
            right: 32px;
            bottom: 32px;
            width: 380px;
            min-width: 280px;
            max-width: 98vw;
            min-height: 80px;
            max-height: 80vh;
            height: 520px;
            z-index: 9999;
            box-shadow: 0 6px 32px rgba(0,0,0,0.18);
            border-radius: 15px;
            background: white;
            transition: box-shadow 0.2s, width 0.3s, height 0.3s;
            display: flex;
            flex-direction: column;
            resize: both;
            overflow: hidden;
        }
        .floating-chat.maximized {
            width: 90vw !important;
            height: 90vh !important;
            right: 5vw !important;
            bottom: 5vh !important;
            min-width: 320px;
            min-height: 320px;
        }
        .floating-chat-header {
            cursor: move;
            background: #1a73e8;
            color: #fff;
            padding: 0.7rem 1.2rem;
            border-radius: 15px 15px 0 0;
            font-size: 1.15rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
            user-select: none;
        }
        .floating-chat-close, .floating-chat-max {
            background: none;
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            margin-left: 10px;
        }
        .floating-chat-max {
            font-size: 1.1rem;
        }
        .chat-container {
            flex: 1 1 auto;
            display: flex;
            flex-direction: column;
            box-shadow: none;
            border-radius: 0 0 15px 15px;
            background: #fff;
            min-height: 0;
        }
        .chat-box {
            flex: 1 1 auto;
            overflow-y: auto;
            padding: 1.2rem;
            font-size: 1.08rem;
            background: #fafbfc;
            border-radius: 0 0 0 0;
            max-height: 100%;
        }
        .message {
            margin-bottom: 1.2rem;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            width: 95%;
            max-width: 95%;
            word-break: break-all;
            font-size: 1.08rem;
            line-height: 1.7;
            box-sizing: border-box;
        }

        .user-message {
            background-color: #e8f0fe;
            color: #1967d2;
            border: 1px solid #d2e3fc;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background-color: #ffffff;
            color: #202124;
            border: 1px solid #e8eaed;
            margin-right: auto;
            text-align: left;
            box-shadow: 0 1px 4px rgba(0,0,0,0.04);
        }

        .message .expand-btn {
            color: #1a73e8;
            cursor: pointer;
            font-size: 0.95em;
            margin-left: 8px;
        }

        @media (max-width: 600px) {
            .floating-chat, .floating-chat.maximized {
                width: 98vw !important;
                height: 90vh !important;
                right: 1vw !important;
                bottom: 1vh !important;
                min-width: unset;
                min-height: unset;
            }
        }

        /* 聊天窗header加最大化按钮 */
        .floating-chat-header {
            cursor: move;
            background: #1a73e8;
            color: #fff;
            padding: 0.7rem 1.2rem;
            border-radius: 15px 15px 0 0;
            font-size: 1.15rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
            user-select: none;
        }
        .floating-chat-close, .floating-chat-max {
            background: none;
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            margin-left: 10px;
        }
        .floating-chat-max {
            font-size: 1.1rem;
        }

        /* 聊天内容字体缩小，提升可观性 */
        .floating-chat .chat-box {
            font-size: 0.92rem;
        }
        .floating-chat .message {
            font-size: 0.92rem;
            padding: 0.7rem 1rem;
        }
        .floating-chat .ai-message, 
        .floating-chat .user-message {
            line-height: 1.5;
        }

        /* 工艺工站样式 */
        .station-block {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            background: #fafbfc;
            position: relative;
        }

        .station-block label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .station-block input[type="text"],
        .station-block textarea {
            font-size: 0.95rem;
            padding: 0.4rem;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%;
            box-sizing: border-box;
        }

        .station-block textarea {
            resize: vertical;
        }

        .station-block button {
            padding: 0.4rem 0.8rem;
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s;
        }

        .station-block button:hover {
            background-color: #1557b0;
        }

        /* 智能匹配页面样式 */
        .smart-match-input {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .components-selection {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .component-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .component-item:hover {
            border-color: #1a73e8;
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
        }

        .component-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #1a73e8;
        }

        .component-item label {
            margin: 0;
            cursor: pointer;
            font-size: 0.9rem;
            color: #333;
        }

        .component-category {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #1a73e8;
        }

        .component-category h6 {
            margin: 0 0 1rem 0;
            color: #1a73e8;
            font-weight: 600;
            font-size: 0.95rem;
        }

        .quick-select-btn {
            padding: 0.4rem 0.8rem;
            background: #1a73e8;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-select-btn:hover {
            background: #1557b0;
            transform: translateY(-1px);
        }

        .quick-select-btn.clear-btn {
            background: #ff4d4f;
        }

        .quick-select-btn.clear-btn:hover {
            background: #d32f2f;
        }

        /* 产品附件上传样式 */
        .attachments-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .attachment-category {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .attachment-category-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .attachment-category-header h4 {
            margin: 0;
            color: #1a73e8;
            font-size: 1rem;
        }

        .attachment-category-controls {
            display: flex;
            gap: 0.5rem;
        }

        .clear-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .clear-btn:hover {
            background: #d32f2f;
        }

        .attachment-category-content {
            min-height: 120px;
            padding: 1rem;
        }

        .attachment-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .attachment-header h4 {
            margin: 0;
            color: #1a73e8;
            font-size: 1rem;
        }

        .attachment-controls {
            display: flex;
            gap: 0.5rem;
        }

        .upload-btn {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .upload-btn:hover {
            background: #1557b0;
        }

        .remove-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .remove-btn:hover {
            background: #d32f2f;
        }

        .attachment-content {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
            position: relative;
        }

        .upload-placeholder {
            text-align: center;
            color: #666;
        }

        .placeholder-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .upload-placeholder p {
            margin: 0.5rem 0;
            font-size: 1rem;
            color: #333;
        }

        .upload-placeholder small {
            color: #999;
            font-size: 0.8rem;
        }

        .attachment-preview {
            width: 100%;
            height: 100%;
            object-fit: contain;
            max-height: 200px;
        }

        .attachment-preview.pdf {
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #666;
        }

        .attachment-notes {
            padding: 1rem;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .attachment-notes label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
        }

        .attachment-notes textarea {
            width: 100%;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            padding: 0.5rem;
            font-size: 0.9rem;
            resize: vertical;
            font-family: inherit;
        }

        .attachment-notes textarea:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .attachment-info {
            position: absolute;
            bottom: 0.5rem;
            right: 0.5rem;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
        }

        /* 多附件管理样式 */
        .product-attachment-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
            transition: box-shadow 0.2s, border-color 0.2s;
        }

        .product-attachment-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-color: #40a9ff;
        }

        .product-attachment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .product-attachment-info {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            flex: 1;
        }

        .product-attachment-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background: #f0f9ff;
        }

        .product-attachment-details {
            flex: 1;
        }

        .product-attachment-name {
            font-weight: 600;
            color: #333;
            margin: 0 0 0.2rem 0;
            font-size: 1rem;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .product-attachment-name:hover {
            background-color: #f0f9ff;
        }

        .product-attachment-meta {
            font-size: 0.8rem;
            color: #666;
            margin: 0;
        }

        .product-attachment-controls {
            display: flex;
            gap: 0.3rem;
        }

        .product-attachment-btn {
            padding: 0.3rem 0.6rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 0.2rem;
        }

        .product-attachment-btn.view {
            background: #52c41a;
            color: white;
        }

        .product-attachment-btn.view:hover {
            background: #73d13d;
        }

        .product-attachment-btn.edit {
            background: #faad14;
            color: white;
        }

        .product-attachment-btn.edit:hover {
            background: #ffc53d;
        }

        .product-attachment-btn.delete {
            background: #ff4d4f;
            color: white;
        }

        .product-attachment-btn.delete:hover {
            background: #ff7875;
        }

        .product-attachment-btn.download {
            background: #1890ff;
            color: white;
        }

        .product-attachment-btn.download:hover {
            background: #40a9ff;
        }

        .product-attachment-preview {
            margin-top: 1rem;
            padding: 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
            display: none;
        }

        .product-attachment-preview.active {
            display: block;
        }

        .product-attachment-preview img {
            max-width: 100%;
            max-height: 300px;
            object-fit: contain;
            border-radius: 4px;
            display: block;
            margin: 0 auto;
        }

        .product-attachment-preview .pdf-preview {
            text-align: center;
            padding: 2rem;
            color: #ff4d4f;
            font-size: 3rem;
        }

        .product-attachment-notes {
            margin-top: 1rem;
        }

        .product-attachment-notes label {
            display: block;
            margin-bottom: 0.3rem;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .product-attachment-notes textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            resize: vertical;
            font-family: inherit;
            font-size: 0.9rem;
            min-height: 80px;
        }

        .product-attachment-notes textarea:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .product-attachment-edit-mode {
            border-color: #faad14 !important;
            background: #fffbf0 !important;
        }

        .product-attachment-edit-mode .product-attachment-icon {
            background: #fff7e6;
            color: #faad14;
        }

        /* 分类附件项样式 */
        .category-attachment-item {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 0.8rem;
            margin-bottom: 0.8rem;
            background: #fafafa;
            transition: box-shadow 0.2s, border-color 0.2s;
        }

        .category-attachment-item:hover {
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            border-color: #40a9ff;
        }

        .category-attachment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
            padding-bottom: 0.4rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .category-attachment-info {
            display: flex;
            align-items: center;
            gap: 0.6rem;
            flex: 1;
        }

        .category-attachment-icon {
            font-size: 1.2rem;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            background: #e6f7ff;
        }

        .category-attachment-details {
            flex: 1;
        }

        .category-attachment-name {
            font-weight: 600;
            color: #333;
            margin: 0 0 0.2rem 0;
            font-size: 0.9rem;
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }

        .category-attachment-name:hover {
            background-color: #e6f7ff;
        }

        .category-attachment-meta {
            font-size: 0.75rem;
            color: #666;
            margin: 0;
        }

        .category-attachment-controls {
            display: flex;
            gap: 0.25rem;
        }

        .category-attachment-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7rem;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 0.2rem;
        }

        .category-attachment-btn.view {
            background: #52c41a;
            color: white;
        }

        .category-attachment-btn.view:hover {
            background: #73d13d;
        }

        .category-attachment-btn.edit {
            background: #faad14;
            color: white;
        }

        .category-attachment-btn.edit:hover {
            background: #ffc53d;
        }

        .category-attachment-btn.delete {
            background: #ff4d4f;
            color: white;
        }

        .category-attachment-btn.delete:hover {
            background: #ff7875;
        }

        .category-attachment-btn.download {
            background: #1890ff;
            color: white;
        }

        .category-attachment-btn.download:hover {
            background: #40a9ff;
        }

        .category-attachment-preview {
            margin-top: 0.8rem;
            padding: 0.8rem;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background: white;
            display: none;
        }

        .category-attachment-preview.active {
            display: block;
        }

        .category-attachment-preview img {
            max-width: 100%;
            max-height: 200px;
            object-fit: contain;
            border-radius: 4px;
            display: block;
            margin: 0 auto;
        }

        .category-attachment-preview .pdf-preview {
            text-align: center;
            padding: 1.5rem;
            color: #ff4d4f;
            font-size: 2rem;
        }

        .category-attachment-notes {
            margin-top: 0.8rem;
        }

        .category-attachment-notes label {
            display: block;
            margin-bottom: 0.3rem;
            font-weight: 600;
            color: #333;
            font-size: 0.8rem;
        }

        .category-attachment-notes textarea {
            width: 100%;
            padding: 0.4rem;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            resize: vertical;
            font-family: inherit;
            font-size: 0.8rem;
            min-height: 60px;
        }

        .category-attachment-notes textarea:focus {
            outline: none;
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .category-attachment-edit-mode {
            border-color: #faad14 !important;
            background: #fffbf0 !important;
        }

        .category-attachment-edit-mode .category-attachment-icon {
            background: #fff7e6;
            color: #faad14;
        }

        /* 夹具清单样式 */
        .fixture-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            flex-wrap: wrap;
            align-items: end;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            min-width: 150px;
        }

        .control-group label {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
        }

        .control-group select,
        .control-group input {
            padding: 0.5rem;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .control-group select:focus,
        .control-group input:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .fixture-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .summary-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .summary-card:hover {
            transform: translateY(-2px);
        }

        .summary-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1a73e8;
            margin-bottom: 0.5rem;
        }

        .summary-label {
            color: #666;
            font-size: 0.9rem;
        }

        .fixture-table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .fixture-table {
            width: 100%;
            border-collapse: collapse;
        }

        .fixture-table th {
            background: #1a73e8;
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .fixture-table td {
            padding: 0.8rem 1rem;
            border-bottom: 1px solid #e0e0e0;
            font-size: 0.9rem;
        }

        .fixture-table tbody tr:hover {
            background: #f5f5f5;
        }

        .fixture-category-badge {
            display: inline-block;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            color: white;
        }

        .fixture-category-badge.sab-a { background: #ff6b6b; }
        .fixture-category-badge.sab-b { background: #4ecdc4; }
        .fixture-category-badge.sab-c { background: #45b7d1; }
        .fixture-category-badge.sab-d { background: #96ceb4; }
        .fixture-category-badge.sab-f { background: #ff9ff3; }

        .fixture-details {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
        }

        .fixture-details-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .fixture-details-header h3 {
            margin: 0;
            color: #1a73e8;
        }

        .close-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            background: #d32f2f;
        }

        .btn-small {
            padding: 0.3rem 0.6rem;
            font-size: 0.8rem;
            border: none;
            border-radius: 4px;
            background: #1a73e8;
            color: white;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .btn-small:hover {
            background: #1557b0;
        }

        .fixture-station-badge {
            background: #f0f0f0;
            color: #333;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .match-result-card {
            background: linear-gradient(135deg, #e8f0fe, #f8f9ff);
            border: 2px solid #1a73e8;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .match-result-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .match-result-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1a73e8;
        }

        .confidence-badge {
            background: #1a73e8;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .match-result-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
        }

        .detail-icon {
            font-size: 1.1rem;
        }

        .stations-flow {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1.5rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            overflow-x: auto;
        }

        .station-node {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            padding: 1rem;
            background: #f8f9fa;
            border: 2px solid #1a73e8;
            border-radius: 8px;
            text-align: center;
        }

        .station-node-id {
            font-weight: 600;
            color: #1a73e8;
            font-size: 0.9rem;
        }

        .station-node-name {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.3rem;
        }

        .flow-arrow {
            font-size: 1.5rem;
            color: #1a73e8;
        }

        .station-detail-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .station-detail-header {
            background: #1a73e8;
            color: white;
            padding: 1rem;
            font-weight: 600;
        }

        .station-detail-content {
            padding: 1.5rem;
        }

        .detail-section {
            margin-bottom: 1.5rem;
        }

        .detail-section h5 {
            color: #1a73e8;
            margin-bottom: 0.8rem;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .step-item {
            background: #f8f9fa;
            border-left: 4px solid #1a73e8;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border-radius: 0 6px 6px 0;
        }

        .step-description {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .step-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.8rem;
            font-size: 0.9rem;
            color: #666;
        }

        .equipment-info, .fixture-info {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 1rem;
        }

        .equipment-info h6, .fixture-info h6 {
            color: #1a73e8;
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }

        /* 加载动画和状态提示 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(2px);
        }

        .loading-content {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #1a73e8;
            font-weight: 500;
            font-size: 1.1rem;
        }

        /* 折叠展开功能 */
        .collapsible-section {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .collapsible-header {
            background: #f8f9fa;
            padding: 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background-color 0.3s ease;
        }

        .collapsible-header:hover {
            background: #e9ecef;
        }

        .collapsible-header h5 {
            margin: 0;
            color: #1a73e8;
            font-size: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .collapsible-toggle {
            font-size: 1.2rem;
            color: #666;
            transition: transform 0.3s ease;
        }

        .collapsible-toggle.expanded {
            transform: rotate(180deg);
        }

        .collapsible-content {
            padding: 1rem;
            display: none;
        }

        .collapsible-content.expanded {
            display: block;
        }

        /* 历史记录功能 */
        .history-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .history-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .history-item:hover {
            box-shadow: 0 2px 8px rgba(26, 115, 232, 0.1);
            border-color: #1a73e8;
        }

        .history-item:last-child {
            margin-bottom: 0;
        }

        .history-info {
            flex: 1;
        }

        .history-title {
            font-weight: 500;
            color: #333;
            font-size: 0.9rem;
        }

        .history-details {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.2rem;
        }

        .history-actions {
            display: flex;
            gap: 0.5rem;
        }

        .history-btn {
            padding: 0.3rem 0.6rem;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .history-btn:hover {
            background: #1a73e8;
            color: white;
            border-color: #1a73e8;
        }

        /* 快速选择预设 */
        .quick-select-section {
            background: #e8f0fe;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .quick-select-title {
            color: #1a73e8;
            font-weight: 600;
            margin-bottom: 0.8rem;
            font-size: 0.95rem;
        }

        .quick-select-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .quick-select-btn {
            padding: 0.5rem 1rem;
            background: white;
            border: 2px solid #1a73e8;
            color: #1a73e8;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-select-btn:hover {
            background: #1a73e8;
            color: white;
        }

        /* 结果统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a73e8;
            margin-bottom: 0.3rem;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #666;
        }

        /* 增强的工艺步骤样式 */
        .step-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.8rem;
        }

        .step-number {
            background: #1a73e8;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .step-title {
            flex: 1;
            font-weight: 500;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .operator-icon {
            font-size: 1.2rem;
        }

        .manual-step {
            border-left-color: #ff9800;
        }

        .auto-step {
            border-left-color: #4caf50;
        }

        .detail-tag {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .tag-icon {
            font-size: 1rem;
            flex-shrink: 0;
        }

        .tag-label {
            font-weight: 500;
            color: #666;
            min-width: 80px;
        }

        .tag-value {
            flex: 1;
            color: #333;
        }

        .operator-tag {
            border-left: 3px solid #2196f3;
        }

        .quality-tag {
            border-left: 3px solid #4caf50;
        }

        .error-proofing-tag {
            border-left: 3px solid #ff9800;
        }

        /* 设备和夹具信息样式 */
        .equipment-name-card {
            background: #e8f0fe;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #1a73e8;
        }

        .equipment-name-card h6 {
            color: #1a73e8;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .equipment-name-card p {
            margin: 0;
            font-weight: 500;
            color: #333;
        }

        .requirement-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .req-header {
            background: #f8f9fa;
            padding: 0.8rem 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 1px solid #e0e0e0;
        }

        .req-icon {
            font-size: 1.1rem;
        }

        .req-header h6 {
            margin: 0;
            color: #333;
            font-size: 0.95rem;
        }

        .req-content {
            padding: 1rem;
        }

        .mechanical-req {
            border-left: 4px solid #ff9800;
        }

        .electrical-req {
            border-left: 4px solid #2196f3;
        }

        .error-proofing-req {
            border-left: 4px solid #4caf50;
        }

        .requirement-list {
            margin: 0;
            padding-left: 1.5rem;
        }

        .requirement-list li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .numbered-item {
            font-weight: 500;
            color: #1a73e8;
        }

        /* 夹具信息样式 */
        .fixture-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .fixture-number {
            background: #1a73e8;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .fixture-header h6 {
            margin: 0;
            color: #333;
            font-size: 1rem;
        }

        /* Mermaid图表样式 */
        .mermaid-diagram {
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mermaid-diagram svg {
            max-width: 100%;
            height: auto;
        }

        /* 工艺页面工站样式 */
        .process-step {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.8rem;
        }

        .process-step .step-header {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .process-step .step-number {
            background: #1a73e8;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .process-step .step-title {
            font-weight: 500;
            color: #333;
        }

        .process-step .step-details {
            margin-left: 2rem;
        }

        .process-step .step-detail {
            margin-bottom: 0.3rem;
            font-size: 0.9rem;
            color: #666;
        }

        .equipment-section, .fixtures-section {
            margin-top: 1rem;
            padding: 1rem;
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }

        .equipment-section h5, .fixtures-section h5 {
            color: #1a73e8;
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }

        .equipment-item, .fixture-item {
            background: #f8f9fa;
            padding: 0.8rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .equipment-item:last-child, .fixture-item:last-child {
            margin-bottom: 0;
        }

        .station-badge {
            background: #e8f0fe;
            color: #1a73e8;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .station-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .station-btn {
            background: none;
            border: none;
            padding: 0.3rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background-color 0.3s ease;
        }

        .station-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .edit-btn:hover {
            background: rgba(26, 115, 232, 0.1);
        }

        .delete-btn:hover {
            background: rgba(244, 67, 54, 0.1);
        }

        .steps-container {
            max-height: none;
        }

        .process-steps-section {
            margin-bottom: 1rem;
        }

        .process-steps-section h5 {
            color: #1a73e8;
            margin-bottom: 0.8rem;
            font-size: 1rem;
        }
    </style>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="/static/css/station_styles.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="/static/js/station_generator.js"></script>
    <script src="/static/js/station_manager.js"></script>
    <script src="/static/js/smart_match.js"></script>
    <script src="/static/js/product_attachments.js"></script>
    <script src="/static/js/fixture_manager.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 左侧目录导航 -->
        <div class="preview-container">
            <!-- 目录栏 -->
            <nav class="sidebar">
                <!-- 添加折叠按钮 -->
                <div class="sidebar-toggle" id="sidebar-toggle">
                    <i class="toggle-icon">≡</i>
                </div>
                <!-- 菜单内容容器 -->
                <div class="menu-container">
                    <div class="menu-item active" data-page="cover">0.制造规范封面</div>
                    <div class="menu-item" data-page="smart-match">🎯 智能匹配</div>
                    <div class="menu-item" data-page="product">1.产品信息（APP）</div>
                    <div class="menu-item" data-page="process">2.工艺要求（APP）</div>
                    <div class="menu-item" data-page="equipment">3.设备要求（AEP）</div>
                    <div class="menu-item" data-page="fixture">4.设备夹具清单(AEP)</div>
                    <div class="menu-item" data-page="terms">5.通用条款</div>
                    <div class="menu-item ai-assistant-menu" onclick="openAIAssistant()" style="border-top: 1px solid #e0e0e0; margin-top: 0.5rem; padding-top: 0.5rem; color: #1a73e8; font-weight: 500;">
                        🤖 AI助手
                    </div>
                </div>
            </nav>
            <div class="sidebar-resize-handle" id="sidebar-resize-handle"></div>
            <!-- 右侧内容区 -->
            <div class="content-area" id="preview-pages">
                <!-- 目录0：制造规范封面（默认显示） -->
                <div class="preview-page" id="page-cover">
                    <div class="preview-header">
                        <h2>制造规范封面</h2>
                    </div>
                    <div class="preview-content" id="preview-content">
                        <!-- 项目信息部分 -->
                        <div class="preview-section">
                            <h3>项目信息</h3>
                            <div class="project-info-form">
                                <div class="form-group">
                                    <label for="project-name">项目名称</label>
                                    <select id="project-name" name="project-name" class="form-select">
                                        <option value="">请选择项目...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="production-site">生产工厂</label>
                                    <select id="production-site" name="production-site" class="form-select">
                                        <option value="">请选择工厂...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="product-family">产品系列</label>
                                    <select id="product-family" name="product-family" class="form-select">
                                        <option value="">请选择产品系列...</option>
                                    </select>
                                </div>
                                <!-- 修改生产线类型下拉菜单 -->
                                <div class="form-group">
                                    <label for="line-type">生产线类型</label>
                                    <select id="line-type" name="line-type" class="form-select">
                                        <option value="">请选择生产线类型...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="customer">客户</label>
                                    <select id="customer" name="customer" class="form-select">
                                        <option value="">请选择客户...</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="sop-date">SOP日期</label>
                                    <select id="sop-date" name="sop-date" class="form-select">
                                        <option value="">请选择SOP日期...</option>
                                    </select>
                                </div>
                                <!-- 新增字段：线号、APP负责人、AEP负责人 -->
                                <div class="form-group">
                                    <label for="line-number">线号</label>
                                    <select id="line-number" name="line-number" class="form-select">
                                        <option value="">请选择线号...</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="app-responsible">APP负责人</label>
                                    <select id="app-responsible" name="app-responsible" class="form-select">
                                        <option value="">请选择APP负责人...</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="aep-responsible">AEP负责人</label>
                                    <select id="aep-responsible" name="aep-responsible" class="form-select">
                                        <option value="">请选择AEP负责人...</option>
                                    </select>
                                </div>
                                <!-- 在项目信息表单中添加新字段 -->
                                <div class="form-group">
                                    <label for="pre-acceptance-date">产线预验收日期</label>
                                    <select id="pre-acceptance-date" name="pre-acceptance-date" class="form-select">
                                        <option value="">请选择产线预验收日期...</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="offline-debug-date">离线调试完成日期</label>
                                    <select id="offline-debug-date" name="offline-debug-date" class="form-select">
                                        <option value="">请选择离线调试完成日期...</option>
                                    </select>
                                </div>

                                <!-- 在项目信息表单中添加入厂日期字段 -->
                                <div class="form-group">
                                    <label for="factory-entry-date">入厂日期</label>
                                    <input type="text" 
                                           id="factory-entry-date" 
                                           name="factory-entry-date" 
                                           class="form-control"
                                           placeholder="yyyy-mm-dd"
                                           title="YYYY-MM-DD">
                                </div>
                            </div>
                            <div style="margin-top: 1rem;">
                                <a id="sharepoint-link" href="https://autoliv.sharepoint.com/sites/msteams_a5f551/Lists/Airbag%20New%20Project" target="_blank" style="color:#1a73e8;text-decoration:underline;">前往SharePoint查看完整项目信息</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 智能匹配页面 -->
                <div class="preview-page" id="page-smart-match" style="display:none;">
                    <div class="preview-header">
                        <h2>🎯 智能工艺匹配系统</h2>
                        <p style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">
                            基于零件组合智能匹配最适合的工艺类型和工站配置
                        </p>
                    </div>
                    <div class="preview-content">
                        <!-- 快速选择预设 -->
                        <div class="quick-select-section">
                            <div class="quick-select-title">🚀 快速选择常用配置</div>
                            <div class="quick-select-buttons">
                                <button class="quick-select-btn" data-preset="sab-basic">SAB基础型</button>
                                <button class="quick-select-btn" data-preset="sab-harness">SAB带线束</button>
                                <button class="quick-select-btn" data-preset="sab-full">SAB完整型</button>
                                <button class="quick-select-btn" data-preset="custom">自定义配置</button>
                            </div>
                        </div>

                        <!-- 历史记录 -->
                        <div class="history-section" id="history-section" style="display:none;">
                            <div class="quick-select-title">📚 最近分析记录</div>
                            <div id="history-list">
                                <!-- 历史记录将在这里显示 -->
                            </div>
                        </div>

                        <!-- 输入区域 -->
                        <div class="preview-section">
                            <h3>📋 输入信息</h3>
                            <div class="smart-match-input">
                                <div class="form-group">
                                    <label for="smart-product-family">产品族</label>
                                    <select id="smart-product-family" class="form-select">
                                        <option value="">请选择产品族...</option>
                                        <option value="SAB">SAB (安全气囊)</option>
                                        <option value="IC">IC (充气帘)</option>
                                        <option value="DAB">DAB (驾驶员气囊)</option>
                                        <option value="PAB">PAB (乘客气囊)</option>
                                        <option value="FCA">FCA (前排中央气囊)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>零件清单</label>
                                    <div style="margin-bottom: 1rem;">
                                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-bottom: 1rem;">
                                            <button type="button" class="quick-select-btn" onclick="selectSABCategory('A')">选择SAB-A</button>
                                            <button type="button" class="quick-select-btn" onclick="selectSABCategory('B')">选择SAB-B</button>
                                            <button type="button" class="quick-select-btn" onclick="selectSABCategory('C')">选择SAB-C</button>
                                            <button type="button" class="quick-select-btn" onclick="selectSABCategory('D')">选择SAB-D</button>
                                            <button type="button" class="quick-select-btn" onclick="selectSABCategory('E')">选择SAB-E</button>
                                            <button type="button" class="quick-select-btn" onclick="selectSABCategory('F')">选择SAB-F</button>
                                            <button type="button" class="quick-select-btn clear-btn" onclick="clearAllComponents()">清空选择</button>
                                        </div>
                                    </div>
                                    <div class="components-selection">
                                        <!-- SAB-A 预装配零件 -->
                                        <div class="component-category">
                                            <h6>🔧 预装配零件 (SAB-A)</h6>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-deflector" value="Deflector">
                                                <label for="comp-deflector">Deflector (导流片)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-inflator" value="inflator">
                                                <label for="comp-inflator">inflator (充气器)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-cushion" value="cushion">
                                                <label for="comp-cushion">cushion (气囊)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-soft-cover" value="soft cover">
                                                <label for="comp-soft-cover">soft cover (软盖)</label>
                                            </div>
                                        </div>

                                        <!-- SAB-B 线束装配零件 -->
                                        <div class="component-category">
                                            <h6>🔌 线束装配零件 (SAB-B)</h6>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-deflector-b" value="Deflector">
                                                <label for="comp-deflector-b">Deflector (导流片)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-inflator-b" value="inflator">
                                                <label for="comp-inflator-b">inflator (充气器)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-cushion-b" value="cushion">
                                                <label for="comp-cushion-b">cushion (气囊)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-harness" value="Harness">
                                                <label for="comp-harness">Harness (线束)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-soft-cover-b" value="soft cover">
                                                <label for="comp-soft-cover-b">soft cover (软盖)</label>
                                            </div>
                                        </div>

                                        <!-- SAB-C 扭矩检测零件 -->
                                        <div class="component-category">
                                            <h6>⚙️ 扭矩检测零件 (SAB-C)</h6>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-deflector-c" value="Deflector">
                                                <label for="comp-deflector-c">Deflector (导流片)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-inflator-c" value="inflator">
                                                <label for="comp-inflator-c">inflator (充气器)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-cushion-c" value="cushion">
                                                <label for="comp-cushion-c">cushion (气囊)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-harness-c" value="Harness">
                                                <label for="comp-harness-c">Harness (线束)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-bracket" value="Bracket">
                                                <label for="comp-bracket">Bracket (支架)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-nuts" value="Nuts">
                                                <label for="comp-nuts">Nuts (螺母)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-soft-cover-c" value="soft cover">
                                                <label for="comp-soft-cover-c">soft cover (软盖)</label>
                                            </div>
                                        </div>

                                        <!-- SAB-D 硬盖装配零件 -->
                                        <div class="component-category">
                                            <h6>🛡️ 硬盖装配零件 (SAB-D)</h6>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-deflector-d" value="Deflector">
                                                <label for="comp-deflector-d">Deflector (导流片)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-inflator-d" value="inflator">
                                                <label for="comp-inflator-d">inflator (充气器)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-cushion-d" value="cushion">
                                                <label for="comp-cushion-d">cushion (气囊)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-harness-d" value="Harness">
                                                <label for="comp-harness-d">Harness (线束)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-hard-cover" value="hard cover">
                                                <label for="comp-hard-cover">hard cover (硬盖)</label>
                                            </div>
                                        </div>

                                        <!-- SAB-E 外壳装配零件 -->
                                        <div class="component-category">
                                            <h6>🏠 外壳装配零件 (SAB-E)</h6>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-deflector-e" value="Deflector">
                                                <label for="comp-deflector-e">Deflector (导流片)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-inflator-e" value="inflator">
                                                <label for="comp-inflator-e">inflator (充气器)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-cushion-e" value="cushion">
                                                <label for="comp-cushion-e">cushion (气囊)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-bracket-e" value="Bracket">
                                                <label for="comp-bracket-e">Bracket (支架)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-nuts-e" value="Nuts">
                                                <label for="comp-nuts-e">Nuts (螺母)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-housing" value="housing">
                                                <label for="comp-housing">housing (外壳)</label>
                                            </div>
                                        </div>

                                        <!-- SAB-F 3D热成型零件 -->
                                        <div class="component-category">
                                            <h6>🔥 3D热成型零件 (SAB-F)</h6>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-deflector-f" value="Deflector">
                                                <label for="comp-deflector-f">Deflector (导流片)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-inflator-f" value="inflator">
                                                <label for="comp-inflator-f">inflator (充气器)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-cushion-f" value="cushion">
                                                <label for="comp-cushion-f">cushion (气囊)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-hard-cover-f" value="hard cover">
                                                <label for="comp-hard-cover-f">hard cover (硬盖)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-housing-f" value="housing">
                                                <label for="comp-housing-f">housing (外壳)</label>
                                            </div>
                                            <div class="component-item">
                                                <input type="checkbox" id="comp-3d-heat" value="3D heat">
                                                <label for="comp-3d-heat">3D heat (3D热成型)</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="project-requirements">项目特殊要求</label>
                                    <textarea id="project-requirements" rows="3" placeholder="请输入项目的特殊要求或备注..."></textarea>
                                </div>

                                <button id="analyze-btn" class="preview-button">🔍 智能分析</button>
                            </div>
                        </div>

                        <!-- 匹配结果区域 -->
                        <div class="preview-section" id="match-result-section" style="display:none;">
                            <h3>🎯 智能匹配结果</h3>

                            <!-- 统计卡片 -->
                            <div class="stats-grid" id="stats-grid">
                                <!-- 统计卡片将在这里显示 -->
                            </div>

                            <div id="match-result-content">
                                <!-- 匹配结果将在这里显示 -->
                            </div>
                        </div>

                        <!-- 工艺流程图区域 -->
                        <div class="preview-section" id="process-flow-section" style="display:none;">
                            <h3>🏭 工艺流程概览</h3>
                            <div id="process-flow-content">
                                <!-- 流程图将在这里显示 -->
                            </div>
                        </div>

                        <!-- 工站详情区域 -->
                        <div class="preview-section" id="station-details-section" style="display:none;">
                            <h3>📋 详细工艺规范</h3>
                            <div class="collapsible-section">
                                <div class="collapsible-header" onclick="toggleAllStations()">
                                    <h5>🔧 展开/收起所有工站详情</h5>
                                    <span class="collapsible-toggle" id="all-stations-toggle">▼</span>
                                </div>
                            </div>

                            <!-- 操作按钮组 -->
                            <div style="margin-bottom: 1.5rem; text-align: center;">
                                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 1rem;">
                                    <button id="apply-to-process-btn" onclick="applyToProcessPage()" class="preview-button" style="background: #52c41a; border-color: #52c41a;">
                                        🚀 应用到工艺页面
                                    </button>
                                    <button id="export-pdf-btn" onclick="exportToPDF()" class="preview-button" style="background: #ff4d4f; border-color: #ff4d4f;">
                                        📄 导出PDF
                                    </button>
                                    <button id="export-word-btn" onclick="exportToWord()" class="preview-button" style="background: #1890ff; border-color: #1890ff;">
                                        📝 导出Word
                                    </button>
                                    <button id="export-json-btn" onclick="exportToJSON()" class="preview-button" style="background: #722ed1; border-color: #722ed1;">
                                        💾 导出数据
                                    </button>
                                    <button id="print-btn" onclick="printReport()" class="preview-button" style="background: #fa8c16; border-color: #fa8c16;">
                                        🖨️ 打印
                                    </button>
                                </div>
                                <p style="margin: 0; font-size: 0.9rem; color: #666;">
                                    将分析结果应用到工艺页面或导出为文档
                                </p>
                            </div>

                            <div id="station-details-content">
                                <!-- 工站详情将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 目录1：产品信息 -->
                <div class="preview-page" id="page-product" style="display:none;">
                    <div class="preview-header">
                        <h2>产品信息（APP）</h2>
                    </div>
                    <div class="preview-content">
                        <div class="template-panel">
                            <div class="product-selection">
                                <div class="form-group">
                                    <label>产品类型</label>
                                    <select id="productType">
                                        <option value="">请选择产品类型...</option>
                                        <option value="SAB">SAB</option>
                                        <option value="IC">IC</option>
                                        <option value="DAB">DAB</option>
                                        <option value="PAB">PAB</option>
                                        <option value="FCA">FCA</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>模板分类</label>
                                    <select id="templateType">
                                        <option value="">请选择模板分类...</option>
                                        <option value="A">A</option>
                                        <option value="B">B</option>
                                        <option value="C">C</option>
                                        <option value="D">D</option>
                                        <option value="E">E</option>
                                        <option value="F">F</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="parts-table">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>新项目名称</th>
                                            <th>零件编号</th>
                                            <th>零件名称</th>
                                            <th>单位用量</th>
                                            <th>包装尺寸(mm)</th>
                                            <th>单位包装数量</th>
                                            <th>零件状态</th>
                                            <th>共享项目名称</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="partsTableBody"></tbody>
                                </table>
                                <button id="addPartRow" class="btn">添加零件</button>
                            </div>
                            
                            <button id="generatePrompt" class="btn primary">一键生成</button>
                        </div>
                        
                        <!-- 产品附件上传区域 -->
                        <div class="preview-section" id="product-attachments-section">
                            <h3>📎 产品附件</h3>
                            <div class="attachments-container">
                                <!-- 产品爆炸图 -->
                                <div class="attachment-category">
                                    <div class="attachment-category-header">
                                        <h4>🔧 产品爆炸图</h4>
                                        <div class="attachment-category-controls">
                                            <input type="file" id="explosion-diagram-input" accept="image/*,.pdf,.doc,.docx" multiple style="display: none;" onchange="handleCategoryFileUpload('explosion-diagram', event)">
                                            <button onclick="document.getElementById('explosion-diagram-input').click()" class="upload-btn">
                                                📁 上传文件
                                            </button>
                                            <button onclick="clearCategoryAttachments('explosion-diagram')" class="clear-btn" style="display: none;">
                                                🗑️ 清空
                                            </button>
                                        </div>
                                    </div>
                                    <div class="attachment-category-content" id="explosion-diagram-content">
                                        <div class="upload-placeholder">
                                            <div class="placeholder-icon">📋</div>
                                            <p>点击上传产品爆炸图</p>
                                            <small>支持 JPG, PNG, PDF, DOC, DOCX 格式</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 产品折叠图 -->
                                <div class="attachment-category">
                                    <div class="attachment-category-header">
                                        <h4>📐 产品折叠图</h4>
                                        <div class="attachment-category-controls">
                                            <input type="file" id="folding-diagram-input" accept="image/*,.pdf,.doc,.docx" multiple style="display: none;" onchange="handleCategoryFileUpload('folding-diagram', event)">
                                            <button onclick="document.getElementById('folding-diagram-input').click()" class="upload-btn">
                                                📁 上传文件
                                            </button>
                                            <button onclick="clearCategoryAttachments('folding-diagram')" class="clear-btn" style="display: none;">
                                                🗑️ 清空
                                            </button>
                                        </div>
                                    </div>
                                    <div class="attachment-category-content" id="folding-diagram-content">
                                        <div class="upload-placeholder">
                                            <div class="placeholder-icon">📋</div>
                                            <p>点击上传产品折叠图</p>
                                            <small>支持 JPG, PNG, PDF, DOC, DOCX 格式</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- 产品总成图 -->
                                <div class="attachment-category">
                                    <div class="attachment-category-header">
                                        <h4>🏗️ 产品总成图</h4>
                                        <div class="attachment-category-controls">
                                            <input type="file" id="assembly-diagram-input" accept="image/*,.pdf,.doc,.docx" multiple style="display: none;" onchange="handleCategoryFileUpload('assembly-diagram', event)">
                                            <button onclick="document.getElementById('assembly-diagram-input').click()" class="upload-btn">
                                                📁 上传文件
                                            </button>
                                            <button onclick="clearCategoryAttachments('assembly-diagram')" class="clear-btn" style="display: none;">
                                                🗑️ 清空
                                            </button>
                                        </div>
                                    </div>
                                    <div class="attachment-category-content" id="assembly-diagram-content">
                                        <div class="upload-placeholder">
                                            <div class="placeholder-icon">📋</div>
                                            <p>点击上传产品总成图</p>
                                            <small>支持 JPG, PNG, PDF, DOC, DOCX 格式</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 全局管理按钮 -->
                            <div style="margin-top: 1rem; text-align: center;">
                                <button onclick="clearAllCategoryAttachments()" class="btn" style="background: #ff7875; color: white; margin-right: 0.5rem;">
                                    🗑️ 清空所有附件
                                </button>
                                <button onclick="exportAllCategoryAttachments()" class="btn" style="background: #722ed1; color: white;">
                                    💾 导出附件数据
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 目录2：工艺要求 -->
                <div class="preview-page" id="page-process" style="display:none;">
                    <div class="preview-header">
                        <h2>工艺要求（APP）</h2>
                    </div>
                    <div class="preview-content">

                        <!-- 产线Layout上传区域 -->
                        <div class="preview-section" id="process-layout-section">
                            <h3>🏭 产线Layout</h3>
                            <div style="display: flex; margin-bottom: 20px; gap: 20px; min-height: 400px;">
                                <!-- 左侧图片上传区域 -->
                                <div style="flex: 2; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; flex-direction: column;">
                                    <h4 style="margin-bottom: 15px; font-size: 16px; color: #333;">工艺布局图</h4>
                                    <div id="process-layout-preview" style="flex: 1; overflow: hidden; text-align: center; margin-bottom: 15px; min-height: 300px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                        <div style="color: #999;">
                                            <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                                            <p>点击下方按钮上传产线Layout图</p>
                                            <small>支持 JPG, PNG 格式图片</small>
                                        </div>
                                    </div>
                                    <div style="display: flex; justify-content: center; gap: 10px;">
                                        <input type="file" id="process-layout-input" accept="image/*" style="display: none;" onchange="handleProcessLayoutUpload(event)">
                                        <button onclick="document.getElementById('process-layout-input').click()" class="btn primary">
                                            📁 上传图片
                                        </button>
                                        <button onclick="deleteProcessLayoutImage()" class="btn" style="background: #ff7875; color: white; display: none;" id="delete-process-layout-btn">
                                            🗑️ 删除图片
                                        </button>
                                    </div>
                                </div>

                                <!-- 右侧参数区域 -->
                                <div style="flex: 1; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                    <h4 style="margin-bottom: 15px; font-size: 16px; color: #333;">产线参数</h4>
                                    <div style="display: flex; flex-direction: column; gap: 15px;">
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">长度(mm):</label>
                                            <input type="text" id="process-line-length" placeholder="请输入产线长度" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">宽度(mm):</label>
                                            <input type="text" id="process-line-width" placeholder="请输入产线宽度" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">高度(mm):</label>
                                            <input type="text" id="process-line-height" placeholder="请输入产线高度" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                        </div>
                                        <div>
                                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">产线节拍(s):</label>
                                            <input type="text" id="process-line-takt" placeholder="请输入产线节拍" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="preview-section">
                            <h3>🏭 工艺工站列表</h3>
                            <div style="margin-bottom: 1rem; padding: 0.8rem; background: #f0f8ff; border-radius: 4px; border-left: 4px solid #1a73e8;">
                                <p style="margin: 0; font-size: 0.9rem; color: #333;">
                                    <strong>提示：</strong>工站将根据AI生成内容自动创建。您也可以手动添加新的工站。
                                </p>
                            </div>
                            <div id="stations-list" style="min-height: 100px;">
                                <!-- 工站将通过JavaScript动态生成 -->
                            </div>
                            <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button onclick="toggleAllStations(true)" class="btn" style="background: #faad14; color: white;">折叠所有</button>
                                <button onclick="toggleAllStations(false)" class="btn" style="background: #52c41a; color: white;">展开所有</button>
                                <button onclick="clearAllStations()" class="btn" style="background: #ff7875; color: white;">清空所有工站</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 目录3：设备要求 -->
                <div class="preview-page" id="page-equipment" style="display:none;">
                    <div class="preview-header">
                        <h2>设备要求（AEP）</h2>
                    </div>
                    <div class="preview-content">
                        <div class="preview-section">
                            <h3>设备工站列表</h3>
                            <div style="margin-bottom: 1rem; padding: 0.8rem; background: #f0f8ff; border-radius: 4px; border-left: 4px solid #1a73e8;">
                                <p style="margin: 0; font-size: 0.9rem; color: #333;">
                                    <strong>提示：</strong>设备工站将根据AI生成内容自动创建。您也可以手动添加新的设备工站。
                                </p>
                            </div>
                            <div id="equipment-stations-list" style="min-height: 100px;">
                                <!-- 设备工站将通过JavaScript动态生成 -->
                            </div>
                            <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button onclick="addNewEquipmentStation()" class="btn primary">添加设备工站</button>
                                <button onclick="toggleAllStations(true)" class="btn" style="background: #faad14; color: white;">折叠所有</button>
                                <button onclick="toggleAllStations(false)" class="btn" style="background: #52c41a; color: white;">展开所有</button>
                                <button onclick="clearAllStations()" class="btn" style="background: #ff7875; color: white;">清空所有工站</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 目录4：设备夹具清单 -->
                <div class="preview-page" id="page-fixture" style="display:none;">
                    <div class="preview-header">
                        <h2>🔧 设备夹具清单（AEP）</h2>
                        <p>根据工艺类型显示对应的夹具和设备清单</p>
                    </div>

                    <div class="preview-content">
                        <!-- 筛选控制区域 -->
                        <div class="fixture-controls">
                            <div class="control-group">
                                <label>SAB类别:</label>
                                <select id="sab-type-filter" onchange="filterFixtures()">
                                    <option value="">全部类别</option>
                                    <option value="SAB-A">SAB-A (预装配)</option>
                                    <option value="SAB-B">SAB-B (线束装配)</option>
                                    <option value="SAB-C">SAB-C (扭矩检测)</option>
                                    <option value="SAB-D">SAB-D (硬盖装配)</option>
                                    <option value="SAB-F">SAB-F (3D热成型)</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <label>工站筛选:</label>
                                <select id="station-filter" onchange="filterFixtures()">
                                    <option value="">全部工站</option>
                                </select>
                            </div>

                            <div class="control-group">
                                <label>搜索:</label>
                                <input type="text" id="fixture-search" placeholder="搜索夹具或设备名称..." onkeyup="searchFixtures()">
                            </div>

                            <div class="control-group">
                                <button onclick="exportFixtureList()" class="btn primary">📄 导出清单</button>
                                <button onclick="printFixtureList()" class="btn">🖨️ 打印</button>
                                <button onclick="resetFixtureFilters()" class="btn">🔄 重置筛选</button>
                            </div>
                        </div>

                        <!-- 统计概览 -->
                        <div class="fixture-summary">
                            <div class="summary-card">
                                <div class="summary-number" id="total-fixtures">0</div>
                                <div class="summary-label">夹具总数</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="total-stations">0</div>
                                <div class="summary-label">工站数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="total-brands">0</div>
                                <div class="summary-label">品牌数量</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-number" id="active-categories">0</div>
                                <div class="summary-label">活跃类别</div>
                            </div>
                        </div>

                        <!-- 夹具清单表格 -->
                        <div class="fixture-table-container">
                            <table class="fixture-table" id="fixture-table">
                                <thead>
                                    <tr>
                                        <th>SAB类别</th>
                                        <th>工站</th>
                                        <th>设备/夹具名称</th>
                                        <th>品牌</th>
                                        <th>型号</th>
                                        <th>数量</th>
                                        <th>内容描述</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="fixture-table-body">
                                    <!-- 夹具数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 详细信息面板 -->
                        <div class="fixture-details" id="fixture-details" style="display:none;">
                            <div class="fixture-details-header">
                                <h3>夹具详细信息</h3>
                                <button onclick="closeFixtureDetails()" class="close-btn">✕</button>
                            </div>
                            <div id="fixture-details-content">
                                <!-- 详细信息内容 -->
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 目录5：通用条款 -->
                <div class="preview-page" id="page-terms">
                    <div class="preview-header">
                        <h2>通用条款</h2>
                    </div>
                    <div class="preview-content">
                        <div class="preview-section">
                            <h3>5. 通用条款</h3>
                            <div class="section-content terms-content">
                                <!-- 设备交付标准表格 -->
                                <div class="terms-table">
                                    <p class="note">*设备供应商交付的设备、工装、夹具、程序改造等均须符合如下标准与文件:</p>
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>适用阶段</th>
                                                <th>文件名称</th>
                                                <th>类别</th>
                                                <th>编号</th>
                                                <th>要求</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>报价</td>
                                                <td>《中国区气囊生产线方案》</td>
                                                <td>项目方案</td>
                                                <td>E6356772</td>
                                                <td>共线夹具与手动线：技术交流后3天内提供<br>自动线：技术交流后5天内提供</td>
                                            </tr>
                                            <tr>
                                                <td>报价、设计</td>
                                                <td>《中国区气囊线设备夹具清单》</td>
                                                <td>Main BOM</td>
                                                <td>E6356423</td>
                                                <td></td>
                                            </tr>
                                            <!-- 继续添加其他标准文件行... -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 碳排放说明 -->
                                <div class="carbon-note">
                                    <p>*设备碳排放量需在设计初期评估计算，公式：年碳排放量=设备总功率*碳排放系数*工作时间/年(21.5H*250day)*设备利用率(60%)</p>
                                    <p>碳排放系数： 上海 0.42 tCO2/MWh；台湾 0.509 tCO2/MWh；其他地区 0.581 tCO2/MWh</p>
                                </div>

                                <!-- 5.1 方案确认阶段 -->
                                <div class="terms-section">
                                    <h4>5.1 方案确认阶段</h4>
                                    <ol>
                                        <li>设备供应商严格按E6356772、E6356423、E6361925格式及交付时间要求提供对应的方案、时间计划、Main BOM</li>
                                        <li>不能如期交付项目方案、时间计划、Main BOM且未获得Autoliv同意的设备供应商，可视为放弃参与此项目</li>
                                        <li>设备供应商提供的产线交付时间充分考虑商务沟通周期，满足"制造规范封面"中"要求完成日期"</li>
                                    </ol>
                                </div>

                                <!-- 5.2 报价阶段 -->
                                <div class="terms-section">
                                    <h4>5.2 报价阶段</h4>
                                    <div class="pricing-tables">
                                        <div class="left-table">
                                            <h5>涉及新线或多项目共线同步开发时，报价单要求按下表分行提供：</h5>
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>项目</th>
                                                        <th>内容</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>设备主体</td>
                                                        <td>STXX XX设备主体费用</td>
                                                    </tr>
                                                    <tr>
                                                        <td>A项目</td>
                                                        <td>STXX设备 A项目专用夹具费用</td>
                                                    </tr>
                                                    <tr>
                                                        <td>B项目</td>
                                                        <td>STXX设备 B项目专用夹具费用</td>
                                                    </tr>
                                                    <tr>
                                                        <td>C项目</td>
                                                        <td>STXX设备 C项目专用夹具费用</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="right-table">
                                            <h5>报价单按以下时间要求提供：</h5>
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>类别</th>
                                                        <th>提供报价时间</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>共线夹具</td>
                                                        <td>提出报价需求后2日内</td>
                                                    </tr>
                                                    <tr>
                                                        <td>手动线</td>
                                                        <td>提出报价需求后3日内</td>
                                                    </tr>
                                                    <tr>
                                                        <td>自动线</td>
                                                        <td>提出报价需求后5日内</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- 5.3 设计与制造阶段 -->
                                <div class="terms-section">
                                    <h4>5.3 设计与制造阶段</h4>
                                    <ol>
                                        <li>供应定点后按《E6361925_中国区气囊线工装制作计划表》提供详细计划，要求符合Autoliv"产线制造规范"定义的预验收、交付、调试完成的时间</li>
                                        <li>设备夹具制造前按《E6309004_中国区气囊设备设计评审与验收指导书》与Autoliv设计评审，设计评审通过不代表可免除设备供应商保证设备功能完善的责任</li>
                                        <li>设计评审通过后，设备供应商按设计锁定版《E6356423_中国区气囊线设备夹具清单》，经Autoliv确认后启动制作</li>
                                        <li>设备供应商应至少每2周主动向Autoliv订单申请人提供1次项目进展说明，含标准件货期、加工件货期、风险、施工情况等信息</li>
                                        <li>设备夹具设计制造都应该符合E6283425、E6283738、E6283677、E6744785、E6918555等通用型技术规范，包括但不限于：
                                            <ul>
                                                <li>设备供应商需要在对应工装夹具上刻印标识</li>
                                                <li>除特殊要求外，设备工装夹具等都需要去除尖角、飞边、毛刺等处理，避免划伤</li>
                                                <li>除特殊要求外，设备上方都需要配置《E6283677_中国区气囊设备标准零部件清单》中要求的照明灯具</li>
                                                <li>设备整体结构设计应尽量紧凑但必须保留足够的空间方便生产、日常检查、维修、保养</li>
                                                <li>设备的所有运动零部件应动作平稳且有良好的润滑(必要时提供润滑操作说明)，无干涉、卡滞现象</li>
                                                <li>设备的气路、液路、真空回路连接简明、可靠，在最大工作压力120％的压力状态下无泄漏现象</li>
                                                <li>工装夹具含点检、归零样件、工具，必须方便生产过程中放置与拿取</li>
                                                <li>设备夹具快速换型和点检校验时方便拿取和更换，固定在设备上的点检、归零样件应留出足够空间方便测量和校验</li>
                                                <li>需保持整线设备产品方向基准的一致性，针对分批交付的设备，后交付的设备产品方向基准必须严格遵守先交付设备的方向基准</li>
                                            </ul>
                                        </li>
                                    </ol>
                                </div>

                                <!-- 5.4 机械安全评估要求 -->
                                <div class="terms-section">
                                    <h4>5.4 机械安全评估要求</h4>
                                    <div class="safety-requirements">
                                        <p class="note">*设备供应商必须在工装、夹具、设备开发过程中完全遵守《E6744785_中国区气囊设备安全通用技术规范》的要求</p>
                                        <p class="note">*设备供应商应按《E6309004_中国区气囊设备设计评审与验收指导书》在设计、制造、验收等阶段提供资料并参与相应评审及验收活动</p>
                                        <p class="note">*机械安全评估活动中相关评估报告制作、安全审核、安全验收等环节，设备供应商须有满足AS332中对应等级L5资质人员参与或请由资质的第三方完成</p>

                                        <div class="safety-section">
                                            <h5>1. 新设备或涉及到机械安全的设备改造</h5>
                                            <ol>
                                                <li>报价环节：Autoliv订单申请人将已经完成初步风险评估的《TCH-ASS001-Machine Safety Risk Assessment》连同产线要求规范SoW发给设备供应商</li>
                                                <li>设计环节：设备供应商应进行详细的风险评估并采取必要的措施进行风险降低，完善《TCH-ASS001-Machine Safety Risk Assessment》"Cover page"和"Risk-assessment"部分
                                                    内容，如果风险降低措施涉及到安全控制系统(SCS)，须设计详细的安全控制系统电气原理图并完成"PL Report"部分内容，确认安全控制系统PL≥PLr</li>
                                                <li>设计评审和批准环节：在设备制造开始前，根据AS332-5.8中定义的相应等级资质人员针对安全防护措施进行设计评审，并获得Autoliv的批准。设备供应商应在设计评审前
                                                    完成"安全控制系统电气原理图、安全回路模块图、《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"和"PL Report"内容）"</li>
                                                <li>预验收环节：
                                                    <ul>
                                                        <li>预验收前，设备供应商应根据设备实际情况更新"《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"和"PL Report"部分）、安全控制系统
                                                            电气原理图、安全回路模块图"，并验证安全防护的功能</li>
                                                        <li>预验收时，设备供应商向Autoliv相关人员提供"《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"和"PL Report"部分）、安全控制系统
                                                            电气原理图、安全回路模块图".Autoliv相关人员会完成预验收检查表里安全相关项目的检查</li>
                                                    </ul>
                                                </li>
                                                <li>终验收环节：在批准设备使用前，设备供应商应配合Autoliv相关人员对安全防护系统进行最终确认，确认预验收《TCH-ASS001-Machine Safety Risk Assessment》中LOP
                                                    都已关闭，作为向供应商进一步付款前的依据</li>
                                            </ol>
                                        </div>

                                        <div class="safety-section">
                                            <h5>2. 工装夹具类、软件改造类</h5>
                                            <p>在设计评审和验收环节，设备供应商应配合Autoliv结合《中国区气囊设备设计评审与验收报告》中安全检查项对安全防护措施进行确认</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 5.5 验收 -->
                                <div class="terms-section">
                                    <h4>5.5 验收</h4>
                                    <ol>
                                        <li>设备完成组装和调试后，设备供应商应按《E6309004_中国区气囊设备设计评审与验收指导书》对设备、夹具等进行自验收</li>
                                        <li>设备供应商应自验收完成后，应将《E6309135_中国区气囊设备设计评审与验收报告》(供应商自查)签字扫描版、自查LOP等发送给Autoliv订单申请人</li>
                                        <li>Autoliv订单申请人根据供应商自验收结果判定是否达到预验收条件，达到后组织预验收会议</li>
                                        <li>设备供应商根据Autoliv要求的到货时间安排发货，到货当天须提供盖章的送货单《MRO Delivery Note》，并与Autoliv订单申请人完成物品核对收货</li>
                                        <li>原则上设备验收地需要与合同约定的地点保持一致。如有不符，Autoliv有权要求供应商承担验收及设备开发过程中额外产生的费用(差旅、工时损失等)</li>
                                        <li>若因设备供应商未客观反馈产线状态(如未自验收或提供虚假报告等)而导致验收失败，Autoliv有权要求供应商承担再次验收额外产生的费用(差旅、工时损失等)</li>
                                    </ol>
                                </div>

                                <!-- 5.6 交机资料、备件、工具交付要求 -->
                                <div class="terms-section">
                                    <h4>5.6 交机资料、备件、工具交付要求</h4>
                                    <ol>
                                        <li>除特殊约定外，设备供应商为Autoliv开发的气囊设备、工装、夹具的所有文件资料（机械图、电气图、软件等各类文件）版权归Autoliv所有</li>
                                        <li>设备供应商按《E6309004_中国区气囊设备设计评审与验收指导书》_交机资料要求及《E6918555_中国区气囊设备图纸技术规范》提供相关设备、工装、夹具的交机资料、备件、工具等</li>
                                    </ol>
                                </div>

                                <!-- 5.7 培训及售后服务 -->
                                <div class="terms-section">
                                    <h4>5.7 培训及售后服务</h4>
                                    <ol>
                                        <li>设备交付后，设备供应商应按《E6309004_中国区气囊设备设计评审与验收指导书》提供相关培训</li>
                                        <li>除特殊约定，设备自交付日起设备供应商提供24个月免费保修服务，终生维修服务</li>
                                        <li>保修期内，非Autoliv原因造成的设备零件损坏(如零件质量问题或设计不合理)由设备供应商免费提供并维修</li>
                                        <li>保修期内，卖方Autoliv现场服务人员10分钟内到达问题现场，不在Autoliv现场的服务人员10分钟内远程响应并在2小时内到达问题现场</li>
                                        <li>保修期过后，卖方实时响应且24小时内技术人员可到达问题现场</li>
                                    </ol>
                                </div>

                                <!-- 将继续添加5.5-5.7部分 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="preview-actions" style="margin-top:2rem;">
                    <button id="save-button" class="preview-button" disabled>保存回答</button>
                </div>

                <!-- 产品信息模板生成器 -->
                <div id="product-info" class="content-section" style="display:none;">
                    <div class="template-panel">
                        <h3>产品模板生成器</h3>
                        <div class="product-selection">
                            <div class="form-group">
                                <label>产品类型</label>
                                <select id="productType">
                                    <option value="">请选择产品类型...</option>
                                    <option value="SAB">SAB</option>
                                    <option value="IC">IC</option>
                                    <option value="DAB">DAB</option>
                                    <option value="PAB">PAB</option>
                                    <option value="FCA">FCA</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>模板分类</label>
                                <select id="templateType">
                                    <option value="">请选择模板分类...</option>
                                    <option value="A">A</option>
                                    <option value="B">B</option>
                                    <option value="C">C</option>
                                    <option value="D">D</option>
                                    <option value="E">E</option>
                                    <option value="F">F</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="parts-table">
                            <table>
                                <thead>
                                    <tr>
                                        <th>新项目名称</th>
                                        <th>零件编号</th>
                                        <th>零件名称</th>
                                        <th>单位用量</th>
                                        <th>包装尺寸(mm)</th>
                                        <th>单位包装数量</th>
                                        <th>零件状态</th>
                                        <th>共享项目名称</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="partsTableBody">
                                </tbody>
                            </table>
                            <button id="addPartRow" class="btn">添加零件</button>
                        </div>
                        
                        <button id="generatePrompt" class="btn primary">一键生成</button>
                    </div>
                </div>
            </div>
            <div class="resize-handle" id="resize-handle"></div>
        </div>
        <!-- 聊天界面移除，改为浮动窗 -->
        <!-- <div class="chat-container"> ... </div> -->
    </div>

    <!-- 新增：AI助手唤醒按钮 -->
    <div class="ai-assistant-trigger" id="ai-assistant-trigger" title="打开AI助手 (快捷键: Ctrl+Shift+A)">
        <div class="ai-icon">🤖</div>
        <div class="ai-text">AI助手</div>
        <div class="ai-pulse" id="ai-pulse"></div>
    </div>

    <!-- 新增：浮动聊天窗 -->
    <div class="floating-chat" id="floating-chat">
        <div class="floating-chat-header" id="floating-chat-header">
            Linespec 问答系统
            <span>
                <button class="floating-chat-max" id="floating-chat-max" title="最大化/还原">⛶</button>
                <button class="floating-chat-close" id="floating-chat-close" title="关闭">×</button>
            </span>
        </div>
        <div class="chat-container" style="box-shadow:none;border-radius:0 0 15px 15px;">
            <div class="chat-box" id="chat-box"></div>
            <div class="loading" id="loading">AI正在思考中</div>
            <div class="input-container">
                <input type="text" id="question-input" placeholder="请输入您的问题...按回车发送">
                <button id="send-button">发送</button>
            </div>
        </div>
    </div>

    <script>
        const chatBox = document.getElementById('chat-box');
        const questionInput = document.getElementById('question-input');
        const sendButton = document.getElementById('send-button');
        const loading = document.getElementById('loading');
        const previewContent = document.getElementById('preview-content');
        const saveButton = document.getElementById('save-button');
        const partsContent = document.getElementById('parts-content');
        const processContent = document.getElementById('process-content');

        // 内联处理AI生成的工站信息（备用方案）
        function handleAIGeneratedStationsInline(data) {
            console.log('[DEBUG] 使用内联handleAIGeneratedStations，数据:', data);

            // 处理工艺工站
            if (data.process_stations && data.process_stations.length > 0) {
                console.log('[DEBUG] 内联处理工艺工站，数量:', data.process_stations.length);

                // 检查是否有StationGenerator
                if (typeof StationGenerator !== 'undefined') {
                    // 确保stationGenerator实例存在
                    if (!window.stationGenerator) {
                        window.stationGenerator = new StationGenerator();
                        console.log('[DEBUG] 创建新的StationGenerator实例');
                    }

                    // 生成工艺工站
                    window.stationGenerator.generateProcessStations(data.process_stations);
                    console.log('[DEBUG] 内联处理：工艺工站生成完成');

                    // 显示成功消息
                    if (typeof showNotification === 'function') {
                        showNotification(`成功识别到 ${data.process_stations.length} 个工艺工站`, 'success');
                    }
                } else {
                    console.error('[ERROR] StationGenerator类未定义');
                }
            }

            // 处理设备工站
            if (data.equipment_stations && data.equipment_stations.length > 0) {
                console.log('[DEBUG] 内联处理设备工站，数量:', data.equipment_stations.length);

                if (typeof StationGenerator !== 'undefined') {
                    // 确保stationGenerator实例存在
                    if (!window.stationGenerator) {
                        window.stationGenerator = new StationGenerator();
                        console.log('[DEBUG] 创建新的StationGenerator实例');
                    }

                    // 生成设备工站
                    window.stationGenerator.generateEquipmentStations(data.equipment_stations);
                    console.log('[DEBUG] 内联处理：设备工站生成完成');

                    // 显示成功消息
                    if (typeof showNotification === 'function') {
                        showNotification(`成功识别到 ${data.equipment_stations.length} 个设备工站`, 'success');
                    }
                } else {
                    console.error('[ERROR] StationGenerator类未定义');
                }
            }
        }

        // 全局handleAIGeneratedStations函数（确保总是可用）
        function handleAIGeneratedStations(data) {
            console.log('[DEBUG] 全局handleAIGeneratedStations被调用，数据:', data);

            // 直接使用内联处理
            handleAIGeneratedStationsInline(data);
        }

        // 在 script 标签开始处添加全局变量
        let selectedProject = null;

        function formatText(text) {
            // 检测是否包含表格式数据
            if (text.includes('|') && text.includes('\n')) {
                const lines = text.trim().split('\n');
                const headers = lines[0].split('|').map(h => h.trim()).filter(h => h);
                const rows = lines.slice(1).map(line => 
                    line.split('|').map(cell => cell.trim()).filter(cell => cell)
                ).filter(row => row.length > 0);

                let table = '<table><thead><tr>';
                headers.forEach(header => {
                    table += `<th>${header}</th>`;
                });
                table += '</tr></thead><tbody>';

                rows.forEach(row => {
                    table += '<tr>';
                    row.forEach(cell => {
                        table += `<td>${cell}</td>`;
                    });
                    table += '</tr>';
                });

                table += '</tbody></table>';
                return table;
            }

            // 普通文本处理
            return text
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;')
                .replace(/\n/g, '<br>');
        }

        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
            // 展开/收起逻辑
            const maxLen = 600;
            if (!isUser && content.length > maxLen) {
                const shortText = content.slice(0, maxLen) + '...';
                messageDiv.innerHTML = `<span class="msg-short">${formatText(shortText)}</span>
                    <span class="msg-full" style="display:none">${formatText(content)}</span>
                    <span class="expand-btn">展开</span>`;
                messageDiv.querySelector('.expand-btn').onclick = function() {
                    const short = messageDiv.querySelector('.msg-short');
                    const full = messageDiv.querySelector('.msg-full');
                    if (full.style.display === 'none') {
                        full.style.display = '';
                        short.style.display = 'none';
                        this.textContent = '收起';
                    } else {
                        full.style.display = 'none';
                        short.style.display = '';
                        this.textContent = '展开';
                    }
                };
            } else {
                messageDiv.innerHTML = formatText(content);
            }
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
        }

        async function sendQuestion() {
            const question = questionInput.value.trim();
            if (!question) return;

            addMessage(question, true);
            questionInput.value = '';
            loading.style.display = 'block';
            sendButton.disabled = true;
            questionInput.disabled = true;
            saveButton.disabled = true;

            try {
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question }),
                });

                const data = await response.json();

                if (response.ok) {
                    const answer = data.answer;
                    const parsedContent = data.parsed_content;
                    const processStations = data.process_stations;
                    const equipmentStations = data.equipment_stations;

                    console.log('[DEBUG] AI响应数据:', {
                        answer_length: answer ? answer.length : 0,
                        parsedContent: parsedContent,
                        processStations_count: processStations ? processStations.length : 0,
                        equipmentStations_count: equipmentStations ? equipmentStations.length : 0
                    });

                    addMessage(answer);

                    // 处理工站信息（优先级最高）
                    const hasProcessStations = processStations && processStations.length > 0;
                    const hasEquipmentStations = equipmentStations && equipmentStations.length > 0;

                    if (hasProcessStations || hasEquipmentStations) {
                        console.log('[DEBUG] 调用handleAIGeneratedStations');
                        console.log('[DEBUG] 工艺工站数量:', hasProcessStations ? processStations.length : 0);
                        console.log('[DEBUG] 设备工站数量:', hasEquipmentStations ? equipmentStations.length : 0);

                        if (hasProcessStations) {
                            console.log('[DEBUG] 工艺工站数据:', processStations);
                        }
                        if (hasEquipmentStations) {
                            console.log('[DEBUG] 设备工站数据:', equipmentStations);
                        }

                        // 处理AI生成的工站信息
                        if (typeof handleAIGeneratedStations === 'function') {
                            handleAIGeneratedStations({
                                process_stations: processStations || [],
                                equipment_stations: equipmentStations || []
                            });
                        } else {
                            console.error('[ERROR] handleAIGeneratedStations函数未定义，使用内联处理');
                            // 内联处理逻辑
                            handleAIGeneratedStationsInline({
                                process_stations: processStations || [],
                                equipment_stations: equipmentStations || []
                            });
                        }

                        // 标记使用了新的工站系统，避免旧系统干扰
                        window.usingNewStationSystem = true;
                    } else {
                        console.log('[DEBUG] 未满足handleAIGeneratedStations调用条件');
                        console.log('[DEBUG] processStations:', processStations);
                        console.log('[DEBUG] equipmentStations:', equipmentStations);
                        window.usingNewStationSystem = false;
                    }

                    // 使用解析后的内容或回退到原有逻辑
                    if (parsedContent && (parsedContent.process_content || parsedContent.equipment_content)) {
                        // 使用解析后的内容
                        const processInfo = parsedContent.process_content || '';
                        const equipmentInfo = parsedContent.equipment_content || '';
                        const otherInfo = parsedContent.other_content || '';

                        // 只有在没有使用新工站系统时才使用旧系统
                        if (!window.usingNewStationSystem && (!processStations || processStations.length === 0)) {
                            if (processInfo) {
                                console.log('[DEBUG] 使用旧系统fillProcessToStation');
                                fillProcessToStation(processInfo);
                            }
                        } else if (window.usingNewStationSystem) {
                            console.log('[DEBUG] 跳过旧系统fillProcessToStation，使用新工站系统');
                        }



                        // 如果有其他内容，显示在产品信息区域
                        if (otherInfo) {
                            const partsContent = document.getElementById('parts-content');
                            if (partsContent) {
                                partsContent.innerHTML = formatText(otherInfo);
                            }
                        }
                    } else {
                        // 回退到原有的分割逻辑
                        let processIndex = answer.indexOf('工艺信息');
                        let equipmentIndex = answer.indexOf('设备信息');
                        let processInfo = ''; // 统一声明

                        if (processIndex !== -1 && equipmentIndex !== -1) {
                        const partsInfo = answer.substring(0, processIndex).trim();
                        processInfo = answer.substring(processIndex, equipmentIndex).trim();
                        const equipmentInfo = answer.substring(equipmentIndex).trim();

                        partsContent.innerHTML = formatText(partsInfo);
                        // equipmentContent已删除 - 设备信息现在通过工站系统处理
                    } else if (processIndex !== -1) {
                        const partsInfo = answer.substring(0, processIndex).trim();
                        processInfo = answer.substring(processIndex).trim();

                        partsContent.innerHTML = formatText(partsInfo);
                        // equipmentContent已删除 - 设备信息现在通过工站系统处理
                    } else if (equipmentIndex !== -1) {
                        const contentBeforeEquipment = answer.substring(0, equipmentIndex).trim();
                        const equipmentInfo = answer.substring(equipmentIndex).trim();

                        partsContent.innerHTML = formatText(contentBeforeEquipment);
                        // equipmentContent已删除 - 设备信息现在通过工站系统处理
                        processInfo = ''; // 没有工艺部分
                    } else {
                        partsContent.innerHTML = formatText(answer);
                        // equipmentContent已删除 - 设备信息现在通过工站系统处理
                        processInfo = ''; // 没有工艺部分
                    }

                        // 只有在没有使用新工站系统时才调用旧系统
                        if (!window.usingNewStationSystem) {
                            console.log('[DEBUG] 回退逻辑：使用旧系统fillProcessToStation');
                            fillProcessToStation(processInfo);
                        } else {
                            console.log('[DEBUG] 回退逻辑：跳过旧系统，已使用新工站系统');
                        }
                        }

                    document.getElementById('save-button').disabled = false;
                } else {
                    addMessage(`错误：${data.error}`);
                    partsContent.innerHTML = '';
                    //processContent.innerHTML = '';
                    // equipmentContent已删除
                    document.getElementById('save-button').disabled = true;
                }
            } catch (error) {
                addMessage(`系统错误：${error.message}`);
                partsContent.innerHTML = '';
                //processContent.innerHTML = '';
                // equipmentContent已删除
            } finally {
                loading.style.display = 'none';
                sendButton.disabled = false;
                questionInput.disabled = false;
                questionInput.focus();
            }
        }

        // 修改保存功能，收集所有区域的内容
        saveButton.addEventListener('click', async () => {
            const projectInfo = {
                projectName: document.getElementById('project-name').value,
                productionSite: document.getElementById('production-site').value,
                productFamily: document.getElementById('product-family').value,
                customer: document.getElementById('customer').value,
                sopDate: document.getElementById('sop-date').value,
                lineType: document.getElementById('line-type').value,
                lineNumber: document.getElementById('line-number').value,
                appResponsible: document.getElementById('app-responsible').value,
                aepResponsible: document.getElementById('aep-responsible').value,
                preAcceptanceDate: document.getElementById('pre-acceptance-date').value,
                offlineDebugDate:document.getElementById('offline-debug-date').value,
                factoryEntryDate: document.getElementById('factory-entry-date').value
            };

            const processHtml = stations.map(st =>
                `<div>
                    <b>${st.code} ${st.name}</b><br>
                    <b>工艺描述：</b><pre>${st.desc}</pre>
                    <b>具体要求：</b><pre>${st.requirement}</pre>
                </div>`
            ).join('<hr>');
            const content = `
                <h2>零件信息</h2>
                ${partsContent.innerHTML}
                <h2>工艺信息</h2>
                ${processHtml}
            `;

            try {
                const response = await fetch('/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content,
                        project_info: selectedProject // 添加选中的项目信息
                    })
                });

                if (response.ok) {
                    alert('保存成功！');
                } else {
                    const data = await response.json();
                    alert(`保存失败：${data.error}`);
                }
            } catch (error) {
                alert(`保存失败：${error.message}`);
            }
        });

        // 页面加载完成后获取项目数据
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const response = await fetch('/projects');
                if (response.ok) {
                    const projects = await response.json();
                    
                    // 填充项目名称下拉框
                    const projectSelect = document.getElementById('project-name');
                    
                    // 清空现有选项（除了placeholder）
                    while (projectSelect.options.length > 1) {
                        projectSelect.remove(1);
                    }
                    
                    // 添加加载状态信息
                    if (projects.length === 0) {
                        const option = document.createElement('option');
                        option.value = "";
                        option.textContent = "没有找到项目数据";
                        projectSelect.appendChild(option);
                        console.error('没有从服务器获取到项目数据');
                    } else {
                        console.log(`加载了 ${projects.length} 个项目`);
                        
                        // 按项目名称排序
                        projects.sort((a, b) => {
                            return a.title.localeCompare(b.title, 'zh-CN');
                        });
                        
                        // 添加所有项目
                        projects.forEach(project => {
                            if (project && project.id !== undefined && project.title) {
                                const option = document.createElement('option');
                                option.value = project.id;
                                option.textContent = project.title;
                                projectSelect.appendChild(option);
                                console.log(`添加项目: ${project.title}, ID: ${project.id}`);
                            } else {
                                console.warn('跳过无效项目数据:', project);
                            }
                        });
                    }
                    
                    // 收集所有唯一的生产工厂和产品系列
                    const uniqueProductionSites = [...new Set(projects.map(p => p["production-site"]).filter(Boolean))];
                    const uniqueProductFamilies = [...new Set(projects.map(p => p["product-family"]).filter(Boolean))];
                    
                    // 填充生产工厂下拉菜单
                    const productionSiteSelect = document.getElementById('production-site');
                    uniqueProductionSites.forEach(site => {
                        const option = document.createElement('option');
                        option.value = site;
                        option.textContent = site;
                        productionSiteSelect.appendChild(option);
                    });
                    
                    // 填充产品系列下拉菜单
                    const productFamilySelect = document.getElementById('product-family');
                    uniqueProductFamilies.forEach(family => {
                        const option = document.createElement('option');
                        option.value = family;
                        option.textContent = family;
                        productFamilySelect.appendChild(option);
                    });

                    // 收集所有唯一的客户和SOP日期
                    const uniqueCustomers = [...new Set(projects.map(p => p["customer"]).filter(Boolean))];
                    const uniqueSopDates = [...new Set(projects.map(p => p["SOP-date"]).filter(Boolean))];

                    // 填充客户下拉菜单
                    const customerSelect = document.getElementById('customer');
                    uniqueCustomers.forEach(customer => {
                        const option = document.createElement('option');
                        option.value = customer;
                        option.textContent = customer;
                        customerSelect.appendChild(option);
                    });

                    // 填充SOP日期下拉菜单
                    const sopDateSelect = document.getElementById('sop-date');
                    uniqueSopDates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        sopDateSelect.appendChild(option);
                    });

                    // 收集所有唯一的生产线类型
                    const uniqueLineTypes = [...new Set(projects.map(p => p["line-type"]).filter(Boolean))];

                    // 填充生产线类型下拉菜单
                    const lineTypeSelect = document.getElementById('line-type');
                    while (lineTypeSelect.options.length > 1) {  // 保留第一个默认选项
                        lineTypeSelect.remove(1);
                    }
                    uniqueLineTypes.forEach(type => {
                        if (type) {  // 确保值不为空
                            const option = document.createElement('option');
                            option.value = type;
                            option.textContent = type;
                            lineTypeSelect.appendChild(option);
                        }
                    });

                    // 收集所有唯一的线号、APP负责人和AEP负责人
                    const uniqueLineNumbers = [...new Set(projects.map(p => p["Line-number"]).filter(Boolean))];
                    const uniqueAppResponsibles = [...new Set(projects.map(p => p["APP-responsible"]).filter(Boolean))];
                    const uniqueAepResponsibles = [...new Set(projects.map(p => p["AEP-responsible"]).filter(Boolean))];
                    
                    // 填充线号下拉菜单
                    const lineNumberSelect = document.getElementById('line-number');
                    uniqueLineNumbers.forEach(number => {
                        const option = document.createElement('option');
                        option.value = number;
                        option.textContent = number;
                        lineNumberSelect.appendChild(option);
                    });

                    // 填充APP负责人下拉菜单
                    const appResponsibleSelect = document.getElementById('app-responsible');
                    uniqueAppResponsibles.forEach(person => {
                        const option = document.createElement('option');
                        option.value = person;
                        option.textContent = person;
                        appResponsibleSelect.appendChild(option);
                    });

                    // 填充AEP负责人下拉菜单
                    const aepResponsibleSelect = document.getElementById('aep-responsible');
                    uniqueAepResponsibles.forEach(person => {
                        const option = document.createElement('option');
                        option.value = person;
                        option.textContent = person;
                        aepResponsibleSelect.appendChild(option);
                    });

                    // 收集所有唯一的预验收日期和离线调试完成日期
                    const uniquePreAcceptanceDates = [...new Set(projects.map(p => p["pre-acceptance-date"]).filter(Boolean))];
                    const uniqueOfflineDebugDates = [...new Set(projects.map(p => p["offline-debug-date"]).filter(Boolean))];
                    
                    // 填充预验收日期下拉菜单
                    const preAcceptanceDateSelect = document.getElementById('pre-acceptance-date');
                    uniquePreAcceptanceDates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        preAcceptanceDateSelect.appendChild(option);
                    });
                    
                    // 填充离线调试完成日期下拉菜单
                    const offlineDebugDateSelect = document.getElementById('offline-debug-date');
                    uniqueOfflineDebugDates.forEach(date => {
                        const option = document.createElement('option');
                        option.value = date;
                        option.textContent = date;
                        offlineDebugDateSelect.appendChild(option);
                    });

                    // 更新项目选择事件处理
                    projectSelect.addEventListener('change', (e) => {
                        const selectedId = e.target.value;
                        const project = projects.find(p => p.id == selectedId);
                        if (project) {
                            selectedProject = {
                                title: project.title,
                                "production-site": project["production-site"],
                                "product-family": project["product-family"],
                                "customer": project["customer"],
                                "SOP-date": project["SOP-date"],
                                "line-type": project["line-type"] || "",  // 使用SharePoint数据
                                "line-number": project["Line-number"] || "",
                                "app-responsible": project["APP-responsible"] || "",
                                "aep-responsible": project["AEP-responsible"] || "",
                                "preAcceptanceDate": project["pre-acceptance-date"] || "",
                                "offlineDebugDate": project["offline-debug-date"] || "",
                                "factoryEntryDate": document.getElementById('factory-entry-date').value
                            };
                            
                            // 联动更新各个字段
                            productionSiteSelect.value = project["production-site"] || "";
                            productFamilySelect.value = project["product-family"] || "";
                            customerSelect.value = project["customer"] || "";
                            sopDateSelect.value = project["SOP-date"] || "";
                            
                            const lineTypeSelect = document.getElementById('line-type');
                            if (lineTypeSelect) {
                                let found = false;
                                for (let i = 0; i < lineTypeSelect.options.length; i++) {
                                    if (lineTypeSelect.options[i].value === (project["line-type"] || "")) {
                                        found = true;
                                        break;
                                    }
                                }
                                if (!found && project["line-type"]) {
                                    const option = document.createElement('option');
                                    option.value = project["line-type"];
                                    option.textContent = project["line-type"];
                                    lineTypeSelect.appendChild(option);
                                }
                                lineTypeSelect.value = project["line-type"] || "";
                            }
                            
                            lineNumberSelect.value = project["Line-number"] || "";
                            appResponsibleSelect.value = project["APP-responsible"] || "";
                            aepResponsibleSelect.value = project["AEP-responsible"] || "";
                            preAcceptanceDateSelect.value = project["pre-acceptance-date"] || "";
                            offlineDebugDateSelect.value = project["offline-debug-date"] || "";
                        } else {
                            selectedProject = null;
                        }
                    });
                }
            } catch (error) {
                console.error('获取项目数据失败:', error);
            }
        });

        // 修改目录切换事件处理
        document.addEventListener('DOMContentLoaded', function () {
            const menuItems = document.querySelectorAll('.menu-item');
            menuItems.forEach(item => {
                item.addEventListener('click', function() {
                    const pageName = this.getAttribute('data-page');
                    
                    // 如果切换到产品信息页，检查是否已填写制造规范封面信息
                    if (pageName === 'product') {
                        const productFamily = document.getElementById('product-family').value;
                        if (!productFamily) {
                            alert('请先填写制造规范封面信息！');
                            // 阻止切换到产品信息页
                            return;
                        }
                    }
                    
                    // 切换页面显示
                    
                    // 初始化模板生成器
                    if (pageName === 'product') {
                        initTemplateGenerator();
                    }
                });
            });
        });

        // 事件监听
        sendButton.addEventListener('click', sendQuestion);
        questionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendQuestion();
            }
        });

        // 自动聚焦输入框
        questionInput.focus();
        
        // 模板生成器功能
        document.addEventListener('DOMContentLoaded', function() {
            // 只在显示产品信息页面时初始化模板生成器
            document.querySelector('[data-content="product-info"]').addEventListener('click', function() {
                initTemplateGenerator();
            });
        });
        
        function initTemplateGenerator() {
            const partsTableBody = document.getElementById('partsTableBody');
            const addPartRowBtn = document.getElementById('addPartRow');
            const generatePromptBtn = document.getElementById('generatePrompt');
            const productTypeSelect = document.getElementById('productType');
            
            // 获取制造规范封面的产品系列值
            const productFamily = document.getElementById('product-family').value;
            
            // 根据产品系列设置产品类型选项并禁用选择框
            if (productFamily) {
                // 清空现有选项
                productTypeSelect.innerHTML = '<option value="">请选择产品类型...</option>';
                
                // 根据产品系列设置对应的产品类型
                switch(productFamily.toUpperCase()) {
                    case 'SAB':
                        productTypeSelect.innerHTML += '<option value="SAB" selected>SAB</option>';
                        break;
                    case 'IC':
                        productTypeSelect.innerHTML += '<option value="IC" selected>IC</option>';
                        break;


                    case 'DAB':
                        productTypeSelect.innerHTML += '<option value="DAB" selected>DAB</option>';
                        break;
                    case 'PAB':
                        productTypeSelect.innerHTML += '<option value="PAB" selected>PAB</option>';
                        break;
                    case 'FCA':
                        productTypeSelect.innerHTML += '<option value="FCA" selected>FCA</option>';
                        break;
                }
                
                // 禁用产品类型选择
                productTypeSelect.disabled = true;
            } else {
                // 如果没有选择产品系列，显示提示信息
                alert('请先在制造规范封面选择产品系列！');
                // 禁用产品信息页面的操作
                addPartRowBtn.disabled = true;
                generatePromptBtn.disabled = true;
                productTypeSelect.disabled = true;
                return;
            }
            
            // 清空现有行
            partsTableBody.innerHTML = '';
            
            // 添加零件行函数
            function addPartRow() {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="text" placeholder="新项目名称"></td>
                    <td><input type="text" placeholder="零件编号"></td>
                    <td><input type="text" placeholder="零件名称"></td>
                    <td><input type="text" placeholder="单位用量"></td>
                    <td><input type="text" placeholder="包装尺寸(mm)"></td>
                    <td><input type="text" placeholder="单位包装数量"></td>
                    <td>
                        <select>
                            <option value="">请选择状态</option>
                            <option value="共线">共线</option>
                            <option value="新制">新制</option>
                        </select>
                    </td>
                    <td><input type="text" placeholder="共享项目名称"></td>
                    <td><input type="text" placeholder="备注"></td>
                    <td><button class="btn remove-part">删除</button></td>
                `;
                partsTableBody.appendChild(row);
                
                // 添加删除按钮功能
                const deleteButto = row.querySelector('.remove-part').addEventListener('click', function() {
                    row.remove();
                });
            }
            
            // 生成提示语函数
            async function generatePrompt() {
                try {
                    // 获取表格体
                    const partsTableBody = document.querySelector('.parts-table tbody');
                    if (!partsTableBody) {
                        console.error('未找到零件表格');
                        return;
                    }

                    // 获取所有行
                    const rows = partsTableBody.querySelectorAll('tr');
                    let partNames = [];

                    rows.forEach((row, index) => {
                        // 获取第三列的单元格（零件名称）
                        const cells = row.querySelectorAll('td');
                        if (cells.length >= 3) {
                            // 优先取 input 的值，否则取单元格文本
                            let partName = '';
                            const input = cells[2].querySelector('input');
                            if (input) {
                                partName = input.value.trim();
                            } else {
                                partName = cells[2].textContent.trim();
                            }
                            if (partName) {
                                partNames.push(partName);
                            }
                        }
                    });

                    if (partNames.length === 0) {
                        alert('请先添加零件信息');
                        return;
                    }

                    const productType = document.getElementById('productType').value;
                    if (!productType) {
                        alert('请先选择产品类型');
                        return;
                    }

                    // 调用后端API生成改进的prompt
                    const response = await fetch('/generate_prompt', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            product_type: productType,
                            parts_list: partNames,
                            project_requirements: ''
                        }),
                    });

                    const data = await response.json();

                    if (response.ok) {
                        // 填入聊天输入框
                        const questionInput = document.getElementById('question-input');
                        if (questionInput) {
                            questionInput.value = data.prompt;
                            questionInput.focus();
                        }

                        // 显示成功消息
                        alert(`已生成${data.product_type}产品的标准格式Prompt（包含${data.parts_count}个零件）`);
                    } else {
                        alert(`生成Prompt失败：${data.error}`);
                    }
                } catch (error) {
                    console.error('生成Prompt错误:', error);
                    alert('生成Prompt时发生错误，请重试');
                }
            }
            
            // 添加事件监听
            addPartRowBtn.addEventListener('click', addPartRow);
            generatePromptBtn.addEventListener('click', generatePrompt);
            
            // 初始添加一行
            addPartRow();
        }

        // ===== 目录与内容滚动联动 =====
document.addEventListener('DOMContentLoaded', function () {
    // 目录项与内容页一一对应
    const menuItems = document.querySelectorAll('.menu-item');
    const previewPages = [
        document.getElementById('page-cover'),
        document.getElementById('page-smart-match'),
        document.getElementById('page-product'),
        document.getElementById('page-process'),
        document.getElementById('page-equipment'),
        document.getElementById('page-fixture'),
        document.getElementById('page-terms')
    ];
    const contentArea = document.querySelector('.content-area');

    // 1. 点击目录，平滑滚动到对应内容
    menuItems.forEach((item, idx) => {
        item.addEventListener('click', function () {
            // 切换页面显示
            previewPages.forEach((page, i) => {
                page.style.display = (i === idx) ? '' : 'none';
            });
            // 高亮目录
            menuItems.forEach(mi => mi.classList.remove('active'));
            item.classList.add('active');
            // 滚动到顶部
            contentArea.scrollTo({ top: 0, behavior: 'smooth' });
        });
    });

    // 2. 滚动内容区时，自动高亮目录
    contentArea.addEventListener('scroll', function () {
        // 只考虑当前显示的页面
        previewPages.forEach((page, idx) => {
            if (page.style.display !== 'none') {
                // 查找所有 section（如 .preview-section）
                const sections = page.querySelectorAll('.preview-section, .template-panel, .terms-section');
                let current = 0;
                let minDist = Infinity;
                sections.forEach((section, i) => {
                    const rect = section.getBoundingClientRect();
                    const areaRect = contentArea.getBoundingClientRect();
                    const dist = Math.abs(rect.top - areaRect.top);
                    if (rect.top - areaRect.top <= 40 && dist < minDist) {
                        minDist = dist;
                        current = i;
                    }
                });
                // 高亮目录
                menuItems.forEach(mi => mi.classList.remove('active'));
                menuItems[idx].classList.add('active');
            }
        });
    });

    // 3. 页面初始化时只显示第一个页面
    previewPages.forEach((page, i) => {
        page.style.display = (i === 0) ? '' : 'none';
    });
    menuItems.forEach((mi, i) => {
        mi.classList.toggle('active', i === 0);
    });
});

// ====== 浮动聊天窗拖动功能 ======
(function() {
    const chat = document.getElementById('floating-chat');
    const header = document.getElementById('floating-chat-header');
    const closeBtn = document.getElementById('floating-chat-close');
    const aiTrigger = document.getElementById('ai-assistant-trigger');
    let offsetX = 0, offsetY = 0, dragging = false;

    header.addEventListener('mousedown', function(e) {
        dragging = true;
        chat.classList.add('dragging');
        offsetX = e.clientX - chat.getBoundingClientRect().left;
        offsetY = e.clientY - chat.getBoundingClientRect().top;
        document.body.style.userSelect = 'none';
    });

    document.addEventListener('mousemove', function(e) {
        if (!dragging) return;
        let x = e.clientX - offsetX;
        let y = e.clientY - offsetY;
        // 限制在窗口内
        x = Math.max(10, Math.min(window.innerWidth - chat.offsetWidth - 10, x));
        y = Math.max(10, Math.min(window.innerHeight - chat.offsetHeight - 10, y));
        chat.style.left = x + 'px';
        chat.style.top = y + 'px';
        chat.style.right = 'auto';
    });

    document.addEventListener('mouseup', function() {
        if (dragging) {
            dragging = false;
            chat.classList.remove('dragging');
            document.body.style.userSelect = '';
        }
    });

    // 关闭按钮
    closeBtn.addEventListener('click', function() {
        chat.style.display = 'none';
        aiTrigger.classList.remove('hidden');
        // 保存聊天框关闭状态到本地存储
        localStorage.setItem('chatBoxClosed', 'true');
    });

    // AI助手按钮点击事件
    aiTrigger.addEventListener('click', function() {
        chat.style.display = 'flex';
        aiTrigger.classList.add('hidden');
        // 保存聊天框打开状态到本地存储
        localStorage.setItem('chatBoxClosed', 'false');

        // 添加打开动画效果
        chat.style.transform = 'scale(0.8)';
        chat.style.opacity = '0';
        setTimeout(() => {
            chat.style.transition = 'all 0.3s ease';
            chat.style.transform = 'scale(1)';
            chat.style.opacity = '1';
        }, 10);
    });

    // 页面加载时检查聊天框状态
    function initializeChatState() {
        const isClosed = localStorage.getItem('chatBoxClosed') === 'true';
        const isFirstVisit = localStorage.getItem('aiAssistantFirstVisit') !== 'false';

        if (isClosed) {
            chat.style.display = 'none';
            aiTrigger.classList.remove('hidden');

            // 首次访问时添加脉冲动画提示
            if (isFirstVisit) {
                aiTrigger.classList.add('first-visit');
                // 5秒后移除脉冲动画
                setTimeout(() => {
                    aiTrigger.classList.remove('first-visit');
                    localStorage.setItem('aiAssistantFirstVisit', 'false');
                }, 5000);
            }
        } else {
            chat.style.display = 'flex';
            aiTrigger.classList.add('hidden');
            // 如果聊天框是打开的，标记为已访问
            localStorage.setItem('aiAssistantFirstVisit', 'false');
        }
    }

    // 初始化聊天框状态
    initializeChatState();

    // 可通过双击header恢复显示（保留原有功能）
    header.addEventListener('dblclick', function() {
        chat.style.display = '';
    });

    // 初始定位
    chat.style.left = '';
    chat.style.top = '';
    chat.style.right = '40px';
    chat.style.bottom = '';
})();

document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.sidebar');
    const contentArea = document.querySelector('.content-area');
    const sidebarResizeHandle = document.getElementById('sidebar-resize-handle');
    const previewContainer = document.querySelector('.preview-container');
    let isSidebarDragging = false;
    let startX = 0;
    let startSidebarWidth = 0;

    sidebarResizeHandle.addEventListener('mousedown', function(e) {
        isSidebarDragging = true;
        startX = e.clientX;
        startSidebarWidth = sidebar.offsetWidth;
        sidebarResizeHandle.classList.add('dragging');
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
        e.preventDefault();
    });

    document.addEventListener('mousemove', function(e) {
        if (!isSidebarDragging) return;
        const deltaX = e.clientX - startX;
        // 限制最小最大宽度
        const minSidebarWidth = 80;
        const maxSidebarWidth = 400;
        let newSidebarWidth = Math.max(minSidebarWidth, Math.min(startSidebarWidth + deltaX, maxSidebarWidth));
        sidebar.style.width = newSidebarWidth + 'px';
        // 内容区自适应
        contentArea.style.flex = '1';
    });

    document.addEventListener('mouseup', function() {
        if (isSidebarDragging) {
            isSidebarDragging = false;
            sidebarResizeHandle.classList.remove('dragging');
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
        }
    });
});

document.addEventListener('DOMContentLoaded', async () => {
    // flatpickr 初始化入厂日期输入框
    flatpickr("#factory-entry-date", {
        dateFormat: "Y-m-d"
    });
});

document.getElementById('templateType').addEventListener('change', async function() {
    const productFamily = document.getElementById('productType').value;
    const templateType = this.value;

    if (!productFamily || !templateType) {
        alert('请先选择产品类型和模板分类');
        return;
    }

    try {
        const response = await fetch('/get_parts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ product_family: productFamily, template_type: templateType })
        });

        const data = await response.json();
        if (response.ok) {
            const partsTableBody = document.getElementById('partsTableBody');
            partsTableBody.innerHTML = ''; // 清空现有零件列表

            data.parts.forEach(part => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="text" placeholder="新项目名称"></td>
                    <td><input type="text" placeholder="零件编号"></td>
                    <td><input type="text" value="${part.name}" placeholder="零件名称"></td>
                    <td><input type="text" placeholder="单位用量"></td>
                    <td><input type="text" placeholder="包装尺寸(mm)"></td>
                    <td><input type="text" placeholder="单位包装数量"></td>
                    <td>
                        <select>
                            <option value="">请选择状态</option>
                            <option value="共线">共线</option>
                            <option value="新制">新制</option>
                        </select>
                    </td>
                    <td><input type="text" placeholder="共享项目名称"></td>
                    <td><input type="text" placeholder="备注"></td>
                    <td><button class="btn remove-part">删除</button></td>
                `;
                // 添加删除按钮功能
                row.querySelector('.remove-part').addEventListener('click', function() {
                    row.remove();
                });
                partsTableBody.appendChild(row);
            });
        } else {
            alert(`错误：${data.error}`);
        }
    } catch (error) {
        console.error('获取零件失败:', error);
    }
});

// 工艺工站数据结构
let stations = [];

// 工艺工站渲染函数
function renderStations() {
    const list = document.getElementById('stations-list');
    list.innerHTML = '';
    stations.forEach((station, idx) => {
        const stationDiv = document.createElement('div');
        stationDiv.className = 'station-block';
        stationDiv.style = 'border:1px solid #e0e0e0;border-radius:8px;padding:1rem;margin-bottom:1.5rem;background:#fafbfc;position:relative;';
        stationDiv.innerHTML = `
            <div style="display:flex;align-items:center;gap:1rem;margin-bottom:0.8rem;">
                <label>站位：</label>
                <input type="text" value="${station.code}" style="width:80px;" onchange="stations[${idx}].code=this.value">
                <label>站位名称：</label>
                <input type="text" value="${station.name}" style="width:160px;" onchange="stations[${idx}].name=this.value">
                <button class="btn" style="background:#ff5252;color:#fff;margin-left:auto;" onclick="removeStation(${idx})">删除站位</button>
            </div>
            <div style="margin-bottom:0.8rem;">
                <label>工艺描述：</label>
                <textarea style="width:100%;min-height:100px;background:#f6fff6;border:1px solid #cce5cc;border-radius:4px;padding:0.5rem;" onchange="stations[${idx}].desc=this.value">${station.desc || ''}</textarea>
            </div>
            <div>
                <label>具体要求：</label>
                <textarea style="width:100%;min-height:60px;background:#fafafa;border:1px solid #e0e0e0;border-radius:4px;padding:0.5rem;" onchange="stations[${idx}].requirement=this.value">${station.requirement || ''}</textarea>
            </div>
        `;
        list.appendChild(stationDiv);
    });
}

// 添加站位
document.getElementById('add-station-btn').onclick = function() {
    stations.push({
        code: `ST${stations.length+1}`,
        name: '',
        desc: '',
        requirement: ''
    });
    renderStations();
};

// 删除站位
window.removeStation = function(idx) {
    stations.splice(idx, 1);
    renderStations();
};

// 当AI回答后自动填充第一个站位的工艺描述
function fillProcessToStation(content) {
    if (!stations.length) {
        stations.push({
            code: 'ST1',
            name: '',
            desc: content,
            requirement: ''
        });
    } else {
        stations[0].desc = content;
    }
    renderStations();
}

// 修改AI回答内容分割逻辑，填充工艺描述到工站
document.getElementById('send-button').addEventListener('click', async () => {
    const question = document.getElementById('question-input').value.trim();
    if (!question) return;

    addMessage(question, true);
    document.getElementById('question-input').value = '';
    document.getElementById('loading').style.display = 'block';
    document.getElementById('send-button').disabled = true;
    document.getElementById('question-input').disabled = true;
    document.getElementById('save-button').disabled = true;

    try {
        const response = await fetch('/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ question }),
        });

        const data = await response.json();

        if (response.ok) {
            const answer = data.answer;
            addMessage(answer);

            // 分割标题
            let processIndex = answer.indexOf('工艺部分');
            let equipmentIndex = answer.indexOf('设备部分');
            let processInfo = ''; // 统一声明

            if (processIndex !== -1 && equipmentIndex !== -1) {
                const partsInfo = answer.substring(0, processIndex).trim();
                processInfo = answer.substring(processIndex, equipmentIndex).trim();
                const equipmentInfo = answer.substring(equipmentIndex).trim();

                partsContent.innerHTML = formatText(partsInfo);
                // equipmentContent已删除 - 设备信息现在通过工站系统处理
            } else if (processIndex !== -1) {
                const partsInfo = answer.substring(0, processIndex).trim();
                processInfo = answer.substring(processIndex).trim();

                partsContent.innerHTML = formatText(partsInfo);
                // equipmentContent已删除 - 设备信息现在通过工站系统处理
            } else if (equipmentIndex !== -1) {
                const contentBeforeEquipment = answer.substring(0, equipmentIndex).trim();
                const equipmentInfo = answer.substring(equipmentIndex).trim();

                partsContent.innerHTML = formatText(contentBeforeEquipment);
                // equipmentContent已删除 - 设备信息现在通过工站系统处理
                processInfo = ''; // 没有工艺部分
            } else {
                partsContent.innerHTML = formatText(answer);
                // equipmentContent已删除 - 设备信息现在通过工站系统处理
                processInfo = ''; // 没有工艺部分
            }

            // 只有在没有使用新工站系统时才调用旧系统
            if (!window.usingNewStationSystem) {
                console.log('[DEBUG] 第二个回退逻辑：使用旧系统fillProcessToStation');
                fillProcessToStation(processInfo);
            } else {
                console.log('[DEBUG] 第二个回退逻辑：跳过旧系统，已使用新工站系统');
            }

            document.getElementById('save-button').disabled = false;
        } else {
            addMessage(`错误：${data.error}`);
            partsContent.innerHTML = '';
            //processContent.innerHTML = '';
            // equipmentContent已删除
            document.getElementById('save-button').disabled = true;
        }
    } catch (error) {
        addMessage(`系统错误：${error.message}`);
        partsContent.innerHTML = '';
        //processContent.innerHTML = '';
        // equipmentContent已删除
    } finally {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('send-button').disabled = false;
        document.getElementById('question-input').disabled = false;
        document.getElementById('question-input').focus();
    }
});

// ====== AI助手相关全局函数 ======
/**
 * 打开AI助手聊天框
 */
function openAIAssistant() {
    const chat = document.getElementById('floating-chat');
    const aiTrigger = document.getElementById('ai-assistant-trigger');

    if (chat && aiTrigger) {
        chat.style.display = 'flex';
        aiTrigger.classList.add('hidden');
        localStorage.setItem('chatBoxClosed', 'false');

        // 添加打开动画效果
        chat.style.transform = 'scale(0.8)';
        chat.style.opacity = '0';
        setTimeout(() => {
            chat.style.transition = 'all 0.3s ease';
            chat.style.transform = 'scale(1)';
            chat.style.opacity = '1';
        }, 10);

        // 聚焦到输入框
        setTimeout(() => {
            const questionInput = document.getElementById('question-input');
            if (questionInput) {
                questionInput.focus();
            }
        }, 300);
    }
}

/**
 * 关闭AI助手聊天框
 */
function closeAIAssistant() {
    const chat = document.getElementById('floating-chat');
    const aiTrigger = document.getElementById('ai-assistant-trigger');

    if (chat && aiTrigger) {
        chat.style.display = 'none';
        aiTrigger.classList.remove('hidden');
        localStorage.setItem('chatBoxClosed', 'true');
    }
}

/**
 * 切换AI助手聊天框显示状态
 */
function toggleAIAssistant() {
    const chat = document.getElementById('floating-chat');
    const isVisible = chat && chat.style.display !== 'none';

    if (isVisible) {
        closeAIAssistant();
    } else {
        openAIAssistant();
    }
}

/**
 * 检查AI助手是否可见
 */
function isAIAssistantVisible() {
    const chat = document.getElementById('floating-chat');
    return chat && chat.style.display !== 'none';
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + Shift + A 打开/关闭AI助手
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        toggleAIAssistant();
    }

    // ESC键关闭AI助手
    if (e.key === 'Escape' && isAIAssistantVisible()) {
        closeAIAssistant();
    }
});

// ====== 设备图片和参数相关函数 ======
/**
 * 处理设备图片上传
 * @param {Event} event - 文件上传事件
 * @param {number} stationIndex - 工站索引
 */
function handleEquipmentImageUpload(event, stationIndex) {
    const file = event.target.files[0];
    if (!file) return;

    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!allowedTypes.includes(file.type)) {
        showToast('请选择 JPG 或 PNG 格式的图片文件');
        event.target.value = '';
        return;
    }

    // 检查文件大小 (限制为5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
        showToast('图片文件大小不能超过 5MB');
        event.target.value = '';
        return;
    }

    // 显示加载状态
    showLoadingOverlay(`正在上传设备图片...`);

    const reader = new FileReader();
    reader.onload = function(e) {
        // 保存图片数据到设备工站数据中
        if (!window.equipmentImagesData) {
            window.equipmentImagesData = {};
        }

        window.equipmentImagesData[stationIndex] = {
            imageData: e.target.result,
            fileName: file.name
        };

        // 更新预览
        updateEquipmentImagePreview(stationIndex);

        // 显示删除按钮
        const deleteBtn = document.getElementById(`delete-equipment-image-btn-${stationIndex}`);
        if (deleteBtn) {
            deleteBtn.style.display = 'inline-block';
        }

        // 保存到设备工站数据中
        if (typeof saveEquipmentStationImageAndParameters === 'function') {
            saveEquipmentStationImageAndParameters(stationIndex);
        }

        hideLoadingOverlay();
        showToast('设备图片上传成功！');

        console.log(`设备工站 ${stationIndex} 图片上传成功:`, file.name);
    };

    reader.onerror = function() {
        hideLoadingOverlay();
        showToast('图片读取失败，请重试');
    };

    reader.readAsDataURL(file);
}

/**
 * 更新设备图片预览
 * @param {number} stationIndex - 工站索引
 */
function updateEquipmentImagePreview(stationIndex) {
    const previewArea = document.getElementById(`equipment-image-preview-${stationIndex}`);
    if (!previewArea) return;

    const imageData = window.equipmentImagesData?.[stationIndex];

    if (imageData && imageData.imageData) {
        previewArea.innerHTML = `
            <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                <img src="${imageData.imageData}"
                     style="max-width: 100%; max-height: 100%; object-fit: contain; cursor: pointer; border-radius: 4px;"
                     onclick="showImageModal(this.src)"
                     alt="设备图片">
                <div style="position: absolute; top: 5px; right: 5px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">
                    ${imageData.fileName}
                </div>
            </div>
        `;
    } else {
        previewArea.innerHTML = `
            <div style="color: #999;">
                <div style="font-size: 32px; margin-bottom: 5px;">🖼️</div>
                <p style="margin: 0; font-size: 12px;">点击下方按钮上传设备图片</p>
                <small>支持 JPG, PNG 格式</small>
            </div>
        `;
    }
}

/**
 * 删除设备图片
 * @param {number} stationIndex - 工站索引
 */
function deleteEquipmentImage(stationIndex) {
    const imageData = window.equipmentImagesData?.[stationIndex];
    if (!imageData) {
        return;
    }

    if (confirm('确定要删除当前的设备图片吗？')) {
        // 清除数据
        if (window.equipmentImagesData) {
            delete window.equipmentImagesData[stationIndex];
        }

        // 更新预览
        updateEquipmentImagePreview(stationIndex);

        // 隐藏删除按钮
        const deleteBtn = document.getElementById(`delete-equipment-image-btn-${stationIndex}`);
        if (deleteBtn) {
            deleteBtn.style.display = 'none';
        }

        // 清除文件输入
        const inputElement = document.getElementById(`equipment-image-input-${stationIndex}`);
        if (inputElement) {
            inputElement.value = '';
        }

        showToast('设备图片已删除');
        console.log(`设备工站 ${stationIndex} 图片已删除`);
    }
}

/**
 * 更新设备参数
 * @param {number} stationIndex - 工站索引
 * @param {string} paramName - 参数名称
 * @param {string} value - 参数值
 */
function updateEquipmentParameter(stationIndex, paramName, value) {
    // 初始化设备参数数据
    if (!window.equipmentParametersData) {
        window.equipmentParametersData = {};
    }

    if (!window.equipmentParametersData[stationIndex]) {
        window.equipmentParametersData[stationIndex] = {};
    }

    // 更新参数值
    window.equipmentParametersData[stationIndex][paramName] = value;

    // 保存到设备工站数据中
    if (typeof saveEquipmentStationImageAndParameters === 'function') {
        saveEquipmentStationImageAndParameters(stationIndex);
    }

    console.log(`设备工站 ${stationIndex} 参数 ${paramName} 更新为:`, value);
}

/**
 * 获取设备图片和参数数据
 * @param {number} stationIndex - 工站索引
 * @returns {Object} 设备图片和参数数据
 */
function getEquipmentImageAndParameterData(stationIndex) {
    return {
        image: window.equipmentImagesData?.[stationIndex] || null,
        parameters: window.equipmentParametersData?.[stationIndex] || {}
    };
}

/**
 * 设置设备图片和参数数据
 * @param {number} stationIndex - 工站索引
 * @param {Object} data - 设备图片和参数数据
 */
function setEquipmentImageAndParameterData(stationIndex, data) {
    // 设置图片数据
    if (data.image && data.image.imageData) {
        if (!window.equipmentImagesData) {
            window.equipmentImagesData = {};
        }
        window.equipmentImagesData[stationIndex] = data.image;
        updateEquipmentImagePreview(stationIndex);

        const deleteBtn = document.getElementById(`delete-equipment-image-btn-${stationIndex}`);
        if (deleteBtn) {
            deleteBtn.style.display = 'inline-block';
        }
    }

    // 设置参数数据
    if (data.parameters) {
        if (!window.equipmentParametersData) {
            window.equipmentParametersData = {};
        }
        window.equipmentParametersData[stationIndex] = data.parameters;

        // 更新参数输入框的值
        Object.keys(data.parameters).forEach(paramName => {
            let inputId = `equipment-${paramName}-${stationIndex}`;
            // 处理特殊参数名映射
            if (paramName === 'cycle_time') {
                inputId = `equipment-cycle-time-${stationIndex}`;
            } else if (paramName === 'changeover_time') {
                inputId = `equipment-changeover-time-${stationIndex}`;
            }

            const inputElement = document.getElementById(inputId);
            if (inputElement) {
                inputElement.value = data.parameters[paramName];
            }
        });
    }
}

// ====== 夹具文件上传相关函数 ======
/**
 * 处理夹具文件上传（支持多文件）
 * @param {Event} event - 文件上传事件
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引（可选，默认为0以保持向后兼容）
 */
function handleFixtureFilesUpload(event, stationIndex, fixtureIndex = 0) {
    const files = Array.from(event.target.files);
    if (!files.length) return;

    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf',
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx'];

    for (let file of files) {
        const isValidType = allowedTypes.includes(file.type) ||
                           allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
        if (!isValidType) {
            showToast(`文件 "${file.name}" 格式不支持，请选择 JPG、PNG、PDF、DOC、DOCX 格式的文件`);
            event.target.value = '';
            return;
        }

        // 检查文件大小 (限制为10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            showToast(`文件 "${file.name}" 大小不能超过 10MB`);
            event.target.value = '';
            return;
        }
    }

    // 显示加载状态
    showLoadingOverlay(`正在上传 ${files.length} 个夹具文件...`);

    // 初始化夹具文件数据（支持多夹具）
    if (!window.fixtureFilesData) {
        window.fixtureFilesData = {};
    }
    if (!window.fixtureFilesData[stationIndex]) {
        window.fixtureFilesData[stationIndex] = {};
    }
    if (!window.fixtureFilesData[stationIndex][fixtureIndex]) {
        window.fixtureFilesData[stationIndex][fixtureIndex] = [];
    }

    let processedCount = 0;
    const totalFiles = files.length;

    files.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            // 添加文件数据到数组
            window.fixtureFilesData[stationIndex][fixtureIndex].push({
                fileData: e.target.result,
                fileName: file.name,
                fileSize: file.size,
                fileType: file.type,
                uploadTime: new Date().toISOString()
            });

            processedCount++;

            // 所有文件处理完成后更新预览
            if (processedCount === totalFiles) {
                updateFixtureFilesPreview(stationIndex, fixtureIndex);

                // 显示清空按钮
                const clearBtn = document.getElementById(`clear-fixture-files-btn-${stationIndex}-${fixtureIndex}`);
                if (clearBtn) {
                    clearBtn.style.display = 'inline-block';
                }

                // 保存到设备工站数据中
                if (typeof saveEquipmentStationImageAndParameters === 'function') {
                    saveEquipmentStationImageAndParameters(stationIndex);
                }

                hideLoadingOverlay();
                showToast(`成功上传 ${totalFiles} 个夹具文件！`);
                console.log(`设备工站 ${stationIndex} 夹具 ${fixtureIndex} 文件上传成功:`, files.map(f => f.name));
            }
        };

        reader.onerror = function() {
            hideLoadingOverlay();
            showToast(`文件 "${file.name}" 读取失败，请重试`);
        };

        reader.readAsDataURL(file);
    });
}

/**
 * 更新夹具文件预览
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引（可选，默认为0以保持向后兼容）
 */
function updateFixtureFilesPreview(stationIndex, fixtureIndex = 0) {
    const previewArea = document.getElementById(`fixture-files-preview-${stationIndex}-${fixtureIndex}`);
    if (!previewArea) return;

    const filesData = window.fixtureFilesData?.[stationIndex]?.[fixtureIndex];

    if (!filesData || filesData.length === 0) {
        // 显示空状态
        previewArea.innerHTML = `
            <div style="text-align: center; color: #999; padding: 20px;">
                <div style="font-size: 32px; margin-bottom: 5px;">📎</div>
                <p style="margin: 0; font-size: 12px;">点击上方按钮上传夹具文件</p>
                <small>支持 JPG, PNG, PDF, DOC, DOCX 格式，可选择多个文件</small>
            </div>
        `;
    } else {
        // 显示文件列表
        const filesHtml = filesData.map((fileData, index) => {
            const isImage = fileData.fileType.startsWith('image/');
            const fileIcon = getFileIcon(fileData.fileName, fileData.fileType);
            const fileSizeText = formatFileSize(fileData.fileSize);

            return `
                <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #e0e0e0; border-radius: 4px; margin-bottom: 6px; background: #fff;">
                    <div style="margin-right: 10px; font-size: 20px;">${fileIcon}</div>
                    <div style="flex: 1; min-width: 0;">
                        <div style="font-size: 12px; font-weight: 500; color: #333; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="${fileData.fileName}">
                            ${fileData.fileName}
                        </div>
                        <div style="font-size: 10px; color: #999;">
                            ${fileSizeText} • ${new Date(fileData.uploadTime).toLocaleString()}
                        </div>
                    </div>
                    <div style="display: flex; gap: 4px;">
                        ${isImage ? `
                        <button onclick="previewFixtureFile(${stationIndex}, ${fixtureIndex}, ${index})"
                                style="background: #1890ff; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 10px;"
                                title="预览图片">
                            👁️
                        </button>
                        ` : ''}
                        <button onclick="downloadFixtureFile(${stationIndex}, ${fixtureIndex}, ${index})"
                                style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 10px;"
                                title="下载文件">
                            ⬇️
                        </button>
                        <button onclick="deleteFixtureFile(${stationIndex}, ${fixtureIndex}, ${index})"
                                style="background: #ff4d4f; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 10px;"
                                title="删除文件">
                            🗑️
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        previewArea.innerHTML = `
            <div style="max-height: 200px; overflow-y: auto;">
                ${filesHtml}
            </div>
            <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #e0e0e0; font-size: 11px; color: #666; text-align: center;">
                共 ${filesData.length} 个文件
            </div>
        `;
    }
}

/**
 * 获取文件图标
 * @param {string} fileName - 文件名
 * @param {string} fileType - 文件类型
 * @returns {string} 文件图标
 */
function getFileIcon(fileName, fileType) {
    if (fileType.startsWith('image/')) {
        return '🖼️';
    } else if (fileType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf')) {
        return '📄';
    } else if (fileType.includes('word') || fileName.toLowerCase().endsWith('.doc') || fileName.toLowerCase().endsWith('.docx')) {
        return '📝';
    } else {
        return '📎';
    }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 预览夹具文件（仅支持图片）
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 * @param {number} fileIndex - 文件索引
 */
function previewFixtureFile(stationIndex, fixtureIndex, fileIndex) {
    const filesData = window.fixtureFilesData?.[stationIndex]?.[fixtureIndex];
    if (!filesData || !filesData[fileIndex]) return;

    const fileData = filesData[fileIndex];
    if (!fileData.fileType.startsWith('image/')) {
        showToast('只能预览图片文件');
        return;
    }

    // 创建模态框显示图片
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.8); z-index: 10000; display: flex;
        align-items: center; justify-content: center; cursor: pointer;
    `;

    modal.innerHTML = `
        <div style="max-width: 90%; max-height: 90%; position: relative;">
            <img src="${fileData.fileData}" style="max-width: 100%; max-height: 100%; object-fit: contain;" alt="${fileData.fileName}">
            <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 4px; font-size: 12px;">
                ${fileData.fileName}
            </div>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="position: absolute; top: 10px; left: 10px; background: rgba(255,77,79,0.8); color: white; border: none; border-radius: 4px; padding: 5px 10px; cursor: pointer; font-size: 12px;">
                关闭
            </button>
        </div>
    `;

    modal.onclick = function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    };

    document.body.appendChild(modal);
}

/**
 * 下载夹具文件
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 * @param {number} fileIndex - 文件索引
 */
function downloadFixtureFile(stationIndex, fixtureIndex, fileIndex) {
    const filesData = window.fixtureFilesData?.[stationIndex]?.[fixtureIndex];
    if (!filesData || !filesData[fileIndex]) return;

    const fileData = filesData[fileIndex];

    // 创建下载链接
    const link = document.createElement('a');
    link.href = fileData.fileData;
    link.download = fileData.fileName;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast(`文件 "${fileData.fileName}" 下载开始`);
}

/**
 * 删除单个夹具文件
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 * @param {number} fileIndex - 文件索引
 */
function deleteFixtureFile(stationIndex, fixtureIndex, fileIndex) {
    const filesData = window.fixtureFilesData?.[stationIndex]?.[fixtureIndex];
    if (!filesData || !filesData[fileIndex]) return;

    const fileData = filesData[fileIndex];

    if (confirm(`确定要删除文件 "${fileData.fileName}" 吗？`)) {
        // 从数组中删除文件
        filesData.splice(fileIndex, 1);

        // 更新预览
        updateFixtureFilesPreview(stationIndex, fixtureIndex);

        // 如果没有文件了，隐藏清空按钮
        if (filesData.length === 0) {
            const clearBtn = document.getElementById(`clear-fixture-files-btn-${stationIndex}-${fixtureIndex}`);
            if (clearBtn) {
                clearBtn.style.display = 'none';
            }
        }

        // 保存数据
        if (typeof saveEquipmentStationImageAndParameters === 'function') {
            saveEquipmentStationImageAndParameters(stationIndex);
        }

        showToast(`文件 "${fileData.fileName}" 已删除`);
        console.log(`设备工站 ${stationIndex} 夹具 ${fixtureIndex} 文件已删除:`, fileData.fileName);
    }
}

/**
 * 清空所有夹具文件
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 */
function clearAllFixtureFiles(stationIndex, fixtureIndex) {
    const filesData = window.fixtureFilesData?.[stationIndex]?.[fixtureIndex];
    if (!filesData || filesData.length === 0) return;

    if (confirm(`确定要清空所有 ${filesData.length} 个夹具文件吗？`)) {
        // 清空文件数组
        window.fixtureFilesData[stationIndex][fixtureIndex] = [];

        // 更新预览
        updateFixtureFilesPreview(stationIndex, fixtureIndex);

        // 隐藏清空按钮
        const clearBtn = document.getElementById(`clear-fixture-files-btn-${stationIndex}-${fixtureIndex}`);
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }

        // 清除文件输入
        const inputElement = document.getElementById(`fixture-files-input-${stationIndex}-${fixtureIndex}`);
        if (inputElement) {
            inputElement.value = '';
        }

        // 保存数据
        if (typeof saveEquipmentStationImageAndParameters === 'function') {
            saveEquipmentStationImageAndParameters(stationIndex);
        }

        showToast('所有夹具文件已清空');
        console.log(`设备工站 ${stationIndex} 夹具 ${fixtureIndex} 所有文件已清空`);
    }
}

// ====== 产线Layout相关函数 ======
/**
 * 处理产线Layout图片上传
 */
function handleProcessLayoutUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        showToast('请选择图片文件（JPG、PNG格式）');
        event.target.value = '';
        return;
    }

    // 验证文件大小（限制为10MB）
    if (file.size > 10 * 1024 * 1024) {
        showToast('图片文件大小不能超过10MB');
        event.target.value = '';
        return;
    }

    // 显示加载状态
    showLoadingOverlay('正在上传产线Layout图片...');

    const reader = new FileReader();
    reader.onload = function(e) {
        // 保存图片数据
        if (!window.processLayoutData) {
            window.processLayoutData = {};
        }
        window.processLayoutData.layoutImage = e.target.result;
        window.processLayoutData.fileName = file.name;
        window.processLayoutData.fileSize = file.size;
        window.processLayoutData.uploadTime = new Date().toISOString();

        // 更新预览
        updateProcessLayoutPreview();

        // 显示删除按钮
        const deleteBtn = document.getElementById('delete-process-layout-btn');
        if (deleteBtn) {
            deleteBtn.style.display = 'inline-block';
        }

        hideLoadingOverlay();
        showToast('产线Layout图片上传成功！');

        console.log('产线Layout上传成功:', file.name);
    };

    reader.onerror = function() {
        hideLoadingOverlay();
        showToast('图片读取失败，请重试');
        event.target.value = '';
    };

    reader.readAsDataURL(file);
}

/**
 * 更新产线Layout预览
 */
function updateProcessLayoutPreview() {
    const previewArea = document.getElementById('process-layout-preview');
    if (!previewArea) return;

    if (window.processLayoutData && window.processLayoutData.layoutImage) {
        previewArea.innerHTML = `
            <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                <img src="${window.processLayoutData.layoutImage}"
                     style="max-width: 100%; max-height: 100%; object-fit: contain; cursor: pointer; border-radius: 4px;"
                     onclick="showImageModal(this.src)"
                     alt="产线Layout图">
                <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                    ${window.processLayoutData.fileName}
                </div>
            </div>
        `;
    } else {
        previewArea.innerHTML = `
            <div style="color: #999;">
                <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                <p>点击下方按钮上传产线Layout图</p>
                <small>支持 JPG, PNG 格式图片</small>
            </div>
        `;
    }
}

/**
 * 删除产线Layout图片
 */
function deleteProcessLayoutImage() {
    if (!window.processLayoutData || !window.processLayoutData.layoutImage) {
        return;
    }

    if (confirm('确定要删除当前的产线Layout图片吗？')) {
        // 清除数据
        window.processLayoutData = {};

        // 更新预览
        updateProcessLayoutPreview();

        // 隐藏删除按钮
        const deleteBtn = document.getElementById('delete-process-layout-btn');
        if (deleteBtn) {
            deleteBtn.style.display = 'none';
        }

        // 清除文件输入
        const inputElement = document.getElementById('process-layout-input');
        if (inputElement) {
            inputElement.value = '';
        }

        showToast('产线Layout图片已删除');
        console.log('产线Layout图片已删除');
    }
}

/**
 * 获取产线Layout数据
 */
function getProcessLayoutData() {
    const data = {
        layoutImage: window.processLayoutData?.layoutImage || '',
        fileName: window.processLayoutData?.fileName || '',
        lineLength: document.getElementById('process-line-length')?.value || '',
        lineWidth: document.getElementById('process-line-width')?.value || '',
        lineHeight: document.getElementById('process-line-height')?.value || '',
        lineTakt: document.getElementById('process-line-takt')?.value || ''
    };
    return data;
}

/**
 * 设置产线Layout数据
 */
function setProcessLayoutData(data) {
    if (data.layoutImage) {
        window.processLayoutData = {
            layoutImage: data.layoutImage,
            fileName: data.fileName || '产线Layout图.jpg'
        };
        updateProcessLayoutPreview();

        const deleteBtn = document.getElementById('delete-process-layout-btn');
        if (deleteBtn) {
            deleteBtn.style.display = 'inline-block';
        }
    }

    // 设置参数值
    if (data.lineLength) {
        const lengthInput = document.getElementById('process-line-length');
        if (lengthInput) lengthInput.value = data.lineLength;
    }
    if (data.lineWidth) {
        const widthInput = document.getElementById('process-line-width');
        if (widthInput) widthInput.value = data.lineWidth;
    }
    if (data.lineHeight) {
        const heightInput = document.getElementById('process-line-height');
        if (heightInput) heightInput.value = data.lineHeight;
    }
    if (data.lineTakt) {
        const taktInput = document.getElementById('process-line-takt');
        if (taktInput) taktInput.value = data.lineTakt;
    }
}

// ====== 辅助函数 ======
/**
 * 显示Toast提示消息
 */
function showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;

    // 设置样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#ff4d4f' : type === 'success' ? '#52c41a' : '#1890ff'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 10000;
        font-size: 14px;
        max-width: 300px;
        word-wrap: break-word;
        animation: slideInRight 0.3s ease;
    `;

    // 添加动画样式
    if (!document.querySelector('#toast-styles')) {
        const style = document.createElement('style');
        style.id = 'toast-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 添加到页面
    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

/**
 * 显示加载遮罩层
 */
function showLoadingOverlay(message = '正在处理中...') {
    const overlay = document.getElementById('loading-overlay');
    const text = document.getElementById('loading-text');

    if (overlay && text) {
        text.textContent = message;
        overlay.style.display = 'flex';
    }
}

/**
 * 隐藏加载遮罩层
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

/**
 * 显示图片模态框
 */
function showImageModal(imageSrc) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
        cursor: pointer;
    `;

    // 创建图片元素
    const img = document.createElement('img');
    img.src = imageSrc;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    // 点击关闭
    modal.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    // ESC键关闭
    const handleKeydown = (e) => {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.removeEventListener('keydown', handleKeydown);
        }
    };
    document.addEventListener('keydown', handleKeydown);
}

// ====== 多夹具管理相关函数 ======
/**
 * 添加新夹具块
 * @param {number} stationIndex - 工站索引
 */
function addFixtureBlock(stationIndex) {
    const container = document.getElementById(`fixtures-container-${stationIndex}`);
    if (!container) {
        console.error(`未找到夹具容器 fixtures-container-${stationIndex}`);
        return;
    }

    // 获取当前夹具数量
    const existingFixtures = container.querySelectorAll('.fixture-block');
    const fixtureIndex = existingFixtures.length;

    // 创建新夹具数据
    const newFixture = {
        fixture_name: `夹具${fixtureIndex + 1}`,
        mechanical_requirements: '请输入夹具机械要求',
        electrical_requirements: '请输入夹具电气要求',
        error_prevention_requirements: '请输入夹具防错及点检要求'
    };

    // 生成新夹具HTML
    const fixtureHtml = window.stationGenerator.createSingleFixtureBlockHtml(newFixture, stationIndex, fixtureIndex);

    // 添加到容器
    container.insertAdjacentHTML('beforeend', fixtureHtml);

    // 更新设备工站数据
    updateEquipmentStationFixtures(stationIndex);

    showToast(`已添加新夹具：夹具${fixtureIndex + 1}`);
    console.log(`设备工站 ${stationIndex} 添加新夹具，索引: ${fixtureIndex}`);
}

/**
 * 在指定夹具后添加新夹具块
 * @param {number} stationIndex - 工站索引
 * @param {number} afterIndex - 在此索引后添加
 */
function addFixtureBlockAfter(stationIndex, afterIndex) {
    const container = document.getElementById(`fixtures-container-${stationIndex}`);
    if (!container) {
        console.error(`未找到夹具容器 fixtures-container-${stationIndex}`);
        return;
    }

    // 获取当前夹具数量
    const existingFixtures = container.querySelectorAll('.fixture-block');
    const fixtureIndex = existingFixtures.length;

    // 创建新夹具数据
    const newFixture = {
        fixture_name: `夹具${fixtureIndex + 1}`,
        mechanical_requirements: '请输入夹具机械要求',
        electrical_requirements: '请输入夹具电气要求',
        error_prevention_requirements: '请输入夹具防错及点检要求'
    };

    // 生成新夹具HTML
    const fixtureHtml = window.stationGenerator.createSingleFixtureBlockHtml(newFixture, stationIndex, fixtureIndex);

    // 在指定位置后插入
    if (afterIndex < existingFixtures.length - 1) {
        existingFixtures[afterIndex].insertAdjacentHTML('afterend', fixtureHtml);
    } else {
        container.insertAdjacentHTML('beforeend', fixtureHtml);
    }

    // 重新索引所有夹具块
    reindexFixtureBlocks(stationIndex);

    // 更新设备工站数据
    updateEquipmentStationFixtures(stationIndex);

    showToast(`已在夹具${afterIndex + 1}后添加新夹具`);
    console.log(`设备工站 ${stationIndex} 在夹具 ${afterIndex} 后添加新夹具`);
}

/**
 * 删除夹具块
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 */
function deleteFixtureBlock(stationIndex, fixtureIndex) {
    const container = document.getElementById(`fixtures-container-${stationIndex}`);
    if (!container) {
        console.error(`未找到夹具容器 fixtures-container-${stationIndex}`);
        return;
    }

    const fixtureBlocks = container.querySelectorAll('.fixture-block');
    if (fixtureBlocks.length <= 1) {
        showToast('至少需要保留一个夹具');
        return;
    }

    if (fixtureIndex >= 0 && fixtureIndex < fixtureBlocks.length) {
        const fixtureBlock = fixtureBlocks[fixtureIndex];
        const fixtureName = fixtureBlock.querySelector('h6').textContent.trim();

        if (confirm(`确定要删除 "${fixtureName}" 吗？`)) {
            fixtureBlock.remove();

            // 重新索引所有夹具块
            reindexFixtureBlocks(stationIndex);

            // 更新设备工站数据
            updateEquipmentStationFixtures(stationIndex);

            showToast(`已删除夹具：${fixtureName}`);
            console.log(`设备工站 ${stationIndex} 删除夹具 ${fixtureIndex}`);
        }
    }
}

/**
 * 重新索引夹具块
 * @param {number} stationIndex - 工站索引
 */
function reindexFixtureBlocks(stationIndex) {
    const container = document.getElementById(`fixtures-container-${stationIndex}`);
    if (!container) return;

    const fixtureBlocks = container.querySelectorAll('.fixture-block');
    fixtureBlocks.forEach((block, newIndex) => {
        // 更新data-fixture-index属性
        block.setAttribute('data-fixture-index', newIndex);

        // 更新所有相关的ID和onclick事件
        updateFixtureBlockIds(block, stationIndex, newIndex);
    });
}

// ====== 夹具文件相关函数全局暴露 ======
// 确保夹具文件相关函数暴露到全局作用域
window.handleFixtureFilesUpload = handleFixtureFilesUpload;
window.updateFixtureFilesPreview = updateFixtureFilesPreview;
window.getFileIcon = getFileIcon;
window.formatFileSize = formatFileSize;
window.previewFixtureFile = previewFixtureFile;
window.downloadFixtureFile = downloadFixtureFile;
window.deleteFixtureFile = deleteFixtureFile;
window.clearAllFixtureFiles = clearAllFixtureFiles;

// 暴露多夹具管理函数
window.addFixtureBlock = addFixtureBlock;
window.addFixtureBlockAfter = addFixtureBlockAfter;
window.deleteFixtureBlock = deleteFixtureBlock;
window.reindexFixtureBlocks = reindexFixtureBlocks;

/**
 * 更新夹具块的ID和事件
 * @param {Element} block - 夹具块元素
 * @param {number} stationIndex - 工站索引
 * @param {number} newIndex - 新的夹具索引
 */
function updateFixtureBlockIds(block, stationIndex, newIndex) {
    // 更新文件输入ID
    const fileInput = block.querySelector(`input[type="file"]`);
    if (fileInput) {
        fileInput.id = `fixture-files-input-${stationIndex}-${newIndex}`;
        fileInput.setAttribute('onchange', `handleFixtureFilesUpload(event, ${stationIndex}, ${newIndex})`);
    }

    // 更新文件预览区域ID
    const previewArea = block.querySelector('[id^="fixture-files-preview-"]');
    if (previewArea) {
        previewArea.id = `fixture-files-preview-${stationIndex}-${newIndex}`;
    }

    // 更新清空按钮ID
    const clearBtn = block.querySelector('[id^="clear-fixture-files-btn-"]');
    if (clearBtn) {
        clearBtn.id = `clear-fixture-files-btn-${stationIndex}-${newIndex}`;
        clearBtn.setAttribute('onclick', `clearAllFixtureFiles(${stationIndex}, ${newIndex})`);
    }

    // 更新上传按钮onclick
    const uploadBtn = block.querySelector('button[onclick*="fixture-files-input"]');
    if (uploadBtn) {
        uploadBtn.setAttribute('onclick', `document.getElementById('fixture-files-input-${stationIndex}-${newIndex}').click()`);
    }

    // 更新夹具名称编辑事件
    const nameElement = block.querySelector('h6[ondblclick*="makeFixtureNameEditable"]');
    if (nameElement) {
        nameElement.setAttribute('ondblclick', `makeFixtureNameEditable(${stationIndex}, ${newIndex}, this)`);
    }

    // 更新操作按钮事件
    const addBtn = block.querySelector('button[onclick*="addFixtureBlockAfter"]');
    if (addBtn) {
        addBtn.setAttribute('onclick', `addFixtureBlockAfter(${stationIndex}, ${newIndex})`);
    }

    const deleteBtn = block.querySelector('button[onclick*="deleteFixtureBlock"]');
    if (deleteBtn) {
        deleteBtn.setAttribute('onclick', `deleteFixtureBlock(${stationIndex}, ${newIndex})`);
    }

    // 更新textarea的onchange事件
    const textareas = block.querySelectorAll('textarea[onchange*="updateFixtureInfo"]');
    textareas.forEach((textarea, fieldIndex) => {
        const fieldNames = ['mechanical_requirements', 'electrical_requirements', 'error_prevention_requirements'];
        if (fieldIndex < fieldNames.length) {
            textarea.setAttribute('onchange', `updateFixtureInfo(${stationIndex}, ${newIndex}, '${fieldNames[fieldIndex]}', this.value)`);
        }
    });
}

/**
 * 更新夹具信息
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 * @param {string} field - 字段名
 * @param {string} value - 字段值
 */
function updateFixtureInfo(stationIndex, fixtureIndex, field, value) {
    // 更新设备工站数据
    updateEquipmentStationFixtures(stationIndex);

    console.log(`设备工站 ${stationIndex} 夹具 ${fixtureIndex} 字段 ${field} 更新为:`, value);
}

/**
 * 更新设备工站的夹具数据
 * @param {number} stationIndex - 工站索引
 */
function updateEquipmentStationFixtures(stationIndex) {
    if (!window.equipmentStationsData || !window.equipmentStationsData[stationIndex]) {
        console.error(`设备工站数据不存在: ${stationIndex}`);
        return;
    }

    const container = document.getElementById(`fixtures-container-${stationIndex}`);
    if (!container) return;

    const fixtureBlocks = container.querySelectorAll('.fixture-block');
    const fixtures = [];

    fixtureBlocks.forEach((block, index) => {
        const nameElement = block.querySelector('h6');
        const textareas = block.querySelectorAll('textarea');

        const fixture = {
            fixture_name: nameElement ? nameElement.textContent.replace('🔧 ', '').trim() : `夹具${index + 1}`,
            mechanical_requirements: textareas[0] ? textareas[0].value : '',
            electrical_requirements: textareas[1] ? textareas[1].value : '',
            error_prevention_requirements: textareas[2] ? textareas[2].value : ''
        };

        fixtures.push(fixture);
    });

    // 更新设备工站数据
    if (!window.equipmentStationsData[stationIndex].equipment_details) {
        window.equipmentStationsData[stationIndex].equipment_details = {};
    }

    window.equipmentStationsData[stationIndex].equipment_details.fixtures = fixtures;

    console.log(`设备工站 ${stationIndex} 夹具数据已更新:`, fixtures);
}

/**
 * 编辑夹具名称
 * @param {number} stationIndex - 工站索引
 * @param {number} fixtureIndex - 夹具索引
 * @param {Element} element - 名称元素
 */
function makeFixtureNameEditable(stationIndex, fixtureIndex, element) {
    const currentText = element.textContent.replace('🔧 ', '').trim();
    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentText;
    input.style.cssText = element.style.cssText;
    input.style.border = '1px solid #fa8c16';
    input.style.background = '#fff';
    input.style.width = '150px';

    // 替换元素
    element.parentNode.replaceChild(input, element);
    input.focus();
    input.select();

    // 保存函数
    const saveEdit = () => {
        const newText = input.value.trim() || `夹具${fixtureIndex + 1}`;
        const newElement = document.createElement('h6');
        newElement.innerHTML = `🔧 ${newText}`;
        newElement.style.cssText = element.style.cssText;
        newElement.setAttribute('ondblclick', `makeFixtureNameEditable(${stationIndex}, ${fixtureIndex}, this)`);
        newElement.setAttribute('onmouseover', "this.style.backgroundColor='#fff2e6'");
        newElement.setAttribute('onmouseout', "this.style.backgroundColor='transparent'");
        newElement.setAttribute('title', '双击编辑夹具名称');

        input.parentNode.replaceChild(newElement, input);

        // 更新设备工站数据
        updateEquipmentStationFixtures(stationIndex);

        console.log(`夹具名称已更新: ${newText}`);
    };

    // 事件监听
    input.addEventListener('blur', saveEdit);
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            saveEdit();
        } else if (e.key === 'Escape') {
            // 取消编辑
            input.parentNode.replaceChild(element, input);
        }
    });
}

// 暴露新增的函数
window.updateFixtureBlockIds = updateFixtureBlockIds;
window.updateFixtureInfo = updateFixtureInfo;
window.updateEquipmentStationFixtures = updateEquipmentStationFixtures;
window.makeFixtureNameEditable = makeFixtureNameEditable;

console.log('[DEBUG] 夹具文件和多夹具管理相关函数已暴露到全局作用域');

    </script>

    <!-- 加载遮罩层 -->
    <div class="loading-overlay" id="loading-overlay" style="display:none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loading-text">正在智能分析中...</div>
        </div>
    </div>
</body>
</html>