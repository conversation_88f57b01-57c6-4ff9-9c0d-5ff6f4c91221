<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤功能测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafbfc;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background-color 0.2s;
        }
        .btn.primary {
            background: #1890ff;
            color: white;
        }
        .btn.success {
            background: #52c41a;
            color: white;
        }
        .btn.danger {
            background: #ff4d4f;
            color: white;
        }
        .debug-log {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            padding: 16px;
            margin-top: 20px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 步骤功能测试页面</h1>
        
        <div class="test-section">
            <h2>📋 功能测试控制面板</h2>
            <button class="btn primary" onclick="initTestData()">初始化测试数据</button>
            <button class="btn success" onclick="testFunctions()">测试所有函数</button>
            <button class="btn success" onclick="testInsertBefore()">测试前插入</button>
            <button class="btn success" onclick="testInsertAfter()">测试后插入</button>
            <button class="btn danger" onclick="testDelete()">测试删除</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
            
            <div id="status-display">
                <div class="status success">
                    ✅ 页面已加载，可以开始测试
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🏭 工艺工站显示区域</h2>
            <div id="stations-list" style="min-height: 200px; border: 1px dashed #ccc; padding: 20px;">
                工站将在这里显示...
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 调试日志</h2>
            <div id="debug-log" class="debug-log">
                调试信息将在这里显示...
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>

    <script>
        // 全局变量
        let testStationIndex = 0;
        let testStepIndex = 0;

        // 调试日志功能
        function addLog(message, type = 'info') {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            debugLog.textContent += logEntry;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('debug-log').textContent = '';
        }

        function updateStatus(message, type = 'success') {
            const statusDisplay = document.getElementById('status-display');
            const statusClass = type === 'error' ? 'error' : 'success';
            const icon = type === 'error' ? '❌' : '✅';
            statusDisplay.innerHTML = `<div class="status ${statusClass}">${icon} ${message}</div>`;
        }

        // 初始化测试数据
        function initTestData() {
            addLog('开始初始化测试数据...');
            
            try {
                // 创建测试工站数据
                const testStation = {
                    station_number: '10',
                    station_name: '测试工站',
                    content: '这是测试工站的内容',
                    process_steps: [
                        {
                            step_number: '1',
                            description: '第一个测试步骤',
                            operator: '人',
                            quality_requirements: '质量要求1',
                            error_prevention: '防错要求1'
                        },
                        {
                            step_number: '2',
                            description: '第二个测试步骤',
                            operator: '设备',
                            quality_requirements: '质量要求2',
                            error_prevention: '防错要求2'
                        }
                    ]
                };

                // 设置全局数据
                if (typeof processStationsData !== 'undefined') {
                    processStationsData.length = 0; // 清空现有数据
                    processStationsData.push(testStation);
                    addLog('测试数据已设置到processStationsData');
                } else {
                    window.processStationsData = [testStation];
                    addLog('创建了新的processStationsData');
                }

                // 生成工站显示
                if (typeof StationGenerator !== 'undefined') {
                    if (!window.stationGenerator) {
                        window.stationGenerator = new StationGenerator();
                        addLog('创建了新的stationGenerator实例');
                    }
                    stationGenerator.generateProcessStations([testStation]);
                    addLog('工站生成完成');
                } else {
                    addLog('StationGenerator类未定义', 'error');
                    updateStatus('StationGenerator类未定义', 'error');
                    return;
                }

                updateStatus('测试数据初始化成功');
                addLog('测试数据初始化完成');

            } catch (error) {
                addLog(`初始化失败: ${error.message}`, 'error');
                updateStatus(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试所有函数是否存在
        function testFunctions() {
            addLog('开始测试函数存在性...');
            
            const functions = [
                'insertProcessStepBefore',
                'insertProcessStepAfter', 
                'deleteProcessStep',
                'insertProcessStep',
                'addProcessStep',
                'showStepInsertMenu',
                'hideStepInsertMenu',
                'hideAllStepInsertMenus'
            ];
            
            let allExists = true;
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                addLog(`${funcName}: ${exists ? '存在' : '不存在'}`, exists ? 'info' : 'error');
                if (!exists) allExists = false;
            });
            
            if (allExists) {
                updateStatus('所有函数都存在');
                addLog('所有函数检查通过');
            } else {
                updateStatus('部分函数不存在', 'error');
                addLog('函数检查失败', 'error');
            }
        }

        // 测试前插入功能
        function testInsertBefore() {
            addLog('测试前插入功能...');
            
            try {
                if (typeof insertProcessStepBefore === 'function') {
                    insertProcessStepBefore(testStationIndex, 1); // 在第二个步骤前插入
                    addLog('调用insertProcessStepBefore(0, 1)成功');
                    updateStatus('前插入测试成功');
                } else {
                    addLog('insertProcessStepBefore函数不存在', 'error');
                    updateStatus('前插入测试失败', 'error');
                }
            } catch (error) {
                addLog(`前插入测试失败: ${error.message}`, 'error');
                updateStatus(`前插入测试失败: ${error.message}`, 'error');
            }
        }

        // 测试后插入功能
        function testInsertAfter() {
            addLog('测试后插入功能...');
            
            try {
                if (typeof insertProcessStepAfter === 'function') {
                    insertProcessStepAfter(testStationIndex, 0); // 在第一个步骤后插入
                    addLog('调用insertProcessStepAfter(0, 0)成功');
                    updateStatus('后插入测试成功');
                } else {
                    addLog('insertProcessStepAfter函数不存在', 'error');
                    updateStatus('后插入测试失败', 'error');
                }
            } catch (error) {
                addLog(`后插入测试失败: ${error.message}`, 'error');
                updateStatus(`后插入测试失败: ${error.message}`, 'error');
            }
        }

        // 测试删除功能
        function testDelete() {
            addLog('测试删除功能...');
            
            try {
                if (typeof deleteProcessStep === 'function') {
                    // 注意：这会弹出确认对话框
                    addLog('准备调用deleteProcessStep(0, 0) - 会弹出确认对话框');
                    deleteProcessStep(testStationIndex, 0); // 删除第一个步骤
                    addLog('deleteProcessStep调用完成');
                    updateStatus('删除测试完成（取决于用户确认）');
                } else {
                    addLog('deleteProcessStep函数不存在', 'error');
                    updateStatus('删除测试失败', 'error');
                }
            } catch (error) {
                addLog(`删除测试失败: ${error.message}`, 'error');
                updateStatus(`删除测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面DOM加载完成');
            addLog(`StationGenerator类: ${typeof StationGenerator !== 'undefined' ? '已定义' : '未定义'}`);
            addLog(`processStationsData: ${typeof processStationsData !== 'undefined' ? '已定义' : '未定义'}`);
            
            // 检查关键函数
            const keyFunctions = ['insertProcessStepBefore', 'insertProcessStepAfter', 'deleteProcessStep'];
            keyFunctions.forEach(funcName => {
                addLog(`${funcName}: ${typeof window[funcName] === 'function' ? '已定义' : '未定义'}`);
            });
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.error.message}`, 'error');
            updateStatus(`发生错误: ${event.error.message}`, 'error');
        });

        // 监听点击事件，用于调试
        document.addEventListener('click', function(event) {
            if (event.target.tagName === 'BUTTON' && event.target.textContent.includes('+')) {
                addLog(`点击了插入按钮: ${event.target.outerHTML}`);
            }
            if (event.target.tagName === 'BUTTON' && event.target.textContent.includes('×')) {
                addLog(`点击了删除按钮: ${event.target.outerHTML}`);
            }
        });
    </script>
</body>
</html>
