# 设备参数更换说明

## 更换概述
根据用户需求，将设备工站的参数从原来的"长度、宽度、高度、功率"更换为新的"设备长度、设备宽度、设备高度、节拍、换型时间"参数。

## 参数对比

### 原参数
- 长度(mm)
- 宽度(mm) 
- 高度(mm)
- 功率(kW)

### 新参数
- 设备长度(mm)
- 设备宽度(mm)
- 设备高度(mm)
- 节拍(s)
- 换型时间(min)

## 修改内容

### 1. HTML结构更新 (`static/js/station_generator.js`)

#### 1.1 参数区域标题
```javascript
// 原标题
<h6>设备参数</h6>

// 新标题  
<h6>基本参数</h6>
```

#### 1.2 参数输入框更新
```javascript
// 原参数输入框
<label>长度(mm):</label>
<input id="equipment-length-${index}" placeholder="设备长度" />

<label>宽度(mm):</label>
<input id="equipment-width-${index}" placeholder="设备宽度" />

<label>高度(mm):</label>
<input id="equipment-height-${index}" placeholder="设备高度" />

<label>功率(kW):</label>
<input id="equipment-power-${index}" placeholder="设备功率" />

// 新参数输入框
<label>设备长度(mm):</label>
<input id="equipment-length-${index}" placeholder="请输入设备长度" />

<label>设备宽度(mm):</label>
<input id="equipment-width-${index}" placeholder="请输入设备宽度" />

<label>设备高度(mm):</label>
<input id="equipment-height-${index}" placeholder="请输入设备高度" />

<label>节拍(s):</label>
<input id="equipment-cycle-time-${index}" placeholder="请输入节拍时间" />

<label>换型时间(min):</label>
<input id="equipment-changeover-time-${index}" placeholder="请输入换型时间" />
```

#### 1.3 支持的设备工站格式
- ✅ **传统格式** (`createTraditionalEquipmentStationHtml`)
- ✅ **Equipment和Fixture分离格式** (`createEquipmentFixtureStationHtml`)
- ✅ **简化Markdown格式** (`createSimpleMarkdownStationHtml`)

### 2. 数据管理更新 (`static/js/station_manager.js`)

#### 2.1 数据恢复函数更新
```javascript
// 原参数恢复
const parameters = {};
if (details.equipment_length) parameters.length = details.equipment_length;
if (details.equipment_width) parameters.width = details.equipment_width;
if (details.equipment_height) parameters.height = details.equipment_height;
if (details.equipment_power) parameters.power = details.equipment_power;

// 新参数恢复
const parameters = {};
if (details.equipment_length) parameters.length = details.equipment_length;
if (details.equipment_width) parameters.width = details.equipment_width;
if (details.equipment_height) parameters.height = details.equipment_height;
if (details.equipment_cycle_time) parameters.cycle_time = details.equipment_cycle_time;
if (details.equipment_changeover_time) parameters.changeover_time = details.equipment_changeover_time;
```

#### 2.2 数据保存函数更新
```javascript
// 原参数保存
details.equipment_length = parameters.length || '';
details.equipment_width = parameters.width || '';
details.equipment_height = parameters.height || '';
details.equipment_power = parameters.power || '';

// 新参数保存
details.equipment_length = parameters.length || '';
details.equipment_width = parameters.width || '';
details.equipment_height = parameters.height || '';
details.equipment_cycle_time = parameters.cycle_time || '';
details.equipment_changeover_time = parameters.changeover_time || '';
```

### 3. 前端函数更新 (`templates/index.html`)

#### 3.1 参数映射更新
```javascript
// 新增特殊参数名映射
Object.keys(data.parameters).forEach(paramName => {
    let inputId = `equipment-${paramName}-${stationIndex}`;
    // 处理特殊参数名映射
    if (paramName === 'cycle_time') {
        inputId = `equipment-cycle-time-${stationIndex}`;
    } else if (paramName === 'changeover_time') {
        inputId = `equipment-changeover-time-${stationIndex}`;
    }
    
    const inputElement = document.getElementById(inputId);
    if (inputElement) {
        inputElement.value = data.parameters[paramName];
    }
});
```

### 4. 数据结构变化

#### 4.1 全局参数数据结构
```javascript
// 原数据结构
window.equipmentParametersData = {
    [stationIndex]: {
        length: "1000",
        width: "800", 
        height: "1500",
        power: "5.5"
    }
};

// 新数据结构
window.equipmentParametersData = {
    [stationIndex]: {
        length: "1000",
        width: "800", 
        height: "1500",
        cycle_time: "30",
        changeover_time: "15"
    }
};
```

#### 4.2 设备工站数据结构
```javascript
// 新增字段
equipment_details: {
    // 原有字段保持不变
    equipment_type: '',
    technical_requirements: '',
    equipment_parameters: '',
    safety_requirements: '',
    
    // 图片数据
    equipment_image_data: '',
    equipment_image_filename: '',
    
    // 更新后的参数字段
    equipment_length: '',
    equipment_width: '',
    equipment_height: '',
    equipment_cycle_time: '',      // 新增：节拍
    equipment_changeover_time: ''  // 新增：换型时间
    
    // 移除：equipment_power (功率)
}
```

## 用户界面变化

### 1. 参数标签更新
- **更明确的标签**：从"长度"改为"设备长度"，增加了明确性
- **新增参数**：节拍(s) 和 换型时间(min)
- **移除参数**：功率(kW)

### 2. 输入提示更新
- **统一格式**：所有placeholder都改为"请输入..."格式
- **更友好的提示**：提供更清晰的输入指导

### 3. 参数单位显示
- **设备长度(mm)**：毫米单位
- **设备宽度(mm)**：毫米单位
- **设备高度(mm)**：毫米单位
- **节拍(s)**：秒单位
- **换型时间(min)**：分钟单位

## 兼容性保证

### 1. 向后兼容
- ✅ **现有数据保持**：原有的长度、宽度、高度数据继续有效
- ✅ **数据结构扩展**：新增字段不影响现有功能
- ✅ **渐进式更新**：用户可以逐步填写新参数

### 2. 数据迁移
- ✅ **自动映射**：原有参数自动映射到新字段
- ✅ **数据保留**：不会丢失任何现有数据
- ✅ **平滑过渡**：用户体验无缝切换

## 功能验证

### 验证项目
1. ✅ 新参数输入框正确显示
2. ✅ 参数标签和单位正确
3. ✅ 输入后自动保存功能正常
4. ✅ 页面刷新后数据正确恢复
5. ✅ 所有设备工站格式都支持新参数
6. ✅ 原有功能不受影响
7. ✅ 数据结构正确更新
8. ✅ 特殊参数名映射正确工作

## 总结

成功将设备参数从原来的4个参数更换为新的5个参数：

### 🔄 **参数变更**
- **保留**：设备长度、设备宽度、设备高度（标签更明确）
- **新增**：节拍(s)、换型时间(min)
- **移除**：功率(kW)

### 🎯 **技术实现**
- **HTML结构**：更新了所有三种设备工站格式的参数区域
- **数据管理**：扩展了数据保存和恢复逻辑
- **前端映射**：添加了特殊参数名的映射处理
- **兼容性**：确保向后兼容和数据完整性

### 📊 **用户体验**
- **更清晰的标签**：参数名称更加明确
- **更友好的提示**：统一的输入提示格式
- **更实用的参数**：节拍和换型时间更符合实际需求

现在设备工站的参数更加贴合实际使用需求，为设备管理提供了更有价值的信息！🎉
