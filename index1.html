<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Line SPEC 系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/antd.min.css">    <style>
        /* 新行样式 */
        .new-row {
            background-color: #f6ffed;
            transition: background-color 0.3s ease;
        }

        .new-row:focus-within {
            background-color: #e6f7ff;
        }

        td[contenteditable="true"][placeholder]:empty:before {
            content: attr(placeholder);
            color: #999;
            cursor: text;
        }

        .container {
            display: flex;
            height: 100vh;
            margin: 0;
        }        .nav-menu {
            width: 180px;  /* 调小目录宽度 */
            background: #f0f2f5;
            padding: 15px;
            min-width: 180px;  /* 确保最小宽度 */
        }
        .content {
            flex: 1.6;  /* 增大内容区域权重 */
            padding: 20px;
            overflow: auto;
            min-width: 0;  /* 允许flex内容收缩 */
        }
        .chat-panel {
            width: 300px;  /* 保持不变 */
            background: #f0f2f5;
            padding: 20px;
            min-width: 300px;  /* 确保最小宽度 */
        }
        .edit-form {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
            z-index: 1000;
        }        .edit-form .form-group {
            margin-bottom: 15px;
        }
        
        .edit-form .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .edit-form input,
        .edit-form select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .edit-form input:focus,
        .edit-form select:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
            outline: none;
        }
        
        .edit-form .shared-project-group {
            transition: all 0.3s ease;
        }
        .edit-form-buttons {
            text-align: right;
            margin-top: 15px;
        }
        .edit-btn {
            padding: 2px 8px;
            margin-left: 5px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 999;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 14px;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        tr:hover {
            background-color: #f9f9f9;
        }
        td select, td input {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 4px;
            width: 100%;
            box-sizing: border-box;
        }        td select:focus, td input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .part-status-select {
            background-color: white;
            transition: all 0.3s ease;
        }

        .part-status-select:hover {
            border-color: #40a9ff;
        }

        .shared-project-name[data-placeholder]:empty:before {
            content: attr(data-placeholder);
            color: #999;
        }

        td[contenteditable="true"]:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        .add-row-btn {
            margin: 10px 0;
            padding: 5px 15px;
            background: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .category-buttons {
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .category-button {
            padding: 8px 15px;
            width: 100%;
            border: 2px solid #1890ff;
            background: white;
            color: #1890ff;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            text-align: left;
        }
        .category-button.selected {
            background: #1890ff;
            color: white;
        }
        .generate-btn {
            margin: 10px 0;
            padding: 8px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }

        .generate-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            display: none;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .generate-popup pre {
            white-space: pre-wrap;
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
            line-height: 1.6;
        }

        .generate-popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .generate-popup-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            color: #999;
        }

        .copy-btn {
            background: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
            display: block;
            width: 100%;
            transition: background 0.3s;
        }

        .copy-btn:hover {
            background: #389e0d;
        }

        .copy-btn.copied {
            background: #1890ff;
        }

        .new-row {
            background-color: #f6ffed;
            transition: background-color 0.3s ease;
        }

        .new-row:focus-within {
            background-color: #e6f7ff;
        }

        td[contenteditable="true"][placeholder]:empty:before {
            content: attr(placeholder);
            color: #999;
            cursor: text;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-menu" id="nav-menu">
            <h2>目录</h2>            <ul style="list-style: none; padding-left: 0;">
                <li style="margin-bottom: 10px;"><a href="#" data-content="cover" style="text-decoration: none; color: #1890ff;">0.制造规范封面</a></li>
                <li style="margin-bottom: 10px;"><a href="#" data-content="product" style="text-decoration: none; color: #1890ff;">1.产品信息（APP）</a></li>
                <li style="margin-bottom: 10px;"><a href="#" data-content="process" style="text-decoration: none; color: #1890ff;">2.工艺要求（APP）</a></li>
                <li style="margin-bottom: 10px;"><a href="#" data-content="equipment" style="text-decoration: none; color: #1890ff;">3.设备要求（AEP）</a></li>
                <li style="margin-bottom: 10px;"><a href="#" data-content="fixtures" style="text-decoration: none; color: #1890ff;">4.设备夹具清单(AEP)</a></li>
                <li style="margin-bottom: 10px;"><a href="#" data-content="terms" style="text-decoration: none; color: #1890ff;">5.通用条款</a></li>
            </ul>
        </div>
        
        <div class="content" id="content">
            <h1>欢迎使用 Line SPEC 系统</h1>
            <p>请从左侧选择要查看的内容</p>
        </div>
        
        <div class="chat-panel">
            <h2>AI 对话</h2>
            <div id="chat-history" style="height: 400px; overflow-y: auto; border: 1px solid #ddd; margin-bottom: 10px; padding: 10px;">
            </div>
            <div>
                <input type="text" id="chat-input" placeholder="请输入您的问题..." style="width: 100%; padding: 5px;">
                <button onclick="sendMessage()" style="width: 100%; margin-top: 10px;">发送</button>
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay"></div>
    <div class="edit-form" id="edit-form">
        <h3 id="edit-form-title">编辑信息</h3>
        <div id="edit-form-fields"></div>
        <div class="edit-form-buttons">
            <button onclick="cancelEdit()" style="margin-right: 10px">取消</button>
            <button onclick="saveEdit()" style="background: #1890ff; color: white">保存</button>
        </div>
    </div>    

    <!-- 添加生成信息的弹出窗口 -->
    <div class="generate-popup" id="generate-popup">
        <div class="generate-popup-header">
            <h3>LineSpec 生成结果</h3>
            <button class="generate-popup-close" onclick="closeGeneratePopup()">&times;</button>
        </div>
        <div id="generate-content"></div>
    </div>

    <script>
        // 全局变量声明
        let currentEditType = '';
        let currentEditIndex = -1;
        let selectedCategory = '';
        const editingRows = new Set(); // 存储正在编辑的行索引

        // 更新工艺描述
        function updateProcessDescription(value) {
            data.processDescription = value;
            saveDataAfterChange();
        }

        // 更新具体要求
        function updateSpecificRequirements(value) {
            data.specificRequirements = value;
            saveDataAfterChange();
        }

        // 从 localStorage 加载数据
        function loadData() {
            const savedData = localStorage.getItem('lineSpecData');
            if (savedData) {
                try {
                    const loadedData = JSON.parse(savedData);
                    Object.assign(data, loadedData);
                    // 重新显示当前页面
                    if (currentEditType) {
                        showContent(currentEditType);
                    }
                } catch (error) {
                    console.error('Failed to load data:', error);
                }
            }
        }

        // 保存数据到 localStorage
        function saveData() {
            try {
                localStorage.setItem('lineSpecData', JSON.stringify(data));
                console.log('数据保存成功');
            } catch (error) {
                console.error('保存数据失败:', error);
            }
        }

        // 在每次修改后保存数据
        function saveDataAfterChange() {
            saveData();
        }

        // 更新工艺描述
        function updateProcessDescription(value) {
            data.processDescription = value;
            saveDataAfterChange();
        }

        // 更新具体要求
        function updateSpecificRequirements(value) {
            data.specificRequirements = value;
            saveDataAfterChange();
        }

        // 存储数据的对象
        const data = {
            product: [
                { 
                    id: 'P001', 
                    projectName: '',
                    name: '示例零件1', 
                    unitUsage: '1',
                    packageSize: '100x100x100mm', 
                    unitPackageQty: '100',
                    partStatus: '新制',
                    sharedProjectName: '',
                    remarks: ''
                }
            ],
            processLayout: {
                layoutImage: '',
                lineLength: '',
                lineWidth: '',
                lineHeight: '',
                lineTakt: ''
            },
            currentStation: {
                number: '',
                name: ''
            },
            processDescription: '',  // 新增字段：工艺描述
            specificRequirements: '', // 新增字段：具体要求
            process: [
                { 
                    id: '1', 
                    step: '工序1', 
                    requirement: '示例工艺要求', 
                    takt: '',
                    type: '',
                    productFeature: '',
                    errorProof: '示例防错要求'
                }
            ],            equipmentLayout: {
                layoutImage: '',
                length: '',
                width: '',
                height: '',
                takt: '',
                changeTime: '',
                standardNo: '',
                mechanicalRequirement: '',
                electricalRequirement: ''
            },
            equipmentStations: [], // 存储设备信息的数组
            // station 结构：
            // {
            //     number: '',  // 站位编号
            //     name: '',    // 站位名称
            //     layoutImage: '', // 设备布局图
            //     length: '',  // 设备长度
            //     width: '',   // 设备宽度
            //     height: '',  // 设备高度
            //     takt: '',    // 节拍
            //     changeTime: '', // 换型时间
            //     mechanicalRequirement: '', // 机械要求
            //     electricalRequirement: '', // 电气要求
            //     fixtureDetail: {  // 夹具分解机构详细要求
            //         shown: false,  // 是否显示
            //         images: [],    // 夹具分解机构图片列表
            //         keyPoints: '', // 关键点要求
            //         measures: ''   // 具体要求和措施
            //     }
            // }
            fixtures: [
                { id: 'F001', name: '示例夹具1', quantity: '2', techRequirement: '示例技术要求' }
            ],
            terms: {
                general: '',
                safety: '',
                quality: ''
            },
            stations: [] // 新增字段：站位信息
        };

        // 从服务器加载数据
        async function loadServerData() {
            try {
                const response = await fetch('/api/data');
                const loadedData = await response.json();
                Object.assign(data, loadedData);
                // 重新显示当前页面
                if (currentEditType) {
                    showContent(currentEditType);
                }
            } catch (error) {
                console.error('Failed to load data:', error);
            }
        }

        // 保存数据到服务器
        async function saveServerData() {
            try {
                const response = await fetch('/api/data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                console.log(result.message);
            } catch (error) {
                console.error('Failed to save data:', error);
            }
        }

        // 修改模块选择函数来保存选择状态
        function handleModuleChange(selectedModule) {
            // 清除项目名称字段的值
            const projectNameCell = document.querySelector('td[contenteditable="true"]');
            if (projectNameCell) {
                projectNameCell.textContent = '请输入项目名称';
            }

            // 保存选择的模块类别
            localStorage.setItem('selectedModule', selectedModule);

            console.log('Selected module:', selectedModule);
        }

        // 处理产品页面模块选择改变事件，并保存选择状态
        function handleProductModuleChange(selectedModule) {
            // 获取类别按钮容器
            const categoryButtonsContainer = document.querySelector('.category-buttons');
            // 定义每个模块对应的类别
            const moduleCategories = {
                'SAB': ['A', 'B', 'C', 'D', 'E', 'F'],
                'FCA': ['A', 'B', 'C', 'D'],
                'PAB': ['A', 'B', 'C', 'D', 'E', 'F'],
                'KAB': ['A', 'B'],
                'IC': ['A', 'B', 'C'],
                'DAB': ['A', 'B', 'C', 'D']
            };

            // 清空当前选中的类别
            selectedCategory = '';
            
            // 获取当前模块的类别列表
            const categories = moduleCategories[selectedModule] || [];
            
            // 生成类别按钮HTML
            const buttonsHtml = categories.map(category => `
                <button class="category-button" data-category="${category}">${category}类</button>
            `).join('');
            
            // 更新按钮容器内容
            categoryButtonsContainer.innerHTML = buttonsHtml;
            
            // 保存选择的模块
            localStorage.setItem('selectedProductModule', selectedModule);
            
            // 重新添加事件监听器
            attachCategoryButtonListeners();
            
            // 更新产品列表显示
            updateProductList();
            
            console.log('Selected product module:', selectedModule);
        }

        // 为类别按钮添加点击事件监听器的函数
        function attachCategoryButtonListeners() {
            document.querySelectorAll('.category-button').forEach(button => {
                button.addEventListener('click', () => {
                    const category = button.dataset.category;
                    
                    // 移除所有按钮的选中状态
                    document.querySelectorAll('.category-button').forEach(btn => {
                        btn.classList.remove('selected');
                    });
                    
                    // 如果点击的是已选中的按钮，取消选择
                    if (selectedCategory === category) {
                        selectedCategory = '';
                        localStorage.removeItem('selectedCategory');
                    } else {
                        // 否则选中新按钮
                        button.classList.add('selected');
                        selectedCategory = category;
                        localStorage.setItem('selectedCategory', category);
                    }
                    
                    // 更新产品列表显示
                    updateProductList();
                });

                // 恢复之前选中的类别
                if (button.dataset.category === localStorage.getItem('selectedCategory')) {
                    button.click();
                }
            });
        }

        // 页面加载时恢复选择状态的函数
        function restoreSelections() {
            // 恢复封面页的模块选择
            const savedModule = localStorage.getItem('selectedModule');
            if (savedModule) {
                const moduleSelect = document.getElementById('moduleSelect');
                if (moduleSelect) {
                    moduleSelect.value = savedModule;
                    handleModuleChange(savedModule);
                }
            }

            // 恢复产品页面的模块选择
            const savedProductModule = localStorage.getItem('selectedProductModule');
            if (savedProductModule) {
                const productModuleSelect = document.getElementById('productModuleSelect');
                if (productModuleSelect) {
                    productModuleSelect.value = savedProductModule;
                    handleProductModuleChange(savedProductModule);
                }
            }
        }

        // 更新产品列表显示
        function updateProductList() {
            const selectedModule = document.getElementById('productModuleSelect')?.value;
            console.log('Updating product list with:', { selectedModule, selectedCategory });
            
            // 定义每个类别对应的行数
            const categoryRows = {
                'A': 3,
                'B': 4,
                'C': 5,
                'D': 6,
                'E': 7,
                'F': 8
            };
            
            // 根据选择的类别生成对应数量的示例数据
            if (selectedCategory && selectedModule) {
                const rowCount = categoryRows[selectedCategory];
                console.log('Generating rows:', rowCount);
                
                // 生成新的示例数据
                data.product = Array.from({ length: rowCount }, (_, index) => ({
                    id: `P${String(index + 1).padStart(3, '0')}`,
                    projectName: '',
                    name: `${selectedModule}-${selectedCategory}类零件${index + 1}`,
                    unitUsage: '1',
                    packageSize: `${100 + index * 10}x${100 + index * 10}x${100 + index * 10}`,
                    unitPackageQty: '100',
                    partStatus: '新制',
                    sharedProjectName: '',
                    remarks: ''
                }));                // 根据指定顺序排序列
                const tableBody = document.querySelector('#content table');
                if (tableBody) {                const tableHeader = `<tr>
                        <th>新项目名称</th>
                        <th>零件编号</th>
                        <th>零件名称</th>
                        <th>单位用量</th>
                        <th>包装尺寸（长*宽*高mm）</th>
                        <th>单位包装数量</th>
                        <th>零件状态</th>
                        <th>共享项目名称</th>
                        <th>备注</th>
                        <th>操作</th>
                    </tr>`;
                    
                    // 生成表格内容
                    const tableContent = data.product.map((item, index) => `
                        <tr data-index="${index}">
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'projectName', this.textContent)" 
                                onfocus="if(this.textContent === '') this.textContent = ''" 
                                placeholder="点击输入项目名称">${item.projectName || ''}</td>
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'id', this.textContent)">${item.id}</td>
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'name', this.textContent)" 
                                onfocus="if(this.textContent === '') this.textContent = ''" 
                                placeholder="点击输入零件名称">${item.name || ''}</td>
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'unitUsage', this.textContent)" 
                                onfocus="if(this.textContent === '') this.textContent = ''" 
                                placeholder="点击输入用量">${item.unitUsage || ''}</td>
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'packageSize', this.textContent)" 
                                onfocus="if(this.textContent === '') this.textContent = ''" 
                                placeholder="点击输入包装尺寸">${item.packageSize || ''}</td>
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'unitPackageQty', this.textContent)" 
                                onfocus="if(this.textContent === '') this.textContent = ''" 
                                placeholder="点击输入包装数量">${item.unitPackageQty || ''}</td>
                            <td>
                                <select class="part-status-select" onchange="updatePartStatus(${index}, this.value)" style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="新制" ${item.partStatus === '新制' ? 'selected' : ''}>新制</option>
                                    <option value="共用" ${item.partStatus === '共用' ? 'selected' : ''}>共用</option>
                                </select>
                            </td>
                            <td contenteditable="false" 
                                class="shared-project-name" 
                                onblur="updateSharedProjectName(${index}, this.textContent)"
                                style="${item.partStatus === '共用' ? '' : 'color: #999;'}"
                                ${item.partStatus === '共用' ? '' : 'data-placeholder="不适用"'}
                                >
                                ${item.partStatus === '共用' ? (item.sharedProjectName || '') : '不适用'}</td>
                            <td contenteditable="false" 
                                onblur="updateProductField(${index}, 'remarks', this.textContent)" 
                                onfocus="if(this.textContent === '') this.textContent = ''" 
                                placeholder="点击输入备注">${item.remarks || ''}</td>
                            <td>
                                <button class="edit-btn" onclick="toggleRowEdit(${index}, 'product')" style="background: #1890ff;">编辑</button>
                                <button class="edit-btn" onclick="deleteRow('product', ${index})" style="background: #ff4d4f">删除</button>
                            </td>
                        </tr>
                    `).join('');
                    
                    // 更新表格内容
                    tableBody.innerHTML = tableHeader + tableContent;
                }
            } else {
                // 如果没有选择类别或模块，清空数据
                data.product = [];
            }
            
            // 保存更改
            saveDataAfterChange();
        }        // 更新零件状态
        function updatePartStatus(index, value) {
            // 更新数据
            data.product[index].partStatus = value;
            
            // 如果状态不是"共用"，清除共享项目名称
            if (value !== '共用') {
                data.product[index].sharedProjectName = '';
            }
            
            // 获取共享项目名称单元格
            const row = document.querySelectorAll('table tr')[index + 1];
            if (row) {
                const sharedProjectCell = row.querySelector('.shared-project-name');
                if (sharedProjectCell) {
                    if (value === '共用') {
                        sharedProjectCell.removeAttribute('data-placeholder');
                        sharedProjectCell.style.color = '';
                        sharedProjectCell.textContent = data.product[index].sharedProjectName || '';
                    } else {
                        sharedProjectCell.setAttribute('data-placeholder', '不适用');
                        sharedProjectCell.style.color = '#999';
                        sharedProjectCell.textContent = '不适用';
                    }
                }
            }
            
            // 保存更改
            saveDataAfterChange();
        }        // 更新共享项目名称
        function updateSharedProjectName(index, value) {
            // 只有在"共用"状态下才更新共享项目名称
            if (data.product[index].partStatus === '共用') {
                data.product[index].sharedProjectName = value;
                saveDataAfterChange();
            }
        }

        // 更新产品字段的函数
        function updateProductField(index, field, value) {
            data.product[index][field] = value;
            saveDataAfterChange();
        }
        
        // 监听可编辑单元格的变化并保存
        function attachEditableListeners() {
            document.querySelectorAll('[contenteditable="true"]').forEach(cell => {
                cell.addEventListener('blur', () => {
                    // 保存更改到 localStorage
                    saveDataAfterChange();
                });
            });
        }

        // 增强 showContent 函数以支持自动保存
        function showContent(type) {
            const contentDiv = document.getElementById('content');
            currentEditType = type;
            let content = '';
            
            switch(type) {
                case 'cover':
                    content = `
                        <div style="padding: 40px;">
                            <h1 style="text-align: center; font-size: 32px; margin-bottom: 40px;">Line SPEC 系统</h1>
                            
                            <div style="max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <tr>
                                        <td style="padding: 15px; width: 200px; background: #f8f9fa; font-weight: bold;">模块类别</td>
                                        <td style="padding: 15px;">
                                            <select id="moduleSelect" style="width: 200px; padding: 5px; border: 1px solid #ddd; border-radius: 4px;" onchange="handleModuleChange(this.value)">
                                                <option value="">请选择模块类别</option>
                                                <option value="SAB">SAB</option>
                                                <option value="FCA">FCA</option>
                                                <option value="IC">IC</option>
                                                <option value="PAB">PAB</option>
                                                <option value="KAB">KAB</option>
                                                <option value="DAB">DAB</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">项目名称</td>
                                        <td style="padding: 15px;" contenteditable="true">请输入项目名称</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">交付工厂</td>
                                        <td style="padding: 15px;" contenteditable="true">请输入交付工厂</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">产线类型</td>
                                        <td style="padding: 15px;" contenteditable="true">请输入产线类型</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">设备类型</td>
                                        <td style="padding: 15px;">
                                            <select style="width: 200px; padding: 5px; border: 1px solid #ddd; border-radius: 4px;" onchange="handleEquipmentTypeChange(this.value)">
                                                <option value="">请选择设备类型</option>
                                                <option value="manual">手动线</option>
                                                <option value="auto">自动线</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">产线编号</td>
                                        <td style="padding: 15px;" contenteditable="true">请输入产线编号</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">APP项目负责人</td>
                                        <td style="padding: 15px;" contenteditable="true">请输入APP负责人姓名</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">AEP项目负责人</td>
                                        <td style="padding: 15px;" contenteditable="true">请输入AEP负责人姓名</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">产线预验收日期</td>
                                        <td style="padding: 15px;" contenteditable="true" onclick="this.innerHTML='2025-05-20'">点击设置日期</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">产线到入厂日期</td>
                                        <td style="padding: 15px;" contenteditable="true" onclick="this.innerHTML='2025-05-20'">点击设置日期</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 15px; background: #f8f9fa; font-weight: bold;">离线调试完成日期</td>
                                        <td style="padding: 15px;" contenteditable="true" onclick="this.innerHTML='2025-05-20'">点击设置日期</td>
                                    </tr>
                                </table>
                                
                                <div style="margin-top: 30px; text-align: right;">
                                    <div style="font-size: 14px; color: #666;">版本：1.0.0</div>
                                    <div style="font-size: 14px; color: #666; margin-top: 5px;">更新日期：${new Date().toLocaleDateString('zh-CN')}</div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
                case 'product':
                    content = `
        <h2>1.产品信息（APP）</h2>
        <div style="display: flex; margin-bottom: 20px;">
            <!-- 左侧选择区域 - 宽度适应内容 -->
            <div style="width: fit-content; min-width: 80px; margin-right: 15px;">
                <div style="margin-bottom: 12px; background: #f8f9fa; padding: 8px; border-radius: 4px;">
                    <label style="display: block; margin-bottom: 6px; font-weight: bold; font-size: 13px; white-space: nowrap;">模块类别</label>
                    <select id="productModuleSelect" style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;" onchange="handleProductModuleChange(this.value)">
                        <option value="">请选择</option>
                        <option value="SAB">SAB</option>
                        <option value="FCA">FCA</option>
                        <option value="PAB">PAB</option>
                        <option value="KAB">KAB</option>
                        <option value="IC">IC</option>
                        <option value="DAB">DAB</option>
                    </select>
                </div>
                <div style="background: #f8f9fa; padding: 8px; border-radius: 4px;">
                    <label style="display: block; margin-bottom: 6px; font-weight: bold; font-size: 13px; white-space: nowrap;">模块明细</label>
                    <div class="category-buttons" style="display: flex; flex-direction: column; gap: 4px;">
                        <!-- 类别按钮将由 JavaScript 动态生成 -->
                    </div>
                    <button class="generate-btn" onclick="generateLineSpec()" style="margin-top: 12px; width: 100%; font-size: 13px; padding: 4px;">一键生成</button>
                </div>
            </div>
            <!-- 右侧内容区域 - 优化表格布局 -->
            <div style="flex: 1;">
                <div style="overflow-x: auto;">
                    <table style="width: 100%; border-collapse: collapse;">
                        <tr>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">新项目名称</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">零件编号</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">零件名称</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">单位用量</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">包装尺寸(长×宽×高mm)</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">单位包装数量</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">零件状态</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">共享项目名称</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">备注</th>
                            <th style="padding: 8px; white-space: nowrap; font-size: 13px; background: #fafafa;">操作</th>
                        </tr>
                        ${data.product.map((item, index) => `
                            <tr data-index="${index}">
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'projectName', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入项目名称">${item.projectName || ''}</td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'id', this.textContent)">${item.id}</td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'name', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入零件名称">${item.name || ''}</td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'unitUsage', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入用量">${item.unitUsage || ''}</td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'packageSize', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入包装尺寸">${item.packageSize || ''}</td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'unitPackageQty', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入包装数量">${item.unitPackageQty || ''}</td>
                                <td style="padding: 6px; font-size: 13px;">
                                    <select class="part-status-select" onchange="updatePartStatus(${index}, this.value)" style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                        <option value="新制" ${item.partStatus === '新制' ? 'selected' : ''}>新制</option>
                                        <option value="共用" ${item.partStatus === '共用' ? 'selected' : ''}>共用</option>
                                    </select>
                                </td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    class="shared-project-name" 
                                    onblur="updateSharedProjectName(${index}, this.textContent)"
                                    style="${item.partStatus === '共用' ? '' : 'color: #999;'}"
                                    ${item.partStatus === '共用' ? '' : 'data-placeholder="不适用"'}
                                    >
                                    ${item.partStatus === '共用' ? (item.sharedProjectName || '') : '不适用'}</td>
                                <td style="padding: 6px; font-size: 13px;" contenteditable="false" 
                                    onblur="updateProductField(${index}, 'remarks', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入备注">${item.remarks || ''}</td>
                                <td style="padding: 6px; font-size: 13px;">
                                    <button class="edit-btn" onclick="toggleRowEdit(${index}, 'product')" style="background: #1890ff;">编辑</button>
                                    <button class="edit-btn" onclick="deleteRow('product', ${index})" style="background: #ff4d4f">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </table>
                </div>
                <button class="add-row-btn" onclick="addRow('product')" style="margin-top: 10px;">添加新行</button>
            </div>
        </div>
    `;
                    break;
                case 'process':
                    content = `
        <h2>2.工艺要求（APP）</h2>
        
        <!-- 布局容器 -->
        <div style="display: flex; margin-bottom: 20px; gap: 20px; min-height: 400px;">
            <!-- 左侧图片上传区域 -->
            <div style="flex: 2; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; flex-direction: column;">
                <h3 style="margin-bottom: 15px; font-size: 16px; color: #333;">工艺布局图</h3>
                <div id="imagePreviewArea" style="flex: 1; overflow: hidden; text-align: center; margin-bottom: 15px; min-height: 300px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                    ${data.processLayout.layoutImage ? 
                        `<img src="${data.processLayout.layoutImage}" alt="工艺布局图" style="max-width: 100%; max-height: 100%; cursor: pointer;" onclick="showImageModal(this.src)">` : 
                        '<div style="color: #999;">点击下方按钮上传图片</div>'
                    }
                </div>
                <div id="imageUploadArea" style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                    <input type="file" id="layoutImageInput" accept="image/*" style="display: none" onchange="handleImageUpload(event)">
                    <button onclick="document.getElementById('layoutImageInput').click()" 
                            style="padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        上传图片
                    </button>
                    <span style="color: #666; font-size: 12px;">支持 jpg、png 格式图片</span>
                </div>
            </div>
            
            <!-- 右侧参数输入区域 -->
            <div style="flex: 1; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; flex-direction: column;">
                <h3 style="margin-bottom: 15px; font-size: 16px; color: #333;">产线参数</h3>
                <div style="margin-bottom: 20px;">
                    <h4 style="margin-bottom: 15px; font-size: 14px; color: #333;">产线空间</h4>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <label style="min-width: 80px;">长度(mm):</label>
                            <input type="number" id="lineLength" value="${data.processLayout.lineLength || ''}" 
                                   onchange="updateProcessLayout('lineLength', this.value)"
                                   style="flex: 1; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <label style="min-width: 80px;">宽度(mm):</label>
                            <input type="number" id="lineWidth" value="${data.processLayout.lineWidth || ''}" 
                                   onchange="updateProcessLayout('lineWidth', this.value)"
                                   style="flex: 1; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <label style="min-width: 80px;">高度(mm):</label>
                            <input type="number" id="lineHeight" value="${data.processLayout.lineHeight || ''}" 
                                   onchange="updateProcessLayout('lineHeight', this.value)"
                                   style="flex: 1; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <label style="min-width: 80px;">产线节拍(s):</label>
                            <input type="number" id="lineTakt" value="${data.processLayout.lineTakt || ''}" 
                                   onchange="updateProcessLayout('lineTakt', this.value)"
                                   style="flex: 1; padding: 6px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 站位信息区域 -->
        <div id="stationsContainer">
            ${(data.stations || []).map((station, index) => `                <div class="station-block" data-index="${index}" style="margin-top: 20px; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <!-- 站位信息头部 -->                    <div style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #f0f0f0; padding-bottom: 15px; margin-bottom: 20px;">
                        <h3 style="font-size: 18px; color: #333; margin: 0;">
                            站位 ${station.number || ''} - ${station.name || ''}
                        </h3>
                        <button onclick="deleteStation(${index})" 
                                style="background: #ff4d4f; color: white; border: none; border-radius: 4px; padding: 6px 12px; cursor: pointer;">
                            删除站位
                        </button>
                    </div>

                    <!-- 站位基本信息 -->
                    <div style="display: flex; gap: 15px; align-items: center; margin-bottom: 20px;">
                        <div style="display: flex; align-items: center;">
                            <label style="margin-right: 8px; min-width: 60px; color: #333;">站位：</label>
                            <input type="text" 
                                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 120px;"
                                   placeholder="请输入站位"
                                   value="${station.number || ''}"
                                   onchange="updateStationInfo(${index}, 'number', this.value)">
                        </div>
                        <div style="display: flex; align-items: center;">
                            <label style="margin-right: 8px; min-width: 80px; color: #333;">站位名称：</label>
                            <input type="text"
                                   style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px;"
                                   placeholder="请输入站位名称"
                                   value="${station.name || ''}"
                                   onchange="updateStationInfo(${index}, 'name', this.value)">
                        </div>
                    </div>

                    <!-- 工艺描述区域 -->
                    <div class="content-section" style="margin-bottom: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4 style="font-size: 16px; color: #333; margin: 0;">工艺描述</h4>
                            <button onclick="toggleEdit(${index}, 'processDescription')" 
                                    style="background: #1890ff; color: white; border: none; border-radius: 4px; padding: 4px 12px; cursor: pointer; font-size: 14px;">
                                编辑
                            </button>
                        </div>
                        <textarea 
                            id="processDescription_${index}"
                            style="width: 100%; min-height: 120px; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 14px; line-height: 1.5; transition: all 0.3s ease;"
                            placeholder="请输入工艺描述..."
                            onchange="updateStationInfo(${index}, 'processDescription', this.value)"
                            disabled>${station.processDescription || ''}</textarea>
                    </div>

                    <!-- 具体要求区域 -->
                    <div class="content-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h4 style="font-size: 16px; color: #333; margin: 0;">具体要求</h4>
                            <button onclick="toggleEdit(${index}, 'specificRequirements')" 
                                    style="background: #1890ff; color: white; border: none; border-radius: 4px; padding: 4px 12px; cursor: pointer; font-size: 14px;">
                                编辑
                            </button>
                        </div>
                        <textarea 
                            id="specificRequirements_${index}"
                            style="width: 100%; min-height: 120px; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 14px; line-height: 1.5; transition: all 0.3s ease;"
                            placeholder="请输入具体要求..."
                            onchange="updateStationInfo(${index}, 'specificRequirements', this.value)"
                            disabled>${station.specificRequirements || ''}</textarea>
                    </div>
                </div>
            `).join('')}
        </div>

        <!-- 添加站位按钮 -->
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="addNewStation()" 
                    style="background: #52c41a; color: white; border: none; border-radius: 4px; padding: 8px 24px; cursor: pointer; font-size: 14px;">
                添加站位
            </button>
        </div>

        <!-- 图片预览模态框 -->
        <div id="imageModal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 1000; cursor: pointer;" onclick="closeImageModal()">
            <span style="position: absolute; right: 20px; top: 20px; color: white; font-size: 30px; cursor: pointer;" onclick="event.stopPropagation(); closeImageModal()">&times;</span>
            <img id="modalImage" style="max-width: 90%; max-height: 90%; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
        </div>
    `;
                    break;                case 'equipment':
                    content = `
        <h2>3.设备要求（AEP）</h2>
        
        <!-- 设备站位列表 -->
        <div id="equipmentContainer">
            ${(data.equipmentStations || []).map((station, index) => `
                <div class="equipment-station" data-index="${index}" style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin-bottom: 20px;">
                    <!-- 第一排：站位信息 -->
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <div style="display: flex; gap: 15px; flex: 1;">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <label style="font-weight: bold;">站位：</label>
                                <input type="text" value="${station.number || ''}" 
                                       onchange="updateEquipmentField(${index}, 'number', this.value)"
                                       style="width: 80px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <label style="font-weight: bold;">站位名称：</label>
                                <input type="text" value="${station.name || ''}"
                                       onchange="updateEquipmentField(${index}, 'name', this.value)"
                                       style="width: 200px; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                        </div>
                        <button onclick="deleteEquipmentStation(${index})" 
                                style="padding: 4px 12px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            删除站位
                        </button>
                    </div>

                    <!-- 第二排：设备布局图和基本参数 -->
                    <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                        <!-- 左侧：设备布局图 -->
                        <div style="flex: 2; background: #fff; padding: 15px; border: 1px solid #f0f0f0; border-radius: 4px;">
                            <h3 style="margin-bottom: 15px; font-size: 16px;">设备布局图</h3>
                            <div class="image-preview-area" style="text-align: center; min-height: 200px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                ${station.layoutImage ? `
                                    <img src="${station.layoutImage}" style="max-width: 100%; max-height: 300px; cursor: pointer;" 
                                         onclick="showImageModal(this.src)">
                                ` : `
                                    <div style="color: #666;">点击下方按钮上传图片</div>
                                `}
                            </div>
                            <div style="margin-top: 10px; display: flex; justify-content: center; gap: 10px;">
                                <input type="file" id="layoutImage_${index}" accept="image/*" style="display: none" 
                                       onchange="handleEquipmentImageUpload(event, ${index})">
                                <button onclick="document.getElementById('layoutImage_${index}').click()"
                                        style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    上传图片
                                </button>
                            </div>
                        </div>

                        <!-- 右侧：基本参数 -->
                        <div style="flex: 1; padding: 15px; border: 1px solid #f0f0f0; border-radius: 4px;">
                            <h3 style="margin-bottom: 15px; font-size: 16px;">基本参数</h3>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <label style="width: 120px;">设备长度（mm）：</label>
                                    <input type="text" value="${station.length || ''}"
                                           onchange="updateEquipmentField(${index}, 'length', this.value)"
                                           style="flex: 1; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <label style="width: 120px;">设备宽度（mm）：</label>
                                    <input type="text" value="${station.width || ''}"
                                           onchange="updateEquipmentField(${index}, 'width', this.value)"
                                           style="flex: 1; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <label style="width: 120px;">设备高度（mm）：</label>
                                    <input type="text" value="${station.height || ''}"
                                           onchange="updateEquipmentField(${index}, 'height', this.value)"
                                           style="flex: 1; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <label style="width: 120px;">节拍（s）：</label>
                                    <input type="text" value="${station.takt || ''}"
                                           onchange="updateEquipmentField(${index}, 'takt', this.value)"
                                           style="flex: 1; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                                <div style="display: flex; align-items: center; gap: 10px;">
                                    <label style="width: 120px;">换型时间（min）：</label>
                                    <input type="text" value="${station.changeTime || ''}"
                                           onchange="updateEquipmentField(${index}, 'changeTime', this.value)"
                                           style="flex: 1; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                </div>
                            </div>
                        </div>
                    </div>                    <!-- 第三排：机械要求和电气要求（分开显示） -->
                    <div style="margin-bottom: 20px;">
                        <!-- 机械要求 -->
                        <div style="margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <h3 style="font-size: 16px; margin: 0;">机械要求</h3>
                                <button onclick="toggleEquipmentEdit(${index}, 'mechanical')"
                                        style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
                                        id="mechanical_btn_${index}">
                                    编辑
                                </button>
                            </div>
                            <textarea id="mechanical_${index}"
                                    disabled
                                    onchange="updateEquipmentField(${index}, 'mechanicalRequirement', this.value)"
                                    style="width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;">${station.mechanicalRequirement || ''}</textarea>
                        </div>

                        <!-- 电气要求 -->
                        <div style="margin-bottom: 20px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <h3 style="font-size: 16px; margin: 0;">电气要求</h3>
                                <button onclick="toggleEquipmentEdit(${index}, 'electrical')"
                                        style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
                                        id="electrical_btn_${index}">
                                    编辑
                                </button>
                            </div>
                            <textarea id="electrical_${index}"
                                    disabled
                                    onchange="updateEquipmentField(${index}, 'electricalRequirement', this.value)"
                                    style="width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;">${station.electricalRequirement || ''}</textarea>
                        </div>
                    </div>

                    <!-- 夹具分解机构详细要求部分 -->
                    <div class="fixture-detail" id="fixtureDetail_${index}" style="display: ${station.showFixtureDetail ? 'block' : 'none'};">
                        <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 20px;">
                            <h3 style="font-size: 16px; margin-bottom: 15px;">夹具分解机构详细要求</h3>
                            
                            <!-- 夹具分解机构图 -->
                            <div style="margin-bottom: 20px;">
                                <h4 style="font-size: 14px; margin-bottom: 10px;">夹具分解机构图</h4>
                                <div class="fixture-images" style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 10px;">
                                    ${(station.fixtureImages || []).map((img, imgIndex) => `
                                        <div style="position: relative; width: 150px; height: 150px;">
                                            <img src="${img}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">
                                            <button onclick="deleteFixtureImage(${index}, ${imgIndex})"
                                                    style="position: absolute; top: 5px; right: 5px; background: rgba(255,77,79,0.8); color: white; border: none; border-radius: 50%; width: 24px; height: 24px; cursor: pointer;">
                                                ×
                                            </button>
                                        </div>
                                    `).join('')}
                                </div>
                                <div style="margin-bottom: 20px;">
                                    <input type="file" accept="image/*" multiple
                                           id="fixtureImages_${index}"
                                           style="display: none;"
                                           onchange="handleFixtureImagesUpload(event, ${index})">
                                    <button onclick="document.getElementById('fixtureImages_${index}').click()"
                                            style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                        上传图片
                                    </button>
                                </div>
                            </div>                            <!-- 关键点要求 -->
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <h4 style="font-size: 14px; margin: 0;">关键点要求</h4>
                                    <button onclick="toggleEquipmentEdit(${index}, 'keyPoints')"
                                            style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
                                            id="keyPoints_btn_${index}">
                                        编辑
                                    </button>
                                </div>
                                <textarea id="keyPoints_${index}"
                                        disabled
                                        style="width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"
                                        onchange="updateEquipmentField(${index}, 'keyPoints', this.value)">${station.keyPoints || ''}</textarea>
                            </div>

                            <!-- 具体要求和措施 -->
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                    <h4 style="font-size: 14px; margin: 0;">具体要求和措施</h4>
                                    <button onclick="toggleEquipmentEdit(${index}, 'measures')"
                                            style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;"
                                            id="measures_btn_${index}">
                                        编辑
                                    </button>
                                </div>
                                <textarea id="measures_${index}"
                                        disabled
                                        style="width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"
                                        onchange="updateEquipmentField(${index}, 'measures', this.value)">${station.measures || ''}</textarea>
                            </div>
                        </div>
                    </div>                    <!-- 添加/删除夹具分解机构详细要求按钮 -->
                    <div style="text-align: right;">
                        ${!station.showFixtureDetail ? 
                            `<button onclick="toggleFixtureDetail(${index})" 
                                    style="padding: 4px 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                                添加夹具分解机构详细要求
                            </button>` :
                            `<button onclick="toggleFixtureDetail(${index})" 
                                    style="padding: 4px 12px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                删除夹具分解机构详细要求
                            </button>`
                        }
                    </div>
                </div>
            `).join('')}
        </div>

        <!-- 添加设备按钮 -->
        <div style="text-align: center; margin-top: 20px;">
            <button onclick="addEquipmentStation()" 
                    style="padding: 8px 24px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                添加设备
            </button>
        </div>
    `;
                    break;
                case 'fixtures':
                    content = `
                        <h2>4.设备夹具清单(AEP)</h2>
                        <table>
                            <tr>
                                <th>夹具编号</th>
                                <th>夹具名称</th>
                                <th>数量</th>
                                <th>技术要求</th>
                                <th>操作</th>
                            </tr>
                            ${(data.fixtures || []).map((item, index) => `
                                <tr>
                                    <td>${item.id || ''}</td>
                                    <td>${item.name || ''}</td>
                                    <td>${item.quantity || ''}</td>
                                    <td>${item.techRequirement || ''}</td>
                                    <td>
                                        <button class="edit-btn" onclick="editRow('fixtures', ${index})">编辑</button>
                                        <button class="edit-btn" onclick="deleteRow('fixtures', ${index})" style="background: #ff4d4f">删除</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </table>
                        <button class="add-row-btn" onclick="addRow('fixtures')">添加新行</button>
                    `;
                    break;
                case 'terms':
                    content = `
                        <div style="padding: 20px;">
                            <h2>5.通用条款</h2>
                            <p style="color: #666; margin-bottom: 20px;">*设备供应商交付的设备、工装、夹具、程序改造等均须符合如下标准与文件</p>
                            
                            <!-- 标准与文件表格 -->
                            <div class="table-responsive" style="overflow-x: auto;">
                                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px;">
                                    <thead>
                                        <tr>
                                            <th style="padding: 12px; border: 1px solid #ddd;">适用阶段</th>
                                            <th style="padding: 12px; border: 1px solid #ddd;">文件名称</th>
                                            <th style="padding: 12px; border: 1px solid #ddd;">类别</th>
                                            <th style="padding: 12px; border: 1px solid #ddd;">编号</th>
                                            <th style="padding: 12px; border: 1px solid #ddd;">要求</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">报价</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊生产线方案》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">项目方案</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6356772</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">共线夹具与手动线：技术交流后3天内提供<br>自动线：技术交流后5天内提供</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">报价、设计</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊线设备夹具清单》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">Main BOM</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6356423</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">报价、设计</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊线工装制作计划表》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">时间计划</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6361925</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;" rowspan="8">报价、设计、制造、验收</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备机械通用技术规范》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">机械规范</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6283425</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">产线开发全过程都须符合<br>如存在冲突或问题，需得到Autoliv设备开发部门认可</td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备电气

                                            <td style="padding: 12px; border: 1px solid #ddd;">电气规范</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6283738</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备安全通用技术规范》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">安全规范</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6744785</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备图纸技术规范》</td>
                                            <td style="padding: 12px; border:   1px solid #ddd;">图纸规范</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6918555</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备标准零部件清单》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">标准件要求</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6283677</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备设计评审与验收指导书》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">评审与验收指导书</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6309004</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《中国区气囊设备设计评审与验收报告》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">评审与验收报告</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">E6309135</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                        <tr>
                                            <td style="padding: 12px; border: 1px solid #ddd;">《机械安全风险评估Machine Safety Risk Assessment》</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">机械安全评估报告</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;">TCH-ASS001</td>
                                            <td style="padding: 12px; border: 1px solid #ddd;"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- 碳排放计算公式 -->
                            <div style="margin-bottom: 30px;">
                                <p style="color: #666;">*设备碳排放量需在设计初期评估计算，公式：</p>
                                <div style="background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 4px;">
                                    年碳排放量 = 设备总功率 * 碳排放系数 * 工作时间/年(21.5H*250day) * 设备利用率(60%)
                                </div>
                                <p style="color: #666;">
                                    碳排放系数： 上海 0.42 tCO2/MWh； 台湾 0.509 tCO2/MWh； 其他地区 0.581 tCO2/MWh
                                </p>
                            </div>

                            <!-- 5.1 方案确认阶段 -->
                            <div class="section" style="margin-bottom: 30px;">
                                <h3>5.1 方案确认阶段</h3>
                                <ol style="padding-left: 20px;">
                                    <li>设备供应商严格按E6356772、E6356423、E6361925格式及交付时间要求提供对应的方案、时间计划、Main BOM</li>
                                    <li>不能如期交付项目方案、时间计划、Main BOM且未获得Autoliv同意的设备供应商，可视为放弃参与此项目</li>
                                    <li>设备供应商提供的产线交付时间充分考虑商务沟通周期，满足"制造规范封面"中"要求完成日期"</li>
                                </ol>
                            </div>

                            <!-- 5.2 报价阶段 -->
                            <div class="section" style="margin-bottom: 30px;">
                                <h3>5.2 报价阶段</h3>
                                <ol style="padding-left: 20px;">
                                    <li>设备供应商收到报价需求后按如下右表要求提供报价，特殊情况需提前与Autoliv采购沟通</li>
                                    <li>涉及新线或多项目共线同步开发时，报价单要求按如下左表要求分行提供，报价单按如下右表要求的时间提供</li>
                                </ol>
                                
                                <!-- 报价表格 -->
                                <div class="tables-container" style="display: flex; gap: 20px; margin-top: 15px;">
                                    <table style="flex: 1;">
                                        <tr>
                                            <th>设备主体</th>
                                            <th>STXX XX设备主体费用</th>
                                        </tr>
                                        <tr>
                                            <td>A项目</td>
                                            <td>STXX设备 A项目专用夹具费用</td>
                                        </tr>
                                        <tr>
                                            <td>B项目</td>
                                            <td>STXX设备 B项目专用夹具费用</td>
                                        </tr>
                                        <tr>
                                            <td>C项目</td>
                                            <td>STXX设备 C项目专用夹具费用</td>
                                        </tr>
                                    </table>
                                    
                                    <table style="flex: 1;">
                                        <
                                            <th>类别</th>
                                            <th>提供报价时间</th>
                                        </tr>
                                        <tr>
                                            <td>共线夹具</td>
                                            <td>提出报价需求后2日内</td>
                                        </tr>
                                        <tr>
                                            <td>手动线</td>
                                            <td>提出报价需求后3日内</td>
                                        </tr>
                                        <tr>
                                            <td>自动线</td>
                                            <td>提出报价需求后5日内</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- 5.3-5.7 各节内容 -->
                            <div class="section" style="margin-bottom: 30px;">
                                <h3>5.3 设计与制造阶段</h3>
                                <ol style="padding-left: 20px;">
                                    <li>供应定点后按《E6361925_中国区气囊线工装制作计划表》提供详细计划，要求符合Autoliv"产线制造规范"定义的预验收、交付、调试完成的时间</li>
                                    <li>设备夹具制造前按《E6309004_中国区气囊设备设计评审与验收指导书》与Autoliv设计评审，设计评审通过不代表可免除设备供应商保证设备功能完善的责任</li>
                                    <li>设计评审通过后，设备供应商按设计锁定版《E6356423_中国区气囊线设备夹具清单》，经Autoliv确认后启动制作</li>
                                    <li>设备供应商应至少每2周主动向Autoliv订单申请人提供1次项目进展说明，含标准件货期、加工件货期、风险、施工情况等信息</li>
                                    <li>设备夹具设计制造都应该符合E6283425、E6283738、E6283677、E6744785、E6918555等通用型技术规范，包括但不限于：
                                        <ol style="list-style-type: lower-latin;">
                                            <li>设备供应商需要在对应工装夹具上刻印标识</li>
                                            <li>除特殊要求外，设备工装夹具等都需要去除尖角、飞边、毛刺等处理，避免划伤</li>
                                            <li>除特殊要求外，设备上方都需要配置《E6283677_中国区气囊设备标准零部件清单》中要求的照明灯具</li>
                                            <li>设备整体结构设计应尽量紧凑但必须保留足够的空间方便生产、日常检查、维修、保养</li>
                                            <li>设备的所有运动零部件应动作平稳且有良好的润滑(必要时提供润滑操作说明)，无干涉、卡滞现象</li>
                                            <li>设备的气路、液路、真空回路连接简明、可靠，在最大工作压力120％的压力状态下无泄漏现象</li>
                                            <li>工装夹具含点检、归零样件、工具，必须方便生产过程中放置与拿取</li>
                                            <li>设备夹具快速换型和点检校验时方便拿取，固定在设备上的点检、归零样件应留出足够空间方便测量和校验</li>
                                            <li>需保持整线设备产品方向基准的一致性，针对分批交付的设备，后交付的设备产品方向基准必须严格遵守先交付设备的方向基准</li>
                                        </ol>
                                    </li>
                                </ol>
                            </div>

                            <div class="section" style="margin-bottom: 30px;">
                                <h3>5.4 机械安全评估要求</h3>
                                <p style="color: #666; margin-bottom: 10px;">*设备供应商必须在工装、夹具、设备开发过程中完全遵守《 E6744785_中国区气囊设备安全通用技术规范》的要求</p>
                                <p style="color: #666; margin-bottom: 10px;">*设备供应商应按《E6309004_中国区气囊设备设计评审与验收指导书》在设计、制造、验收等阶段提供资料并参与相应评审及验收活动</p>
                                <p style="color: #666; margin-bottom: 20px;">*机械安全评估活动中相关评估报告制作、安全审核、安全验收等环节，设备供应商须有满足AS332中对应等级L5资质人员参与或请由资质的第三方完成</p>
                                
                                <h4>1. 新设备或涉及到机械安全的设备改造</h4>
                                <ol style="padding-left: 20px;">
                                    <li>报价环节：Autoliv订单申请人将已经完成初步风险评估的《TCH-ASS001-Machine Safety Risk Assessment》连同产线要求规范SoW发给设备供应商</li>
                                    <li>设计环节：设备供应商应进行详细的风险评估并采取必要的措施进行风险降低，完善《TCH-ASS001-Machine Safety Risk Assessment》"Cover page" 和 "Risk-assessment"部分内容，如果风险降低措施涉及到安全控制系统 (SCS) ，须设计详细的安全控制系统电气原理图并完成"PL Report"部分内容，确认安全控制系统PL≥PLr</li>
                                    <li>设计评审和批准环节：在设备制造开始前，根据AS332-5.8中定义的相应等级资质人员针对安全防护措施进行设计评审，并获得Autoliv的批准。设备供应商应在设计评审前完成"安全控制系统电气原理图、安全回路模块图、《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"和"PL Report"内容）"</li>
                                    <li>预验收环节：
                                        <ol style="list-style-type: lower-latin;">
                                            <li>预验收前，设备供应商应根据设备实际情况更新"《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"和"PL Report"部分）、安全控制系统电气原理图、安全回路模块图"，并验证安全防护的功能</li>
                                            <li>预验收时，设备供应商向Autoliv相关人员提供"《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"和"PL Report"部分）、安全控制系统电气原理图、安全回路模块图".Autoliv相关人员会完成预验收检查表里安全相关项目的检查，并对《TCH-ASS001-Machine Safety Risk Assessment》"Cover page"、"Risk-assessment"和"PL Report"部分）及安全防护措施进行确认，复核安全控制系统PL，输出完整的《TCH-ASS001-Machine Safety Risk Assessment》（"Cover page"、"Risk-assessment"、"PL Report"和"Summary"部分）</li>
                                        </ol>
                                    </li>
                                    <li>终验收环节：在批准设备使用前，设备供应商应配合Autoliv相关人员对安全防护系统进行最终确认，确认预验收《TCH-ASS001-Machine Safety Risk Assessment》中LOP都已关闭，作为向供应商进一步付款前的依据</li>
                                </ol>
                                
                                <h4>2. 工装夹具类、软件改造类</h4>
                                <p>在设计评审和验收环节，设备供应商应配合Autoliv结合《中国区气囊设备设计评审与验收报告》中安全检查项对安全防护措施进行确认</p>
                            </div>

                            <div class="section" style="margin-bottom: 30px;">
                                <h3>5.5 验收</h3>
                                <ol style="padding-left: 20px;">
                                    <li>设备完成组装和调试后，设备供应商应按《E6309004_中国区气囊设备设计评审与验收指导书》对设备、夹具等进行自验收</li>
                                    <li>设备供应商应自验收完成后，应将《E6309135_中国区气囊设备设计评审与验收报告》(供应商自查)签字扫描版、自查LOP等发送给Autoliv订单申请人</li>
                                    <li>Autoliv订单申请人根据供应商自验收结果判定是否达到预验收条件，达到后组织预验收会议</li>
                                    <li>设备供应商根据Autoliv要求的到货时间安排发货，到货当天须提供盖章的送货单《MRO Delivery Note》，并与Autoliv订单申请人完成物品核对收货</li>
                                    <li>原则上设备验收地需要与合同约定的地点保持一致。如有不符，Autoliv有权要求供应商承担验收及设备开发过程中额外产生的费用(差旅、工时损失等)</li>
                                    <li>若因设备供应商未客观反馈产线状态(如未自验收或提供虚假报告等)而导致验收失败，Autoliv有权要求供应商承担再次验收额外产生的费用(差旅、工时损失等)</li>
                                </ol>
                            </div>

                            <div class="section" style="margin-bottom: 30px;">
                                <h3>5.6 交机资料、备件、工具交付要求</h3>
                                <ol style="padding-left: 20px;">
                                    <li>除特殊约定外，设备供应商为Auotliv开发的气囊设备、工装、夹具的所以文件资料（机械图、电气图、软件等各类文件）版权归Autoliv所有</li>
                                    <li>设备供应商按《E6309004_中国区气囊设备设计评审与验收指导书》_交机资料要求及《E6918555_中国区气囊设备图纸技术规范》提供相关设备、工装、夹具的交机资料、备件、工具等</li>
                                </ol>
                            </div>

                            <div class="section">
                                <h3>5.7 培训及售后服务</h3>
                                <ol style="padding-left: 20px;">
                                    <li>设备交付后，设备供应商应按《E6309004_中国区气囊设备设计评审与验收指导书》提供相关培训</li>
                                    <li>除特殊约定，设备自交付日起设备供应商提供24个月免费保修服务，终生维修服务</li>
                                    <li>保修期内，非Autoliv原因造成的设备零件损坏(如零件质量问题或设计不合理)由设备供应商免费提供并维修</li>
                                    <li>保修期内，卖方Autoliv现场服务人员10分钟内到达问题现场，不在Autoliv现场的服务人员10分钟内远程响应并在2小时内到达问题现场</li>
                                    <li>保修期过后，卖方实时响应且24小时内技术人员可到达问题现场</li>
                                </ol>
                            </div>
                        </div>
                    `;
                    break;
            }
            contentDiv.innerHTML = content;
            
            // 如果是产品信息页面，需要重新绑定事件监听器
            if (type === 'product') {
                attachCategoryButtonListeners();
                // 如果已经选择了模块，重新生成按钮
                const selectedModule = document.getElementById('productModuleSelect')?.value;
                if (selectedModule) {
                    handleProductModuleChange(selectedModule);
                }
            }

            // 为所有可编辑内容添加自动保存
            attachEditableListeners();
        }

        // 增强 handleEquipmentTypeChange 函数
        function handleEquipmentTypeChange(value) {
            localStorage.setItem('selectedEquipmentType', value);
            // 保存其他相关数据
            saveDataAfterChange();
        }

        // 保存条款内容
        function saveTerms() {
            const generalTextarea = document.querySelector('textarea[placeholder="请输入一般要求内容..."]');
            const safetyTextarea = document.querySelector('textarea[placeholder="请输入安全要求内容..."]');
            const qualityTextarea = document.querySelector('textarea[placeholder="请输入质量要求内容..."]');
            
            if (generalTextarea && safetyTextarea && qualityTextarea) {
                data.terms = {
                    general: generalTextarea.value,
                    safety: safetyTextarea.value,
                    quality: qualityTextarea.value
                };
                saveDataAfterChange();
            }
        }

        // 为文本区域添加自动保存
        function attachTextareaListeners() {
            document.querySelectorAll('textarea').forEach(textarea => {
                textarea.addEventListener('blur', saveTerms);
            });
        }

        // DOM加载完成后初始化所有功能



        document.addEventListener('DOMContentLoaded', () => {
            // 加载保存的数据
            loadData();
            
            // 显示初始内容（封面）
            showContent('cover');
            
            // 恢复之前的选择状态
            restoreSelections();
            
            // 为导航菜单项添加事件监听
            document.querySelectorAll('#nav-menu a').forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();  // 阻止默认的链接行为
                    const content = link.getAttribute('data-content');
                    showContent(content);
                    
                    // 如果切换到产品页面，恢复选择状态
                    if (content === 'product') {
                        setTimeout(restoreSelections, 0);
                    }
                });
            });

            // 为所有文本区域添加自动保存
            attachTextareaListeners();
        });

        function editRow(type, index) {
            currentEditType = type;
            currentEditIndex = index;
            const item = data[type][index];
            const form = document.getElementById('edit-form');
            const formFields = document.getElementById('edit-form-fields');
            const overlay = document.getElementById('overlay');
            
            // 根据不同类型生成不同的表单字段
            let fields = '';
            switch(type) {
                case 'fixtures':
                    fields = `
                        <input type="text" placeholder="夹具编号" value="${item.id}" data-field="id">
                        <input type="text" placeholder="夹具名称" value="${item.name}" data-field="name">
                        <input type="text" placeholder="数量" value="${item.quantity}" data-field="quantity">
                        <input type="text" placeholder="技术要求" value="${item.techRequirement}" data-field="techRequirement">
                    `;
                    break;                case 'product':
                    fields = `
                        <div class="form-group">
                            <label>零件编号</label>
                            <input type="text" placeholder="零件编号" value="${item?.id || ''}" data-field="id">
                        </div>
                        <div class="form-group">
                            <label>项目名称</label>
                            <input type="text" placeholder="项目名称" value="${item?.projectName || ''}" data-field="projectName">
                        </div>
                        <div class="form-group">
                            <label>零件名称</label>
                            <input type="text" placeholder="零件名称" value="${item?.name || ''}" data-field="name">
                        </div>
                        <div class="form-group">
                            <label>单件用量</label>
                            <input type="text" placeholder="单件用量" value="${item?.unitUsage || ''}" data-field="unitUsage">
                        </div>
                        <div class="form-group">
                            <label>包装尺寸</label>
                            <input type="text" placeholder="长*宽*高mm" value="${item?.packageSize || ''}" data-field="packageSize">
                        </div>
                        <div class="form-group">
                            <label>单包数量</label>
                            <input type="text" placeholder="单包数量" value="${item?.unitPackageQty || ''}" data-field="unitPackageQty">
                        </div>
                        <div class="form-group">
                            <label>零件状态</label>
                            <select class="part-status-select" data-field="partStatus" onchange="toggleSharedProjectInput(this.value)">
                                <option value="新制" ${item?.partStatus === '新制' ? 'selected' : ''}>新制</option>
                                <option value="共用" ${item?.partStatus === '共用' ? 'selected' : ''}>共用</option>
                            </select>
                        </div>
                        <div class="form-group shared-project-group" style="display: ${item?.partStatus === '共用' ? 'block' : 'none'}">
                            <label>共享项目名称</label>
                            <input type="text" placeholder="输入共享项目名称" value="${item?.sharedProjectName || ''}" data-field="sharedProjectName">
                        </div>
                        <div class="form-group">
                            <label>备注</label>
                            <input type="text" placeholder="备注信息" value="${item?.remarks || ''}" data-field="remarks">
                        </div>
                    `;
                    break;
                case 'process':
                    fields = `
                        <input type="text" placeholder="工序" value="${item.step}" data-field="step">
                        <input type="text" placeholder="工艺要求" value="${item.requirement}" data-field="requirement">
                        <input type="text" placeholder="节拍" value="${item.takt}" data-field="takt">
                        <input type="text" placeholder="类型" value="${item.type}" data-field="type">
                        <input type="text" placeholder="产品特性要求" value="${item.productFeature}" data-field="productFeature">
                        <input type="text" placeholder="防错要求" value="${item.errorProof}" data-field="errorProof">
                    `;
                    break;
                case 'equipment':
                    fields = `
                        <input type="text" placeholder="设备编号" value="${item.id}" data-field="id">
                        <input type="text" placeholder="设备名称" value="${item.name}" data-field="name">
                        <input type="text" placeholder="技术要求" value="${item.techRequirement}" data-field="techRequirement">
                    `;
                    break;
            }
            
            formFields.innerHTML = fields;
            form.style.display = 'block';
            overlay.style.display = 'block';
        }        function toggleSharedProjectInput(value) {
            const sharedProjectGroup = document.querySelector('.shared-project-group');
            if (sharedProjectGroup) {
                if (value === '共用') {
                    sharedProjectGroup.style.display = 'block';
                    setTimeout(() => {
                        const input = sharedProjectGroup.querySelector('input');
                        if (input) {
                            input.focus();
                        }
                    }, 100);
                } else {
                    sharedProjectGroup.style.display = 'none';
                    const input = sharedProjectGroup.querySelector('input');
                    if (input) {
                        input.value = '';
                    }
                }
            }
        }        function addRow(type) {
            if (type === 'product') {
                // 生成新的空行数据
                const rowData = {
                    id: `P${String(data.product.length + 1).padStart(3, '0')}`,
                    projectName: '',
                    name: '',
                    unitUsage: '',
                    packageSize: '',
                    unitPackageQty: '',
                    partStatus: '新制',
                    sharedProjectName: '',
                    remarks: ''
                };
                
                // 添加到数据数组
                data.product.push(rowData);
                const newIndex = data.product.length - 1;
                
                // 获取表格元素
                const table = document.querySelector('#content table');
                const rowHtml = `
            <tr class="new-row" data-index="${newIndex}">
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'projectName', this.textContent)" 
                    onfocus="if(this.textContent === '') this.textContent = ''" 
                    placeholder="点击输入项目名称"></td>
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'id', this.textContent)">${rowData.id}</td>
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'name', this.textContent)" 
                    onfocus="if(this.textContent === '') this.textContent = ''" 
                    placeholder="点击输入零件名称"></td>
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'unitUsage', this.textContent)" 
                    onfocus="if(this.textContent === '') this.textContent = ''" 
                    placeholder="点击输入用量"></td>
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'packageSize', this.textContent)" 
                    onfocus="if(this.textContent === '') this.textContent = ''" 
                    placeholder="点击输入包装尺寸"></td>
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'unitPackageQty', this.textContent)" 
                    onfocus="if(this.textContent === '') this.textContent = ''" 
                    placeholder="点击输入包装数量"></td>
                <td>
                    <select class="part-status-select" onchange="updatePartStatus(${newIndex}, this.value)" style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="新制" selected>新制</option>
                        <option value="共用">共用</option>
                    </select>
                </td>
                <td contenteditable="true" 
                    class="shared-project-name" 
                    onblur="updateSharedProjectName(${newIndex}, this.textContent); this.parentElement.classList.remove('new-row')"
                    style="color: #999;"
                    data-placeholder="不适用">不适用</td>
                <td contenteditable="true" 
                    onblur="updateProductField(${newIndex}, 'remarks', this.textContent)" 
                    onfocus="if(this.textContent === '') this.textContent = ''" 
                    placeholder="点击输入备注"></td>
                <td>
                    <button class="edit-btn" onclick="toggleRowEdit(${newIndex}, 'product')" style="background: #52c41a">保存</button>
                    <button class="edit-btn" onclick="deleteRow('product', ${newIndex})" style="background: #ff4d4f">删除</button>
                </td>
            </tr>
        `;
        
                // 添加新行到表格
                table.insertAdjacentHTML('beforeend', rowHtml);
                
                // 将新行添加到编辑状态集合中
                editingRows.add(newIndex);
                
                // 滚动到新行位置
                const lastRow = table.rows[table.rows.length - 1];
                lastRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
                
                // 将焦点设置到第一个单元格
                const firstCell = lastRow.cells[0];
                if (firstCell) {
                    firstCell.focus();
                }
                
                // 保存数据
                saveDataAfterChange();
                return;
            }
            
            // 对于其他类型的表格，保持原有的弹窗添加方式
            currentEditType = type;
            currentEditIndex = -1;
            const form = document.getElementById('edit-form');
            const formFields = document.getElementById('edit-form-fields');
            const overlay = document.getElementById('overlay');
            
            let fields = '';
            switch(type) {
                case 'fixtures':
                    fields = `
                        <input type="text" placeholder="夹具编号" value="" data-field="id">
                        <input type="text" placeholder="夹具名称" value="" data-field="name">
                        <input type="text" placeholder="数量" value="" data-field="quantity">
                        <input type="text" placeholder="技术要求" value="" data-field="techRequirement">
                    `;
                    break;
                case 'process':
                    fields = `
                        <input type="text" placeholder="工序" value="" data-field="step">
                        <input type="text" placeholder="工艺要求" value="" data-field="requirement">
                        <input type="text" placeholder="节拍" value="" data-field="takt">
                        <input type="text" placeholder="类型" value="" data-field="type">
                        <input type="text" placeholder="产品特性要求" value="" data-field="productFeature">
                        <input type="text" placeholder="防错要求" value="" data-field="errorProof">
                    `;
                    break;
                case 'equipment':
                    fields = `
                        <input type="text" placeholder="设备编号" value="" data-field="id">
                        <input type="text" placeholder="设备名称" value="" data-field="name">
                        <input type="text" placeholder="技术要求" value="" data-field="techRequirement">
                    `;
                    break;
            }
            
            formFields.innerHTML = fields;
            form.style.display = 'block';
            overlay.style.display = 'block';
        }

        function deleteRow(type, index) {
            if (confirm('确定要删除这条记录吗？')) {
                if (type === 'product') {
                    // 保存当前的模块和类别选择
                    const currentModule = document.getElementById('productModuleSelect')?.value;
                    const currentCategory = selectedCategory;

                    // 删除数据
                    data[type].splice(index, 1);

                    // 重新生成行号
                    data.product = data.product.map((item, idx) => ({
                        ...item,
                        id: `P${String(idx + 1).padStart(3, '0')}`
                    }));

                    // 重新渲染表格，但不刷新类别选择
                    const tableBody = document.querySelector('#content table');
                    if (tableBody) {
                        const tableHeader = `<tr>
                            <th>新项目名称</th>
                            <th>零件编号</th>
                            <th>零件名称</th>
                            <th>单位用量</th>
                            <th>包装尺寸（长*宽*高mm）</th>
                            <th>单位包装数量</th>
                            <th>零件状态</th>
                            <th>共享项目名称</th>
                            <th>备注</th>
                            <th>操作</th>
                        </tr>`;
                        
                        const tableContent = data.product.map((item, idx) => `
                            <tr data-index="${idx}">
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'projectName', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入项目名称">${item.projectName || ''}</td>
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'id', this.textContent)">${item.id}</td>
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'name', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入零件名称">${item.name || ''}</td>
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'unitUsage', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入用量">${item.unitUsage || ''}</td>
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'packageSize', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入包装尺寸">${item.packageSize || ''}</td>
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'unitPackageQty', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入包装数量">${item.unitPackageQty || ''}</td>
                                <td>
                                    <select class="part-status-select" onchange="updatePartStatus(${idx}, this.value)" style="width: 100%; padding: 4px; border: 1px solid #ddd; border-radius: 4px;">
                                        <option value="新制" ${item.partStatus === '新制' ? 'selected' : ''}>新制</option>
                                        <option value="共用" ${item.partStatus === '共用' ? 'selected' : ''}>共用</option>
                                    </select>
                                </td>
                                <td contenteditable="false" 
                                    class="shared-project-name" 
                                    onblur="updateSharedProjectName(${idx}, this.textContent)"
                                    style="${item.partStatus === '共用' ? '' : 'color: #999;'}"
                                    ${item.partStatus === '共用' ? '' : 'data-placeholder="不适用"'}
                                    >
                                    ${item.partStatus === '共用' ? (item.sharedProjectName || '') : '不适用'}</td>
                                <td contenteditable="false" 
                                    onblur="updateProductField(${idx}, 'remarks', this.textContent)" 
                                    onfocus="if(this.textContent === '') this.textContent = ''" 
                                    placeholder="点击输入备注">${item.remarks || ''}</td>
                                <td>
                                    <button class="edit-btn" onclick="toggleRowEdit(${idx}, 'product')" style="background: #1890ff;">编辑</button>
                                    <button class="edit-btn" onclick="deleteRow('product', ${idx})" style="background: #ff4d4f">删除</button>
                                </td>
                            </tr>
                        `).join('');

                        // 更新表格内容
                        tableBody.innerHTML = tableHeader + tableContent;
                    }

                    // 恢复模块和类别的选择状态
                    if (currentModule) {
                        const moduleSelect = document.getElementById('productModuleSelect');
                        if (moduleSelect) {
                            moduleSelect.value = currentModule;
                        }
                    }
                    if (currentCategory) {
                        const categoryButton = document.querySelector(`.category-button[data-category="${currentCategory}"]`);
                        if (categoryButton) {
                            categoryButton.classList.add('selected');
                        }
                        selectedCategory = currentCategory;
                    }

                    // 保存更改
                    saveDataAfterChange();
                } else {
                    // 处理其他类型表格的删除
                    data[type].splice(index, 1);
                    showContent(type);
                    saveDataAfterChange();
                }
            }
        }

        function saveEdit() {
            const form = document.getElementById('edit-form');
            const overlay = document.getElementById('overlay');
            const fields = form.querySelectorAll('input[data-field], select[data-field]');
            const newData = {};
            
            fields.forEach(field => {
                newData[field.dataset.field] = field.value;
            });
            
            if (currentEditIndex === -1) {
                // 添加新行
                data[currentEditType].push(newData);
            } else {
                // 更新现有行
                data[currentEditType][currentEditIndex] = newData;
            }
            
            form.style.display = 'none';
            overlay.style.display = 'none';
            showContent(currentEditType);
            saveDataAfterChange(); // 保存更改到 localStorage
        }

        function cancelEdit() {
            const form = document.getElementById('edit-form');
            const overlay = document.getElementById('overlay');
            form.style.display = 'none';
            overlay.style.display = 'none';
        }

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value;
            if (message.trim() === '') return;

            const chatHistory = document.getElementById('chat-history');
            chatHistory.innerHTML += `
                <div style="margin-bottom: 10px;">
                    <strong>用户:</strong> ${message}
                </div>
                <div style="margin-bottom: 10px; color: #1890ff;">
                    <strong>AI:</strong> 正在处理您的问题...
                </div>
            `;
            
            input.value = '';
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }        // 生成LineSpec信息
        function generateLineSpec() {
            const selectedModule = document.getElementById('productModuleSelect')?.value;
            if (!selectedModule || !selectedCategory) {
                alert('请先选择模块类别和模块明细！');
                return;
            }

            // 获取所有零件名称
            const partNames = data.product.map(item => item.name).join('、');
            
            // 生成要复制的文本
            const textToCopy = `帮我写一份${selectedModule}模块的Linespec,涉及的零件有：${partNames}，根据零件匹配该项目所属的工艺类型，根据不同工艺类型对应的工艺信息和设备信息输出推荐的工艺部分和设备部分，按照工站号顺序进行描述`;
            
            // 生成信息文本
            let content = `
<h4>LineSpec生成提示语</h4>
<pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; margin: 10px 0; white-space: pre-wrap;">${textToCopy}</pre>
<div style="margin-top: 15px; color: #666;">
    <p>提示：请将上述文本复制到AI对话框中，以获取详细的工艺和设备推荐信息。</p>
</div>
<button class="copy-btn" onclick="copyGeneratedText('${textToCopy.replace(/'/g, "\\'")}')">复制内容</button>`;

            // 显示弹窗
            const popup = document.getElementById('generate-popup');
            const overlay = document.getElementById('overlay');
            const generateContent = document.getElementById('generate-content');
            
            generateContent.innerHTML = content;
            popup.style.display = 'block';
            overlay.style.display = 'block';
        }

        // 关闭生成信息弹窗
        function closeGeneratePopup() {
            const popup = document.getElementById('generate-popup');
            const overlay = document.getElementById('overlay');
            popup.style.display = 'none';
            overlay.style.display = 'none';
        }

        // 复制生成的文本到剪贴板
        async function copyGeneratedText(text) {
            try {
                await navigator.clipboard.writeText(text);
                const copyBtn = document.querySelector('.copy-btn');
                copyBtn.textContent = '复制成功';
                copyBtn.classList.add('copied');
                
                // 2秒后恢复按钮状态
                setTimeout(() => {
                    copyBtn.textContent = '复制内容';
                    copyBtn.classList.remove('copied');
                }, 2000);
            } catch (err) {
                alert('复制失败，请手动复制文本');
                console.error('复制失败:', err);
            }
        }

        // 显示保存成功提示
        function showSaveSuccess(button) {
            const originalText = button.textContent;
            const originalColor = button.style.background;
            
            button.textContent = '已保存';
            button.style.background = '#52c41a';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = originalColor;
            }, 1500);
        }

        // 切换行编辑状态的函数
        function toggleRowEdit(index, type) {
            const row = document.querySelector(`table tr[data-index="${index}"]`);
            if (!row) return;
            
            const isEditing = editingRows.has(index);
            const editBtn = row.querySelector('.edit-btn');
            const cells = row.querySelectorAll('td[contenteditable]');
            
            if (isEditing) {
                // 保存操作
                editingRows.delete(index);
                editBtn.textContent = '编辑';
                editBtn.style.background = '#1890ff';
                cells.forEach(cell => {
                    cell.setAttribute('contenteditable', 'false');
                    cell.style.backgroundColor = '';
                });
                saveDataAfterChange();
            } else {
                // 进入编辑模式
                editingRows.add(index);
                editBtn.textContent = '保存';
                editBtn.style.background = '#52c41a';
                cells.forEach(cell => {
                    cell.setAttribute('contenteditable', 'true');
                    cell.style.backgroundColor = '#f6ffed';
                });
            }
        }        // 切换编辑状态
        function toggleEdit(index, field) {
            const textarea = document.getElementById(`${field}_${index}`);
            const button = textarea.previousElementSibling.querySelector('button');

            if (textarea.disabled) {
                // 进入编辑模式
                textarea.disabled = false;
                textarea.style.backgroundColor = '#f6ffed';
                button.textContent = '保存';
                button.style.background = '#52c41a';
                textarea.focus();
            } else {
                // 保存更改并显示提示
                textarea.disabled = true;
                textarea.style.backgroundColor = '';
                button.textContent = '编辑';
                button.style.background = '#1890ff';
                
                // 更新数据
                updateStationInfo(index, field, textarea.value);
                showSaveSuccess(button); // 显示保存成功提示
            }
        }

        // 更新设备信息
        function updateEquipmentStationInfo(index, field, value) {
            if (!data.equipmentStations) {
                data.equipmentStations = [];
            }
            if (!data.equipmentStations[index]) {
                data.equipmentStations[index] = {};
            }
            data.equipmentStations[index][field] = value;

            // 如果编辑的是number或name字段，更新设备标题显示
            if (field === 'number' || field === 'name') {
                const stationBlock = document.querySelector(`.station-block[data-index="${index}"]`);
                if (stationBlock) {
                    const title = stationBlock.querySelector('h3');
                    if (title) {
                        title.textContent = `站位 ${data.equipmentStations[index].number || ''} - ${data.equipmentStations[index].name || ''}`;
                    }
                }
            }

            saveDataAfterChange();
        }

        // 切换设备编辑状态
        function toggleEquipmentEdit(index, field) {
            const textarea = document.getElementById(`${field}_${index}`);
            const button = textarea.previousElementSibling.querySelector('button');

            if (textarea.disabled) {
                // 进入编辑模式
                textarea.disabled = false;
                textarea.style.backgroundColor = '#f6ffed';
                button.textContent = '保存';
                button.style.background = '#52c41a';
                textarea.focus();
            } else {
                // 保存更改
                textarea.disabled = true;
                textarea.style.backgroundColor = '';
                button.textContent = '编辑';
                button.style.background = '#1890ff';
                
                // 更新数据
                updateEquipmentStationInfo(index, field, textarea.value);
                showSaveSuccess(button);
            }
        }

        // 更新设备要求（机械/电气）
        function updateEquipmentRequirement(type, value) {
            if (!data.equipmentLayout) {
                data.equipmentLayout = {};
            }
            
            if (type === 'mechanical') {
                data.equipmentLayout.mechanicalRequirement = value;
            } else if (type === 'electrical') {
                data.equipmentLayout.electricalRequirement = value;
            }
            
            saveDataAfterChange();
        }

        // 切换设备要求编辑状态
        function toggleEquipmentRequirementEdit(type) {
            const textarea = document.getElementById(`${type}Requirement`);
            const button = textarea.previousElementSibling.querySelector('button');

            if (textarea.disabled) {
                // 进入编辑模式
                textarea.disabled = false;
                textarea.style.backgroundColor = '#f6ffed';
                button.textContent = '保存';
                button.style.background = '#52c41a';
                textarea.focus();
            } else {
                // 保存修改
                textarea.disabled = true;
                textarea.style.backgroundColor = '';
                button.textContent = '编辑';
                button.style.background = '#1890ff';
                
                // 显示保存成功提示
                showSaveSuccess(button);
            }
        }

        // 处理设备图片上传
        function handleEquipmentImageUpload(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (!data.processLayout) {
                        data.processLayout = {};
                    }
                    data.processLayout.layoutImage = e.target.result;
                    updateImagePreview();
                    saveDataAfterChange();
                };
                reader.readAsDataURL(file);
            }
        }

        // 更新设备图片预览
        function updateEquipmentImagePreview() {
            const previewArea = document.getElementById('equipmentImagePreviewArea');
            if (data.equipmentLayout?.layoutImage) {
                previewArea.innerHTML = `
                    <div style="position: relative; display: inline-block;">
                        <img src="${data.equipmentLayout.layoutImage}" 
                             style="max-height: calc(100% - 20px); max-width: 100%; object-fit: contain; cursor: pointer;" 
                             onclick="showImageModal(this.src)" 
                             alt="设备布局图">
                        <button onclick="deleteEquipmentLayoutImage()" 
                                style="position: absolute; top: 10px; right: 10px; 
                                       background: rgba(255, 77, 79, 0.8); color: white; 
                                       border: none; border-radius: 4px; padding: 5px 10px;
                                       cursor: pointer; font-size: 12px;">
                            删除图片
                        </button>
                    </div>`;
            } else {
                previewArea.innerHTML = '<p style="color: #999;">暂无图片</p>';
            }
        }

        // 删除设备布局图
        function deleteEquipmentLayoutImage() {
            if (confirm('确定要删除当前图片吗？')) {
                if (data.equipmentLayout) {
                    data.equipmentLayout.layoutImage = '';
                }
                updateEquipmentImagePreview();
                saveDataAfterChange();
            }
        }

        // 处理夹具图片上传
        function handleFixtureImagesUpload(event, moduleIndex) {
            const files = event.target.files;
            if (!data.fixtureModules) {
                data.fixtureModules = [];
            }
            if (!data.fixtureModules[moduleIndex]) {
                data.fixtureModules[moduleIndex] = {};
            }
            if (!data.fixtureModules[moduleIndex].images) {
                data.fixtureModules[moduleIndex].images = [];
            }

            Array.from(files).forEach(file => {
                if (file && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        data.fixtureModules[moduleIndex].images.push(e.target.result);
                        showContent('equipment');
                        saveDataAfterChange();
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // 删除夹具图片
        function deleteFixtureImage(moduleIndex, imageIndex) {
            if (confirm('确定要删除该图片吗？')) {
                data.fixtureModules[moduleIndex].images.splice(imageIndex, 1);
                showContent('equipment');
                saveDataAfterChange();
            }
        }

        // 切换夹具内容编辑状态
        function toggleFixtureEdit(moduleIndex, field) {
            const textarea = document.getElementById(`fixture${field.charAt(0).toUpperCase() + field.slice(1)}_${moduleIndex}`);
            const button = textarea.previousElementSibling.querySelector('button');

            if (textarea.disabled) {
                // 进入编辑模式
                textarea.disabled = false;
                textarea.style.backgroundColor = '#f6ffed';
                button.textContent = '保存';
                button.style.background = '#52c41a';
                textarea.focus();
            } else {
                // 保存更改
                textarea.disabled = true;
                textarea.style.backgroundColor = '';
                button.textContent = '编辑';
                button.style.background = '#1890ff';
                updateFixtureContent(moduleIndex, field, textarea.value);
                showSaveSuccess(button);
            }
        }

        // 更新夹具内容
        function updateFixtureContent(moduleIndex, field, value) {
            if (!data.fixtureModules) {
                data.fixtureModules = [];
            }
            if (!data.fixtureModules[moduleIndex]) {
                data.fixtureModules[moduleIndex] = {};
            }
            data.fixtureModules[moduleIndex][field] = value;
            saveDataAfterChange();
        }

        // 添加新的夹具模块
        function addNewFixtureModule() {
            if (!data.fixtureModules) {
                data.fixtureModules = [];
            }
            data.fixtureModules.push({
                images: [],
                keyPoints: '',
                specificMeasures: ''
            });
            showContent('equipment');
            saveDataAfterChange();
        }

        // 删除站位
        function deleteStation(index) {
            if (confirm('确定要删除这个站位吗？这将会删除所有相关的内容。')) {
                data.stations.splice(index, 1);
                showContent('process');
                saveDataAfterChange();
            }
        }

        // 处理图片上传
        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (!data.processLayout) {
                        data.processLayout = {};
                    }
                    data.processLayout.layoutImage = e.target.result;
                    updateImagePreview();
                    saveDataAfterChange();
                };
                reader.readAsDataURL(file);
            }
        }

        // 更新图片预览
        function updateImagePreview() {
            const previewArea = document.getElementById('imagePreviewArea');
            if (!previewArea) return;
            
            if (data.processLayout?.layoutImage) {
                previewArea.innerHTML = `
                    <div style="position: relative; display: inline-block;">
                        <img src="${data.processLayout.layoutImage}" 
                             style="max-width: 100%; max-height: 100%; object-fit: contain; cursor: pointer;" 
                             onclick="showImageModal(this.src)" 
                             alt="工艺布局图">
                        <button onclick="deleteLayoutImage()" 
                                style="position: absolute; top: 10px; right: 10px; 
                                       background: rgba(255, 77, 79, 0.8); color: white; 
                                       border: none; border-radius: 4px; padding: 5px 10px;
                                       cursor: pointer; font-size: 12px;">
                            删除图片
                        </button>
                    </div>`;
            } else {
                previewArea.innerHTML = '<p style="color: #999;">点击下方按钮上传图片</p>';
            }
        }

        // 删除工艺布局图
        function deleteLayoutImage() {
            if (confirm('确定要删除当前图片吗？')) {
                if (data.processLayout) {
                    data.processLayout.layoutImage = '';
                }
                updateImagePreview();
                saveDataAfterChange();
            }
        }

        // 添加新站位
        function addNewStation() {
            if (!data.stations) {
                data.stations = [];
            }
            
            const newStation = {
                number: '',
                name: '',
                processDescription: '',
                specificRequirements: '',
                images: []
            };
            
            data.stations.push(newStation);
            showContent('process');
            saveDataAfterChange();
        }

        // 更新站位信息
        function updateStationInfo(index, field, value) {
            if (!data.stations[index]) {
                data.stations[index] = {};
            }
            data.stations[index][field] = value;

            // 如果编辑的是number或name字段，更新站位标题显示
            if (field === 'number' || field === 'name') {
                const stationBlock = document.querySelector(`.station-block[data-index="${index}"]`);
                if (stationBlock) {
                    const title = stationBlock.querySelector('h3');
                    if (title) {
                        title.textContent = `站位 ${data.stations[index].number || ''} - ${data.stations[index].name || ''}`;
                    }
                }
            }

            saveDataAfterChange();
        }        // 添加设备站位
        function addEquipmentStation() {
            if (!data.equipmentStations) {
                data.equipmentStations = [];
            }
            
            // 创建新的设备站位对象
            const newStation = {
                number: '',
                name: '',
                layoutImage: '',
                length: '',
                width: '',
                height: '',
                takt: '',
                changeTime: '',
                mechanicalRequirement: '',
                electricalRequirement: '',
                showFixtureDetail: false,
                fixtureImages: [],
                keyPoints: '',
                measures: ''
            };
            
            // 添加到数组
            data.equipmentStations.push(newStation);
            
            // 更新显示
            showContent('equipment');
            
            // 保存更改
            saveDataAfterChange();
        }
        
        // 删除设备站位
        function deleteEquipmentStation(index) {
            if (confirm('确定要删除这个设备站位吗？')) {
                data.equipmentStations.splice(index, 1);
                showContent('equipment');
                saveDataAfterChange();
            }
        }
          // 更新设备站位字段
        function updateEquipmentField(index, field, value) {
            if (!data.equipmentStations[index]) {
                data.equipmentStations[index] = {};
            }
            data.equipmentStations[index][field] = value;
            saveDataAfterChange();
        }

        // 切换夹具分解机构详细要求显示状态
        function toggleFixtureDetail(index) {
            if (!data.equipmentStations[index]) {
                data.equipmentStations[index] = {};
            }
            data.equipmentStations[index].showFixtureDetail = !data.equipmentStations[index].showFixtureDetail;
            
            // 如果是删除操作，清空相关数据
            if (!data.equipmentStations[index].showFixtureDetail) {
                data.equipmentStations[index].fixtureImages = [];
                data.equipmentStations[index].keyPoints = '';
                data.equipmentStations[index].measures = '';
            }
            
            showContent('equipment');
            saveDataAfterChange();
        }

        // 处理夹具分解机构图片上传
        function handleFixtureImagesUpload(event, index) {
            const files = Array.from(event.target.files);
            if (!data.equipmentStations[index].fixtureImages) {
                data.equipmentStations[index].fixtureImages = [];
            }

            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        data.equipmentStations[index].fixtureImages.push(e.target.result);
                        showContent('equipment');
                        saveDataAfterChange();
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        // 删除夹具分解机构图片
        function deleteFixtureImage(stationIndex, imageIndex) {
            if (confirm('确定要删除这张图片吗？')) {
                data.equipmentStations[stationIndex].fixtureImages.splice(imageIndex, 1);
                showContent('equipment');
                saveDataAfterChange();
            }
        }
        
        // 处理设备图片上传
        function handleEquipmentImageUpload(event, index) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    data.equipmentStations[index].layoutImage = e.target.result;
                    showContent('equipment');
                    saveDataAfterChange();
                };
                reader.readAsDataURL(file);
            }
        }
    </script>
</body>
</html>
