#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建符合Prompt Lab格式的知识库文件
将Markdown格式转换为TXT格式，优化大模型理解
"""

import os
import re
from pathlib import Path

class PromptLabKnowledgeBaseCreator:
    def __init__(self):
        self.input_dir = "SAB知识库"
        self.output_dir = "Prompt_Lab知识库"
        self.sab_categories = ['A', 'B', 'C', 'D', 'E', 'F']
        
    def create_output_directory(self):
        """创建输出目录"""
        Path(self.output_dir).mkdir(exist_ok=True)
        print(f"创建输出目录: {self.output_dir}")
    
    def convert_markdown_to_txt(self, category):
        """将Markdown格式转换为TXT格式"""
        
        input_file = f"{self.input_dir}/SAB-{category}类.md"
        output_file = f"{self.output_dir}/SAB-{category}类知识库.txt"
        
        if not os.path.exists(input_file):
            print(f"❌ 输入文件不存在: {input_file}")
            return False
        
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 转换为TXT格式
            txt_content = self.markdown_to_txt(content, category)
            
            # 写入TXT文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(txt_content)
            
            print(f"✅ 已转换: SAB-{category}类知识库.txt")
            return True
            
        except Exception as e:
            print(f"❌ 转换失败 {input_file}: {e}")
            return False
    
    def markdown_to_txt(self, content, category):
        """将Markdown内容转换为TXT格式"""
        
        # 移除Markdown标记
        content = re.sub(r'^#+\s*', '', content, flags=re.MULTILINE)  # 移除标题标记
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # 移除粗体标记
        content = re.sub(r'\*(.*?)\*', r'\1', content)  # 移除斜体标记
        content = re.sub(r'`(.*?)`', r'\1', content)  # 移除代码标记
        
        # 构建TXT格式内容
        txt_content = f"""SAB-{category}类产品工艺设备知识库

=== 产品信息 ===
产品族: SAB
工艺类型: {category}
适用场景: SAB-{category}类产品的完整工艺流程和设备配置

=== 零件组成 ===
{self.extract_parts_section(content)}

=== 工艺流程规范 ===
{self.extract_process_section(content)}

=== 设备配置要求 ===
{self.extract_equipment_section(content)}

=== 应用说明 ===
本知识库适用于SAB-{category}类产品的工艺设计和设备配置。
当输入的零件组合匹配SAB-{category}类特征时，应参考此知识库生成相应的工艺流程和设备要求。
输出格式必须严格按照Markdown格式，包含工艺部分和设备部分的完整对应关系。

=== 输出格式要求 ===
必须按照以下结构输出：

### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

"""
        return txt_content
    
    def extract_parts_section(self, content):
        """提取零件部分"""
        parts_match = re.search(r'## 零件清单\n(.*?)(?=\n## |\n### |$)', content, re.DOTALL)
        if parts_match:
            parts_content = parts_match.group(1).strip()
            # 清理格式
            parts_content = re.sub(r'^\d+\.\s*', '- ', parts_content, flags=re.MULTILINE)
            return parts_content
        return "零件信息待补充"
    
    def extract_process_section(self, content):
        """提取工艺部分"""
        process_match = re.search(r'### 工艺部分\n(.*?)(?=\n### 设备部分|\n## |$)', content, re.DOTALL)
        if process_match:
            process_content = process_match.group(1).strip()
            # 保持工艺流程的结构
            return process_content
        return "工艺流程信息待补充"
    
    def extract_equipment_section(self, content):
        """提取设备部分"""
        equipment_match = re.search(r'### 设备部分\n(.*?)$', content, re.DOTALL)
        if equipment_match:
            equipment_content = equipment_match.group(1).strip()
            # 保持设备要求的结构
            return equipment_content
        return "设备配置信息待补充"
    
    def create_combined_knowledge_base(self):
        """创建合并的知识库文件"""
        
        combined_content = """SAB产品族完整知识库

=== 概述 ===
本知识库包含SAB产品族A-F所有类别的完整工艺流程和设备配置信息。
根据输入的零件组合，可以准确识别SAB类别并生成相应的工艺和设备要求。

=== SAB类别识别规则 ===
根据零件组合识别SAB类别：
- SAB-A类: Deflector, inflator, cushion, soft cover (4个零件)
- SAB-B类: Deflector, inflator, cushion, Harness, soft cover (5个零件)
- SAB-C类: Deflector, inflator, cushion, Harness, Bracket, Nuts, soft cover (7个零件)
- SAB-D类: Deflector, inflator, cushion, Harness, hard cover (5个零件)
- SAB-E类: Deflector, inflator, cushion, Bracket, Nuts, housing (6个零件)
- SAB-F类: Deflector, inflator, cushion, hard cover, housing, 3D heat (6个零件)

=== 输出格式要求 ===
必须严格按照以下Markdown格式输出，确保工艺部分和设备部分完全对应：

### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

"""
        
        # 添加每个SAB类别的详细信息
        for category in self.sab_categories:
            input_file = f"{self.input_dir}/SAB-{category}类.md"
            if os.path.exists(input_file):
                try:
                    with open(input_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    combined_content += f"\n{'='*50}\n"
                    combined_content += f"SAB-{category}类详细信息\n"
                    combined_content += f"{'='*50}\n\n"
                    
                    # 添加零件信息
                    parts_section = self.extract_parts_section(content)
                    combined_content += f"零件组成:\n{parts_section}\n\n"
                    
                    # 添加工艺信息
                    process_section = self.extract_process_section(content)
                    combined_content += f"工艺流程:\n{process_section}\n\n"
                    
                    # 添加设备信息
                    equipment_section = self.extract_equipment_section(content)
                    combined_content += f"设备配置:\n{equipment_section}\n\n"
                    
                except Exception as e:
                    print(f"⚠️ 读取 SAB-{category}类 时出错: {e}")
        
        # 保存合并文件
        combined_file = f"{self.output_dir}/SAB完整知识库.txt"
        try:
            with open(combined_file, 'w', encoding='utf-8') as f:
                f.write(combined_content)
            print(f"✅ 已创建合并知识库: SAB完整知识库.txt")
            return True
        except Exception as e:
            print(f"❌ 创建合并知识库失败: {e}")
            return False
    
    def create_all_formats(self):
        """创建所有格式的知识库文件"""
        
        print("🔄 开始创建Prompt Lab格式知识库...")
        print("=" * 60)
        
        success_count = 0
        
        # 创建单独的TXT文件
        for category in self.sab_categories:
            print(f"\n📝 转换 SAB-{category}类...")
            if self.convert_markdown_to_txt(category):
                success_count += 1
        
        # 创建合并的知识库文件
        print(f"\n📝 创建合并知识库...")
        if self.create_combined_knowledge_base():
            success_count += 1
        
        print(f"\n{'=' * 60}")
        print(f"📊 转换完成统计:")
        print(f"✅ 成功创建: {success_count} 个文件")
        
        return success_count > 0
    
    def run(self):
        """运行转换流程"""
        
        # 检查输入目录
        if not os.path.exists(self.input_dir):
            print(f"❌ 输入目录不存在: {self.input_dir}")
            return False
        
        # 创建输出目录
        self.create_output_directory()
        
        # 创建所有格式文件
        return self.create_all_formats()

def main():
    """主函数"""
    
    print("🚀 Prompt Lab知识库格式转换工具")
    print("=" * 60)
    print("支持格式: TXT (推荐)")
    print("输出目录: Prompt_Lab知识库/")
    
    creator = PromptLabKnowledgeBaseCreator()
    
    if creator.run():
        print(f"\n✅ 转换成功完成！")
        print(f"📁 输出目录: {creator.output_dir}")
        print(f"📄 生成的文件:")
        
        # 列出生成的文件
        if os.path.exists(creator.output_dir):
            for file in os.listdir(creator.output_dir):
                if file.endswith('.txt'):
                    file_path = os.path.join(creator.output_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"   - {file} ({file_size:,} bytes)")
        
        print(f"\n📋 上传建议:")
        print(f"1. 推荐上传: SAB完整知识库.txt (包含所有类别)")
        print(f"2. 或分别上传各个SAB类别的TXT文件")
        print(f"3. 文件格式: TXT (Prompt Lab支持)")
        
    else:
        print(f"\n❌ 转换过程中出现错误")

if __name__ == "__main__":
    main()
