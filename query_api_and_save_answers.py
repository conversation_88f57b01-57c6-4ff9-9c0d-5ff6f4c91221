import pandas as pd
import requests
import urllib3
import time
import os
from datetime import datetime

# Disable SSL warnings (only for development, not recommended for production)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Load the Excel file containing questions
def load_excel_file(file_path):
    try:
        data = pd.read_excel(file_path)
        print(f"成功加载Excel文件，共有{len(data)}行数据。")
        return data
    except Exception as e:
        print(f"加载Excel文件失败: {e}")
        return None

# API details
def query_api_for_answer(question, api_url, headers):
    """向API发送请求并获取回答"""
    
    # API请求体
    body = {
        'query': question,
        'chat_id': 9188,  # 确保这是正确的chatId
        'stream': False
    }
    
    try:
        # 发送API请求，禁用SSL验证（仅用于测试环境）
        response = requests.post(f"https://ach-sso01.ap.autoliv.int:18443/api/v1/chat-messages", 
                               headers=headers, 
                               json=body, 
                               verify=False,
                               timeout=30)  # 增加超时时间
        
        # 检查响应状态码
        response.raise_for_status()
        
        # 解析JSON响应
        response_json = response.json()
        
        # 从响应中提取答案，根据实际API响应格式调整
        answer = response_json.get("answer", "未能提供具体建议，请参考相关文档。")
        
        # 检查答案是否为空
        if not answer or answer.strip() == "":
            answer = "API返回的答案为空。"
            
        return answer, None
    
    except requests.exceptions.HTTPError as http_err:
        error_message = f"HTTP错误: {http_err}"
        print(error_message)
        return None, error_message
    
    except requests.exceptions.Timeout:
        error_message = "请求超时，请稍后再试。"
        print(error_message)
        return None, error_message
        
    except ValueError as json_err:
        error_message = f"无法解析响应为JSON，响应内容: {response.text}"
        print(error_message)
        return None, error_message
        
    except Exception as e:
        error_message = f"发生未知错误: {e}"
        print(error_message)
        return None, error_message

def main():
    # 文件路径设置
    input_file_path = input("请输入Excel文件路径: ").strip()
    
    # 检查文件是否存在
    if not os.path.exists(input_file_path):
        print(f"错误: 文件 '{input_file_path}' 不存在。")
        return

    # 加载数据
    data = load_excel_file(input_file_path)
    if data is None:
        return
    
    # 检查必要的列是否存在
    question_column = '常见问题'
    answer_column = 'AI生成的答案'
    
    if question_column not in data.columns:
        print(f"错误: 列 '{question_column}' 不存在。请确保Excel文件包含此列。")
        return
    
    # 如果答案列不存在，添加它
    if answer_column not in data.columns:
        data[answer_column] = ""
    
    # API配置
    api_url = 'https://ach-sso01.ap.autoliv.int:18443/api/v1/chat-messages'
    
    headers = {
        'Authorization': 'Bearer Ey0H37iNjuDk/t6pDNiKnjA+QKoCPNYXIskz6MZijYat6ow31IsDdVBgniQtcVQCJjxFDYVYlMGjlvhuj6ij9TWLHXJpRFC0xcia+xb/N/T8u5Nzp1stCwUXkUyZS7qqU71+1eqUzpORyrNX+yqZVGeHSdKgh2x4lkn+AKdBH0JKldzVBtghjQ==',
        'X-API-Key': '0b2c4eec9-be01-46f6-a501-f313a348d979',
        'Content-Type': 'application/json'
    }
    
    # 创建一个带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file_path = os.path.splitext(input_file_path)[0] + f"_answers_{timestamp}.xlsx"
    
    # 处理计数器
    total_questions = len(data)
    success_count = 0
    error_count = 0
    
    # 遍历每一个问题，通过API获取答案
    for index, row in data.iterrows():
        question = row[question_column]
        print(f"\n处理问题 {index+1}/{total_questions}: {question[:50]}...")
        
        # 调用API获取答案
        answer, error = query_api_for_answer(question, api_url, headers)
        
        if answer:
            # 更新数据框
            data.at[index, answer_column] = answer
            success_count += 1
            print(f"成功获取答案，长度: {len(answer)} 字符")
        else:
            # 记录错误信息
            data.at[index, answer_column] = f"获取答案失败: {error}"
            error_count += 1
        
        # 每处理完5个问题，就保存一次进度
        if (index + 1) % 5 == 0 or index == total_questions - 1:
            try:
                data.to_excel(output_file_path, index=False)
                print(f"进度已保存至 {output_file_path}")
            except Exception as e:
                print(f"保存文件时出错: {e}")
        
        # 短暂暂停，避免API请求过于频繁
        time.sleep(1)
    
    # 最终结果保存
    try:
        data.to_excel(output_file_path, index=False)
        print(f"\n处理完成! 结果已保存至 {output_file_path}")
        print(f"总问题数: {total_questions}, 成功: {success_count}, 失败: {error_count}")
    except Exception as e:
        print(f"保存最终结果时出错: {e}")

if __name__ == "__main__":
    main() 