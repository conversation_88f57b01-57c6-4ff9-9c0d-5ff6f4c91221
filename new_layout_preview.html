
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新工艺步骤布局预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .step-preview { 
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white; 
        }
        .step-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem; 
        }
        .step-title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .step-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .operator-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        .content-row { 
            display: flex; 
            align-items: center; 
            gap: 0.5rem; 
            margin-bottom: 0.4rem; 
        }
        .content-row:last-child {
            margin-bottom: 0;
        }
        label { 
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap; 
            min-width: 90px;
        }
        .operator-label {
            min-width: auto;
        }
        textarea { 
            flex: 1; 
            height: 35px; 
            padding: 0.3rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            resize: none; 
            font-size: 0.8rem; 
            line-height: 1.3; 
        }
        select { 
            width: 80px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 28px; 
        }
        .delete-btn {
            background: #ff7875; 
            color: white; 
            border: none; 
            border-radius: 2px; 
            padding: 1px 4px; 
            cursor: pointer; 
            font-size: 0.65rem;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>新工艺步骤布局预览</h2>
        <p>新的布局设计："人or设备"移到步骤标题右边，三个标题各占一行</p>
        
        <div class="step-preview">
            <!-- 步骤标题行：步骤号 + 人or设备 + 删除按钮 -->
            <div class="step-header">
                <div class="step-title-group">
                    <h6 class="step-title">步骤 1</h6>
                    <div class="operator-group">
                        <label class="operator-label">人or设备:</label>
                        <select>
                            <option>人</option>
                        </select>
                    </div>
                </div>
                <button class="delete-btn">×</button>
            </div>
            
            <!-- 第一行：工艺过程描述 -->
            <div class="content-row">
                <label>工艺过程描述:</label>
                <textarea placeholder="请输入工艺过程描述">人工检查包装材料完整性</textarea>
            </div>
            
            <!-- 第二行：产品特性要求 -->
            <div class="content-row">
                <label>产品特性要求:</label>
                <textarea placeholder="请输入产品特性要求">包装材料无破损，标签清晰</textarea>
            </div>
            
            <!-- 第三行：过程防错要求 -->
            <div class="content-row">
                <label>过程防错要求:</label>
                <textarea placeholder="请输入过程防错要求">目视检查，发现问题立即停止</textarea>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>新布局改进说明：</h3>
            <ul>
                <li>✅ "人or设备"移到步骤标题（步骤1/2/3...）右边</li>
                <li>✅ 步骤标题、人or设备选择器、删除按钮在同一行</li>
                <li>✅ 工艺过程描述单独占一行</li>
                <li>✅ 产品特性要求单独占一行</li>
                <li>✅ 过程防错要求单独占一行</li>
                <li>✅ 所有标签设置统一的最小宽度（90px）</li>
                <li>✅ 布局更加清晰，层次分明</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px;">
            <h3>布局结构：</h3>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 步骤 1    人or设备: [选择器]                      × │
├─────────────────────────────────────────────────────────┤
│ 工艺过程描述: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求: [──────────文本框──────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
    </div>
</body>
</html>
        