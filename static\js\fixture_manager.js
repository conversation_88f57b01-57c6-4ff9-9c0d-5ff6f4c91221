/**
 * 夹具清单管理功能
 */

// 夹具数据（从API加载）
let fixtureData = {};
let filteredData = {};
let isDataLoaded = false;

// 从API加载夹具数据
async function loadFixtureData() {
    try {
        console.log('开始从API加载夹具数据...');

        const response = await fetch('/api/fixture_data');
        const result = await response.json();

        if (result.success) {
            fixtureData = result.data;
            filteredData = fixtureData;
            isDataLoaded = true;

            console.log('夹具数据加载成功:', fixtureData);

            // 初始化界面
            initFixtureList();

        } else {
            console.error('加载夹具数据失败:', result.message);
            showToast('加载夹具数据失败: ' + result.message);

            // 使用默认数据
            useDefaultFixtureData();
        }

    } catch (error) {
        console.error('加载夹具数据时发生错误:', error);
        showToast('加载夹具数据时发生错误');

        // 使用默认数据
        useDefaultFixtureData();
    }
}

// 使用默认夹具数据（作为备用）
function useDefaultFixtureData() {
    console.log('使用默认夹具数据...');

    fixtureData = {
        "SAB-A": [
            {"station": "ST10", "content": "充气器安装", "equipment_name": "充气器安装夹具", "brand": "KUKA", "model": "KR-10", "quantity": "1"},
            {"station": "ST15", "content": "气囊折叠", "equipment_name": "气囊折叠设备", "brand": "FANUC", "model": "LR-200", "quantity": "1"}
        ],
        "SAB-B": [
            {"station": "ST10", "content": "线束连接", "equipment_name": "线束连接夹具", "brand": "ABB", "model": "IRB-140", "quantity": "1"},
            {"station": "ST20", "content": "线束测试", "equipment_name": "线束测试设备", "brand": "HIOKI", "model": "3560", "quantity": "1"}
        ]
    };

    filteredData = fixtureData;
    isDataLoaded = true;

    // 初始化界面
    initFixtureList();
}

// 初始化夹具清单
function initFixtureList() {
    console.log('初始化夹具清单界面...');

    if (!isDataLoaded) {
        console.log('数据未加载，等待数据加载完成...');
        return;
    }

    // 初始化工站筛选器
    initStationFilter();

    // 更新统计概览
    updateFixtureSummary();

    // 渲染夹具表格
    renderFixtureTable();

    console.log('夹具清单初始化完成');
}

// 初始化工站筛选器
function initStationFilter() {
    const stationFilter = document.getElementById('station-filter');
    if (!stationFilter) return;
    
    // 获取所有工站
    const allStations = new Set();
    Object.values(fixtureData).forEach(items => {
        items.forEach(item => allStations.add(item.station));
    });
    
    // 清空现有选项（保留"全部工站"）
    stationFilter.innerHTML = '<option value="">全部工站</option>';
    
    // 添加工站选项
    Array.from(allStations).sort().forEach(station => {
        const option = document.createElement('option');
        option.value = station;
        option.textContent = station;
        stationFilter.appendChild(option);
    });
}

// 更新统计概览
function updateFixtureSummary() {
    let totalFixtures = 0;
    let allStations = new Set();
    let allBrands = new Set();
    let activeCategories = 0;
    
    Object.keys(filteredData).forEach(sabType => {
        if (filteredData[sabType].length > 0) {
            activeCategories++;
            totalFixtures += filteredData[sabType].length;
            
            filteredData[sabType].forEach(item => {
                allStations.add(item.station);
                if (item.brand) allBrands.add(item.brand);
            });
        }
    });
    
    // 更新显示
    const totalFixturesEl = document.getElementById('total-fixtures');
    const totalStationsEl = document.getElementById('total-stations');
    const totalBrandsEl = document.getElementById('total-brands');
    const activeCategoriesEl = document.getElementById('active-categories');
    
    if (totalFixturesEl) totalFixturesEl.textContent = totalFixtures;
    if (totalStationsEl) totalStationsEl.textContent = allStations.size;
    if (totalBrandsEl) totalBrandsEl.textContent = allBrands.size;
    if (activeCategoriesEl) activeCategoriesEl.textContent = activeCategories;
}

// 渲染夹具表格
function renderFixtureTable() {
    const tbody = document.getElementById('fixture-table-body');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    Object.keys(filteredData).forEach(sabType => {
        filteredData[sabType].forEach((item, index) => {
            const row = document.createElement('tr');
            
            // 生成类别徽章的CSS类名
            const badgeClass = sabType.toLowerCase().replace('-', '-');
            
            row.innerHTML = `
                <td><span class="fixture-category-badge ${badgeClass}">${sabType}</span></td>
                <td><span class="fixture-station-badge">${item.station}</span></td>
                <td><strong>${item.equipment_name}</strong></td>
                <td>${item.brand || '-'}</td>
                <td>${item.model || '-'}</td>
                <td>${item.quantity || '1'}</td>
                <td>${item.content || '-'}</td>
                <td>
                    <button onclick="viewFixtureDetails('${sabType}', ${index})" class="btn-small">详情</button>
                </td>
            `;
            
            tbody.appendChild(row);
        });
    });
    
    // 如果没有数据，显示提示
    if (tbody.children.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="8" style="text-align: center; padding: 2rem; color: #666;">
                <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">📋</div>
                <div>暂无符合条件的夹具数据</div>
                <div style="font-size: 0.9rem; margin-top: 0.5rem;">请调整筛选条件或重置筛选器</div>
            </td>
        `;
        tbody.appendChild(row);
    }
}

// 筛选夹具
function filterFixtures() {
    const sabFilter = document.getElementById('sab-type-filter')?.value || '';
    const stationFilter = document.getElementById('station-filter')?.value || '';
    const searchTerm = document.getElementById('fixture-search')?.value.toLowerCase() || '';
    
    filteredData = {};
    
    Object.keys(fixtureData).forEach(sabType => {
        // SAB类别筛选
        if (sabFilter && sabType !== sabFilter) return;
        
        // 筛选该类别的数据
        const filteredItems = fixtureData[sabType].filter(item => {
            // 工站筛选
            if (stationFilter && item.station !== stationFilter) return false;
            
            // 搜索筛选
            if (searchTerm) {
                const searchableText = [
                    item.equipment_name,
                    item.brand,
                    item.model,
                    item.content,
                    item.station
                ].join(' ').toLowerCase();
                
                if (!searchableText.includes(searchTerm)) return false;
            }
            
            return true;
        });
        
        if (filteredItems.length > 0) {
            filteredData[sabType] = filteredItems;
        }
    });
    
    updateFixtureSummary();
    renderFixtureTable();
}

// 搜索夹具
function searchFixtures() {
    filterFixtures(); // 搜索也使用统一的筛选逻辑
}

// 重置筛选器
function resetFixtureFilters() {
    const sabFilter = document.getElementById('sab-type-filter');
    const stationFilter = document.getElementById('station-filter');
    const searchInput = document.getElementById('fixture-search');
    
    if (sabFilter) sabFilter.value = '';
    if (stationFilter) stationFilter.value = '';
    if (searchInput) searchInput.value = '';
    
    filteredData = fixtureData;
    updateFixtureSummary();
    renderFixtureTable();
    
    showToast('筛选器已重置');
}

// 查看夹具详情
function viewFixtureDetails(sabType, index) {
    const item = filteredData[sabType][index];
    if (!item) return;
    
    const detailsContent = `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
            <div>
                <h4 style="color: #1a73e8; margin-bottom: 1rem;">${item.equipment_name}</h4>
                <div style="margin-bottom: 0.8rem;">
                    <strong>SAB类别:</strong> 
                    <span class="fixture-category-badge ${sabType.toLowerCase().replace('-', '-')}">${sabType}</span>
                </div>
                <div style="margin-bottom: 0.8rem;">
                    <strong>工站:</strong> 
                    <span class="fixture-station-badge">${item.station}</span>
                </div>
                <div style="margin-bottom: 0.8rem;">
                    <strong>内容描述:</strong> ${item.content || '无描述'}
                </div>
            </div>
            <div>
                <h5 style="color: #666; margin-bottom: 1rem;">设备信息</h5>
                <div style="margin-bottom: 0.8rem;">
                    <strong>品牌:</strong> ${item.brand || '未指定'}
                </div>
                <div style="margin-bottom: 0.8rem;">
                    <strong>型号:</strong> ${item.model || '未指定'}
                </div>
                <div style="margin-bottom: 0.8rem;">
                    <strong>数量:</strong> ${item.quantity || '1'} 台/套
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('fixture-details-content').innerHTML = detailsContent;
    document.getElementById('fixture-details').style.display = 'block';
    
    // 滚动到详情面板
    document.getElementById('fixture-details').scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
    });
}

// 关闭夹具详情
function closeFixtureDetails() {
    document.getElementById('fixture-details').style.display = 'none';
}

// 导出夹具清单
function exportFixtureList() {
    const data = {
        exportTime: new Date().toISOString(),
        exportType: 'fixture_list',
        filters: {
            sabType: document.getElementById('sab-type-filter')?.value || '',
            station: document.getElementById('station-filter')?.value || '',
            search: document.getElementById('fixture-search')?.value || ''
        },
        fixtureData: filteredData,
        summary: {
            totalFixtures: Object.values(filteredData).reduce((sum, items) => sum + items.length, 0),
            totalCategories: Object.keys(filteredData).length,
            totalStations: new Set(Object.values(filteredData).flat().map(item => item.station)).size
        }
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fixture_list_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    showToast('夹具清单已导出');
}

// 打印夹具清单
function printFixtureList() {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintableFixtureList();
    
    printWindow.document.write(`
        <html>
            <head>
                <title>夹具清单</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    th { background-color: #f2f2f2; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .summary { margin-bottom: 20px; }
                    .badge { padding: 2px 6px; border-radius: 3px; font-size: 12px; }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// 生成可打印的夹具清单内容
function generatePrintableFixtureList() {
    const totalFixtures = Object.values(filteredData).reduce((sum, items) => sum + items.length, 0);
    const totalCategories = Object.keys(filteredData).length;
    
    let html = `
        <div class="header">
            <h1>🔧 设备夹具清单</h1>
            <p>生成时间: ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <p><strong>统计概览:</strong> 共 ${totalFixtures} 个夹具设备，涵盖 ${totalCategories} 个SAB类别</p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>SAB类别</th>
                    <th>工站</th>
                    <th>设备/夹具名称</th>
                    <th>品牌</th>
                    <th>型号</th>
                    <th>数量</th>
                    <th>内容描述</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    Object.keys(filteredData).forEach(sabType => {
        filteredData[sabType].forEach(item => {
            html += `
                <tr>
                    <td>${sabType}</td>
                    <td>${item.station}</td>
                    <td>${item.equipment_name}</td>
                    <td>${item.brand || '-'}</td>
                    <td>${item.model || '-'}</td>
                    <td>${item.quantity || '1'}</td>
                    <td>${item.content || '-'}</td>
                </tr>
            `;
        });
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    return html;
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在夹具清单页面
    if (document.getElementById('fixture-table-body')) {
        console.log('检测到夹具清单页面，开始加载数据...');
        setTimeout(loadFixtureData, 100); // 延迟初始化确保DOM完全加载
    }
});

// 当切换到夹具清单页面时也要加载数据
function onFixturePageShow() {
    if (!isDataLoaded) {
        console.log('切换到夹具清单页面，开始加载数据...');
        loadFixtureData();
    }
}
