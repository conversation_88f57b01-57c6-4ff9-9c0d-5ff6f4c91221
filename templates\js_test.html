<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript文件测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JavaScript文件加载测试</h1>
        
        <div class="test-panel">
            <h2>📋 加载状态</h2>
            <div id="load-status" class="status info">
                ℹ️ 正在检查JavaScript文件加载...
            </div>
        </div>

        <div class="test-panel">
            <h2>📝 详细日志</h2>
            <div id="test-log" class="log">
                测试日志将在这里显示...
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const element = document.getElementById('load-status');
            const icons = { success: '✅', error: '❌', info: 'ℹ️' };
            element.className = `status ${type}`;
            element.textContent = `${icons[type]} ${message}`;
        }

        // 监听脚本加载错误
        window.addEventListener('error', function(event) {
            if (event.target.tagName === 'SCRIPT') {
                log(`❌ 脚本加载失败: ${event.target.src}`, 'error');
                updateStatus('JavaScript文件加载失败', 'error');
            } else {
                log(`❌ JavaScript错误: ${event.error.message}`, 'error');
                log(`错误位置: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
                updateStatus('JavaScript执行错误', 'error');
            }
        }, true);

        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面DOM加载完成');
            
            // 延迟检查，确保所有脚本都尝试加载
            setTimeout(() => {
                log('=== 开始检查JavaScript文件 ===');
                
                // 检查station_generator.js
                log('检查station_generator.js...');
                if (typeof StationGenerator !== 'undefined') {
                    log('✅ StationGenerator类已定义');
                } else {
                    log('❌ StationGenerator类未定义', 'error');
                }
                
                // 检查station_manager.js中的函数
                log('检查station_manager.js...');
                const managerFunctions = [
                    'insertProcessStep',
                    'deleteProcessStep', 
                    'addProcessStep',
                    'regenerateProcessStation',
                    'insertProcessStepBefore',
                    'insertProcessStepAfter'
                ];
                
                let managerLoaded = true;
                managerFunctions.forEach(funcName => {
                    const exists = typeof window[funcName] === 'function';
                    log(`   ${funcName}: ${exists ? '✅' : '❌'}`);
                    if (!exists) managerLoaded = false;
                });
                
                if (managerLoaded) {
                    log('✅ station_manager.js加载成功');
                    updateStatus('所有JavaScript文件加载成功', 'success');
                } else {
                    log('❌ station_manager.js加载失败', 'error');
                    updateStatus('station_manager.js加载失败', 'error');
                }
                
                log('=== 检查完成 ===');
            }, 1000);
        });
    </script>

    <!-- 先加载station_generator.js -->
    <script>
        log('开始加载station_generator.js...');
    </script>
    <script src="/static/js/station_generator.js" onload="log('station_generator.js加载完成')" onerror="log('station_generator.js加载失败', 'error')"></script>

    <!-- 再加载station_manager.js -->
    <script>
        log('开始加载station_manager.js...');
    </script>
    <script src="/static/js/station_manager.js" onload="log('station_manager.js加载完成')" onerror="log('station_manager.js加载失败', 'error')"></script>
</body>
</html>
