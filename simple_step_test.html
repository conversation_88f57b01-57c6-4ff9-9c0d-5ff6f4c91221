<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单步骤功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单步骤功能测试</h1>
        
        <div class="test-panel">
            <h2>📋 测试控制</h2>
            <button class="btn primary" onclick="initTest()">初始化测试</button>
            <button class="btn success" onclick="testAddStep()">测试添加步骤</button>
            <button class="btn danger" onclick="testDeleteStep()">测试删除步骤</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
            
            <div id="status" class="status info">
                ℹ️ 请先点击"初始化测试"
            </div>
        </div>

        <div class="test-panel">
            <h3>🏭 工站显示</h3>
            <div id="stations-list" style="min-height: 200px; border: 2px dashed #ccc; padding: 20px; border-radius: 6px;">
                工站将在这里显示...
            </div>
        </div>

        <div class="test-panel">
            <h3>📝 测试日志</h3>
            <div id="log" class="log">
                日志将在这里显示...
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>

    <script>
        let testStationIndex = 0;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            const icons = { success: '✅', error: '❌', info: 'ℹ️' };
            statusElement.className = `status ${type}`;
            statusElement.textContent = `${icons[type]} ${message}`;
        }

        function getStepCount() {
            if (processStationsData && processStationsData[0] && processStationsData[0].process_steps) {
                return processStationsData[0].process_steps.length;
            }
            return 0;
        }

        function initTest() {
            log('开始初始化测试...');
            
            try {
                // 检查必要的类和函数
                log(`StationGenerator: ${typeof StationGenerator !== 'undefined' ? '已定义' : '未定义'}`);
                log(`insertProcessStep: ${typeof insertProcessStep === 'function' ? '已定义' : '未定义'}`);
                log(`deleteProcessStep: ${typeof deleteProcessStep === 'function' ? '已定义'  : '未定义'}`);
                
                if (typeof StationGenerator === 'undefined') {
                    throw new Error('StationGenerator类未定义');
                }

                // 创建测试数据
                const testStation = {
                    station_number: '10',
                    station_name: '测试工站',
                    content: '测试工站内容',
                    process_steps: [
                        {
                            step_number: '1',
                            description: '测试步骤1',
                            operator: '人',
                            quality_requirements: '质量要求1',
                            error_prevention: '防错要求1'
                        },
                        {
                            step_number: '2',
                            description: '测试步骤2',
                            operator: '设备',
                            quality_requirements: '质量要求2',
                            error_prevention: '防错要求2'
                        }
                    ]
                };

                // 设置全局数据
                window.processStationsData = [testStation];
                
                // 创建生成器
                if (!window.stationGenerator) {
                    window.stationGenerator = new StationGenerator();
                }
                
                // 生成显示
                stationGenerator.generateProcessStations([testStation]);
                
                log(`初始化完成，当前步骤数: ${getStepCount()}`);
                updateStatus('初始化成功，可以开始测试', 'success');

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                updateStatus(`初始化失败: ${error.message}`, 'error');
            }
        }

        function testAddStep() {
            log('测试添加步骤...');
            
            try {
                const beforeCount = getStepCount();
                log(`添加前步骤数: ${beforeCount}`);
                
                if (typeof insertProcessStep !== 'function') {
                    throw new Error('insertProcessStep函数未定义');
                }
                
                // 在末尾添加步骤
                insertProcessStep(testStationIndex, beforeCount);
                
                // 延迟检查结果
                setTimeout(() => {
                    const afterCount = getStepCount();
                    log(`添加后步骤数: ${afterCount}`);
                    
                    if (afterCount === beforeCount + 1) {
                        log('添加步骤成功', 'success');
                        updateStatus('添加步骤测试通过', 'success');
                    } else {
                        log('添加步骤失败', 'error');
                        updateStatus('添加步骤测试失败', 'error');
                    }
                }, 100);

            } catch (error) {
                log(`添加步骤失败: ${error.message}`, 'error');
                updateStatus(`添加步骤失败: ${error.message}`, 'error');
            }
        }

        function testDeleteStep() {
            log('测试删除步骤...');
            
            try {
                const beforeCount = getStepCount();
                log(`删除前步骤数: ${beforeCount}`);
                
                if (beforeCount === 0) {
                    log('没有步骤可删除', 'error');
                    updateStatus('没有步骤可删除', 'error');
                    return;
                }
                
                if (typeof deleteProcessStep !== 'function') {
                    throw new Error('deleteProcessStep函数未定义');
                }
                
                // 模拟用户确认
                const originalConfirm = window.confirm;
                window.confirm = () => {
                    log('模拟用户确认删除');
                    return true;
                };
                
                // 删除第一个步骤
                deleteProcessStep(testStationIndex, 0);
                
                // 延迟检查结果
                setTimeout(() => {
                    const afterCount = getStepCount();
                    log(`删除后步骤数: ${afterCount}`);
                    
                    if (afterCount === beforeCount - 1) {
                        log('删除步骤成功', 'success');
                        updateStatus('删除步骤测试通过', 'success');
                    } else {
                        log('删除步骤失败', 'error');
                        updateStatus('删除步骤测试失败', 'error');
                    }
                    
                    // 恢复原始confirm函数
                    window.confirm = originalConfirm;
                }, 100);

            } catch (error) {
                log(`删除步骤失败: ${error.message}`, 'error');
                updateStatus(`删除步骤失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            
            // 检查关键函数和类
            const checks = [
                ['StationGenerator', typeof StationGenerator !== 'undefined'],
                ['insertProcessStep', typeof insertProcessStep === 'function'],
                ['insertProcessStepBefore', typeof insertProcessStepBefore === 'function'],
                ['insertProcessStepAfter', typeof insertProcessStepAfter === 'function'],
                ['deleteProcessStep', typeof deleteProcessStep === 'function'],
                ['addProcessStep', typeof addProcessStep === 'function'],
                ['showStepInsertMenu', typeof showStepInsertMenu === 'function'],
                ['hideStepInsertMenu', typeof hideStepInsertMenu === 'function']
            ];
            
            let allGood = true;
            checks.forEach(([name, exists]) => {
                log(`${name}: ${exists ? '✅' : '❌'}`);
                if (!exists) allGood = false;
            });
            
            if (allGood) {
                updateStatus('所有必要函数都已加载', 'success');
            } else {
                updateStatus('部分函数未加载', 'error');
            }
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error.message}`, 'error');
            updateStatus(`发生错误: ${event.error.message}`, 'error');
        });

        // 监听点击事件进行调试
        document.addEventListener('click', function(event) {
            if (event.target.tagName === 'BUTTON') {
                const buttonText = event.target.textContent;
                if (buttonText.includes('+') || buttonText.includes('×') || buttonText.includes('插入') || buttonText.includes('添加')) {
                    log(`点击了按钮: ${buttonText}`);
                }
            }
        });
    </script>
</body>
</html>
