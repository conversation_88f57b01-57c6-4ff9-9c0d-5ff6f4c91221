<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工站修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 工站修复测试</h1>
        
        <div class="test-panel">
            <h2>📋 测试控制</h2>
            <button class="btn primary" onclick="initTestData()">初始化AI识别数据</button>
            <button class="btn success" onclick="testInsertStation()">测试插入新工站</button>
            <button class="btn success" onclick="testStepOperations()">测试步骤操作</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
            
            <div id="status" class="status info">
                ℹ️ 请先点击"初始化AI识别数据"
            </div>
        </div>

        <div class="test-panel">
            <h3>🏭 工站显示区域</h3>
            <div id="stations-list" style="min-height: 300px; border: 2px dashed #ccc; padding: 20px; border-radius: 6px;">
                工站将在这里显示...
            </div>
        </div>

        <div class="test-panel">
            <h3>📝 测试日志</h3>
            <div id="log" class="log">
                日志将在这里显示...
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="/static/js/station_generator.js"></script>
    <script src="/static/js/station_manager.js"></script>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStatus(message, type = 'info') {
            const element = document.getElementById('status');
            const icons = { success: '✅', error: '❌', info: 'ℹ️' };
            element.className = `status ${type}`;
            element.textContent = `${icons[type]} ${message}`;
        }

        function initTestData() {
            log('初始化AI识别的工站数据...');
            
            try {
                // 模拟AI识别的工站数据
                const aiGeneratedStations = [
                    {
                        station_number: '10',
                        station_name: 'AI识别工站1',
                        content: 'AI生成的工站内容1',
                        process_steps: [
                            {
                                step_number: '1',
                                description: 'AI识别步骤1',
                                operator: '人',
                                quality_requirements: 'AI质量要求1',
                                error_prevention: 'AI防错要求1'
                            },
                            {
                                step_number: '2',
                                description: 'AI识别步骤2',
                                operator: '设备',
                                quality_requirements: 'AI质量要求2',
                                error_prevention: 'AI防错要求2'
                            }
                        ]
                    },
                    {
                        station_number: '15',
                        station_name: 'AI识别工站2',
                        content: 'AI生成的工站内容2',
                        process_steps: [
                            {
                                step_number: '1',
                                description: 'AI识别步骤1',
                                operator: '人',
                                quality_requirements: 'AI质量要求1',
                                error_prevention: 'AI防错要求1'
                            }
                        ]
                    }
                ];

                // 设置全局数据
                window.processStationsData = aiGeneratedStations;
                
                // 确保stationGenerator存在
                if (!window.stationGenerator) {
                    window.stationGenerator = new StationGenerator();
                }
                
                // 生成AI识别的工站（清空现有内容）
                window.stationGenerator.generateProcessStations(aiGeneratedStations, false);
                
                log(`✅ 已生成 ${aiGeneratedStations.length} 个AI识别工站`);
                updateStatus('AI识别数据初始化成功，现在可以测试步骤编辑', 'success');
                
            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                updateStatus(`初始化失败: ${error.message}`, 'error');
            }
        }

        function testInsertStation() {
            log('测试插入新工站（应保留现有AI识别数据）...');
            
            try {
                const beforeCount = window.processStationsData ? window.processStationsData.length : 0;
                log(`插入前工站数: ${beforeCount}`);
                
                // 调用插入工站函数
                if (typeof insertProcessStationBefore === 'function') {
                    insertProcessStationBefore(0); // 在第一个工站前插入
                    
                    setTimeout(() => {
                        const afterCount = window.processStationsData ? window.processStationsData.length : 0;
                        log(`插入后工站数: ${afterCount}`);
                        
                        if (afterCount > beforeCount) {
                            log('✅ 插入新工站成功，AI数据已保留', 'success');
                            updateStatus('插入工站测试通过！', 'success');
                        } else {
                            log('❌ 插入新工站失败', 'error');
                            updateStatus('插入工站测试失败', 'error');
                        }
                    }, 200);
                } else {
                    log('❌ insertProcessStationBefore函数不存在', 'error');
                    updateStatus('插入工站函数不存在', 'error');
                }
                
            } catch (error) {
                log(`插入工站测试失败: ${error.message}`, 'error');
                updateStatus(`插入工站测试失败: ${error.message}`, 'error');
            }
        }

        function testStepOperations() {
            log('测试步骤操作（在AI识别的工站中）...');
            
            try {
                if (!window.processStationsData || window.processStationsData.length === 0) {
                    log('❌ 没有工站数据，请先初始化', 'error');
                    updateStatus('请先初始化AI识别数据', 'error');
                    return;
                }
                
                // 找到第一个有步骤的工站
                let targetStationIndex = -1;
                for (let i = 0; i < window.processStationsData.length; i++) {
                    if (window.processStationsData[i].process_steps && window.processStationsData[i].process_steps.length > 0) {
                        targetStationIndex = i;
                        break;
                    }
                }
                
                if (targetStationIndex === -1) {
                    log('❌ 没有找到有步骤的工站', 'error');
                    return;
                }
                
                const station = window.processStationsData[targetStationIndex];
                const beforeCount = station.process_steps.length;
                log(`在工站 ${station.station_name} 中测试步骤操作`);
                log(`操作前步骤数: ${beforeCount}`);
                
                // 测试添加步骤
                if (typeof insertProcessStep === 'function') {
                    log(`调用 insertProcessStep(${targetStationIndex}, ${beforeCount})`);
                    log(`调用前数据状态: ${JSON.stringify(station.process_steps)}`);

                    insertProcessStep(targetStationIndex, beforeCount);

                    setTimeout(() => {
                        const afterCount = station.process_steps.length;
                        log(`添加后步骤数: ${afterCount}`);
                        log(`调用后数据状态: ${JSON.stringify(station.process_steps)}`);

                        if (afterCount > beforeCount) {
                            log('✅ 在AI识别工站中添加步骤成功', 'success');
                            
                            // 测试删除步骤
                            const originalConfirm = window.confirm;
                            window.confirm = () => true;
                            
                            deleteProcessStep(targetStationIndex, 0);
                            
                            setTimeout(() => {
                                const finalCount = station.process_steps.length;
                                log(`删除后步骤数: ${finalCount}`);
                                
                                if (finalCount < afterCount) {
                                    log('✅ 在AI识别工站中删除步骤成功', 'success');
                                    updateStatus('所有步骤操作测试通过！', 'success');
                                } else {
                                    log('❌ 删除步骤失败', 'error');
                                }
                                
                                window.confirm = originalConfirm;
                            }, 200);
                        } else {
                            log('❌ 添加步骤失败', 'error');
                            updateStatus('添加步骤失败', 'error');
                        }
                    }, 200);
                } else {
                    log('❌ insertProcessStep函数不存在', 'error');
                }
                
            } catch (error) {
                log(`步骤操作测试失败: ${error.message}`, 'error');
                updateStatus(`步骤操作测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            log('这个测试页面将验证以下修复:');
            log('1. AI识别的工站内容可以编辑步骤');
            log('2. 插入新工站时保留现有AI数据');
            log('3. 减少冗余的工站插入按钮');
        });
    </script>
</body>
</html>
