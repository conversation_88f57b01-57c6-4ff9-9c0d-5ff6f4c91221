
/* 夹具清单样式 */
.fixture-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.control-group select,
.control-group input {
    padding: 0.5rem;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    font-size: 0.9rem;
}

.fixture-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.summary-number {
    font-size: 2rem;
    font-weight: bold;
    color: #1a73e8;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
}

.fixture-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fixture-table {
    width: 100%;
    border-collapse: collapse;
}

.fixture-table th {
    background: #1a73e8;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 500;
}

.fixture-table td {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.fixture-table tbody tr:hover {
    background: #f5f5f5;
}

.fixture-category-badge {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.fixture-category-badge.sab-a { background: #ff6b6b; }
.fixture-category-badge.sab-b { background: #4ecdc4; }
.fixture-category-badge.sab-c { background: #45b7d1; }
.fixture-category-badge.sab-d { background: #96ceb4; }
.fixture-category-badge.sab-e { background: #feca57; }
.fixture-category-badge.sab-f { background: #ff9ff3; }

.fixture-details {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
