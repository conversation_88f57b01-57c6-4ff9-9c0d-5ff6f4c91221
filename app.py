from flask import Flask, render_template, request, jsonify
import requests
import urllib3
from requests.exceptions import RequestException
import os
from datetime import datetime
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re
from html import unescape
from office365.runtime.auth.user_credential import UserCredential
from office365.sharepoint.client_context import ClientContext
from office365.sharepoint.listitems.listitem import ListItem
from test import get_sharepoint_data
import json
import pandas as pd
from utils.content_parser import content_parser
from utils.prompt_generator import prompt_generator
from utils.linespec_analyzer import LinespecAnalyzer

# 注册中文字体
FONT_PATH = 'C:/Windows/Fonts/simsun.ttf'  # Windows系统宋体路径
try:
    pdfmetrics.registerFont(TTFont('SimSun', FONT_PATH))
except:
    # 如果默认路径不存在，尝试其他可能的路径
    alternate_paths = [
        'C:/Windows/Fonts/SimSun.ttc',
        'C:/Windows/Fonts/SimSun-ExtB.ttf',
        './simsun.ttf',  # 当前目录
    ]
    font_registered = False
    for path in alternate_paths:
        try:
            if os.path.exists(path):
                pdfmetrics.registerFont(TTFont('SimSun', path))
                font_registered = True
                break
        except:
            continue
    
    if not font_registered:
        print("警告：无法加载中文字体，PDF中的中文可能无法正确显示")

# 禁用SSL警告（仅测试环境使用）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

app = Flask(__name__)

# API配置
API_URL = 'https://ach-alvchat.ap.autoliv.int/api/v1/chat-messages'
HEADERS = {
    'Authorization': 'Bearer eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTA2Nzc2NTksImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWUiOiJqaWFzaGVuLmNhaSIsImFwcF9pZCI6IkFCSVMiLCJhcHBTZWNyZXQiOiJObUkxT0VSaU1GbFZhSEJWV0ZrMGMwaDZUVnBqWlZGWmNHY3pkbXRtUTFFPSIsImlhdCI6MTc1MDMxNzY1OSwibmJmIjoxNzUwMzE3NjU5fQ.sRjbb5SGeu_R3i2YzZlFDH8qLIuVodw9uT2_MLuMeGg',
    'X-API-Key': 'b2c4eec9-be01-46f6-a501-f313a348d979'
}

# SharePoint配置
SHAREPOINT_SITE = "https://autoliv.sharepoint.com/sites/msteams_a5f551"
SHAREPOINT_LIST = "Airbag New Project"
SHAREPOINT_CREDENTIALS = UserCredential("<EMAIL>", "autoliv0708")

# 创建保存目录
SAVE_DIR = 'saved_responses'
if not os.path.exists(SAVE_DIR):
    os.makedirs(SAVE_DIR)

# 初始化Linespec分析器，使用真实的SAB数据
linespec_analyzer = LinespecAnalyzer("Linespec-data-SAB-V0.xlsx")

def safe_filename(s):
    return str(s).replace('/', '_').replace('\\', '_')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/debug')
def debug_step_issue():
    return render_template('debug_step_issue.html')

@app.route('/js-test')
def js_test():
    return render_template('js_test.html')

@app.route('/test-fixes')
def test_fixes():
    return render_template('test_station_fixes.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        question = request.json.get('question', '').strip()
        
        if not question:
            return jsonify({'error': '问题不能为空'}), 400

        response = requests.post(
            API_URL,
            headers=HEADERS,
            json={
                'query': question,
                'chatId': 9386,
                'stream': False
            },
            verify=False,
            timeout=90
        )

        if response.status_code == 200:
            answer = response.json().get('answer', '')
            # 将answer中的\n替换为实际的换行符
            formatted_answer = answer.replace('\\n', '\n')

            # 使用内容解析器分割内容
            try:
                print(f"[DEBUG] AI原始回答长度: {len(formatted_answer)}")
                print(f"[DEBUG] AI回答前500字符: {formatted_answer[:500]}")

                parsed_content = content_parser.parse_content(formatted_answer)

                print(f"[DEBUG] 解析结果 - 工艺部分长度: {len(parsed_content.get('process_content', ''))}")
                print(f"[DEBUG] 解析结果 - 设备部分长度: {len(parsed_content.get('equipment_content', ''))}")
                print(f"[DEBUG] 解析结果 - 其他内容长度: {len(parsed_content.get('other_content', ''))}")

                # 提取工站信息
                process_stations = []
                equipment_stations = []

                if parsed_content.get('process_content'):
                    print(f"[DEBUG] 开始提取工艺工站信息")
                    process_stations = content_parser.extract_station_info(parsed_content['process_content'])
                    print(f"[DEBUG] 提取到 {len(process_stations)} 个工艺工站")

                if parsed_content.get('equipment_content'):
                    print(f"[DEBUG] 开始提取设备工站信息")
                    equipment_stations = content_parser.extract_equipment_station_info(parsed_content['equipment_content'])
                    print(f"[DEBUG] 提取到 {len(equipment_stations)} 个设备工站")

                return jsonify({
                    'answer': formatted_answer,
                    'parsed_content': parsed_content,
                    'process_stations': process_stations,
                    'equipment_stations': equipment_stations
                })
            except Exception as parse_error:
                print(f"内容解析错误: {parse_error}")
                # 如果解析失败，返回原始内容
                return jsonify({'answer': formatted_answer})
        else:
            return jsonify({
                'error': f'API响应异常（状态码：{response.status_code}）',
                'details': response.text[:200]
            }), response.status_code

    except RequestException as e:
        return jsonify({'error': f'网络错误：{str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'系统错误：{str(e)}'}), 500

@app.route('/save', methods=['POST'])
def save_response():
    try:
        content = request.json.get('content', '').strip()
        project_info = request.json.get('project_info', {})  # 获取项目信息
        
        if not content:
            return jsonify({'error': '内容不能为空'}), 400

        # 1. 获取各项内容
        factory = safe_filename(project_info.get('production-site', '工厂'))
        # 兼容 lineNumber、Line-number、line-number 三种写法
        line_no = safe_filename(
            project_info.get('lineNumber') or
            project_info.get('Line-number') or
            project_info.get('line-number') or
            '线号'
        )
        project_name = safe_filename(project_info.get('title', '项目'))
        module_name = safe_filename(project_info.get('product-family', '模块'))

        # 2. 版本号自动递增
        base_filename = f"{factory}_{line_no}_{project_name}_{module_name}  Line spec"
        exist_files = [f for f in os.listdir(SAVE_DIR) if f.startswith(base_filename)]
        version = len(exist_files) + 1
        version_str = f"V{version:02d}"

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{base_filename}__{version_str}+{timestamp}.pdf"
        filepath = os.path.join(SAVE_DIR, filename)

        doc = SimpleDocTemplate(
            filepath,
            pagesize=letter,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )

        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontName='SimSun',
            fontSize=16
        )
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName='SimSun',
            fontSize=12,
            leading=14,
            spaceBefore=10,
            spaceAfter=10
        )

        story = []
        
        # 添加标题（页面标题）
        title = Paragraph(f"{factory}_{line_no}_{project_name}_{module_name}  Line spec__{version_str}+{timestamp}", title_style)
        story.append(title)
        story.append(Spacer(1, 20))

        # 添加项目信息（只显示一次所有字段，无重复）
        if project_info:
            project_text = f"""项目信息
项目名称: {project_info.get('title', '')}
生产工厂: {project_info.get('production-site', '')}
产品系列: {project_info.get('product-family', '')}
生产线类型: {project_info.get('line-type', '')}
线号: {project_info.get('Line-number') or project_info.get('lineNumber') or project_info.get('line-number', '')}
APP负责人: {project_info.get('APP-responsible') or project_info.get('app-responsible', '')}
AEP负责人: {project_info.get('AEP-responsible') or project_info.get('aep-responsible', '')}
产线预验收日期: {project_info.get('pre-acceptance-date') or project_info.get('preAcceptanceDate', '')}
离线调试完成日期: {project_info.get('offline-debug-date') or project_info.get('offlineDebugDate', '')}
"""
            project_para = Paragraph(project_text.replace('\n', '<br/>'), normal_style)
            story.append(project_para)
            story.append(Spacer(1, 20))

        # 处理对话内容
        def decode_unicode_escapes(text):
            return re.sub(r'\\u([0-9a-fA-F]{4})', lambda m: chr(int(m.group(1), 16)), text)

        def clean_html(text):
            text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
            text = re.sub(r'<[^>]+>', '', text)
            text = unescape(text)
            return text

        content = decode_unicode_escapes(content)
        content = clean_html(content)
        content = (content
                  .replace('&', '&amp;')
                  .replace('<', '&lt;')
                  .replace('>', '&gt;'))
        
        para = Paragraph(content.replace('\n', '<br/>'), normal_style)
        story.append(para)

        doc.build(story)

        return jsonify({
            'message': '保存成功',
            'filename': filename
        })
    except Exception as e:
        return jsonify({'error': f'保存失败：{str(e)}'}), 500

def get_sharepoint_projects():
    try:
        print("开始从SharePoint获取项目数据...")
        items = get_sharepoint_data()
        
        if not items:
            print("警告：从SharePoint获取的项目列表为空")
            return []
            
        def format_date(date_str):
            if not date_str:
                return ""
            # 移除T和Z，只保留日期部分
            return date_str.split('T')[0] if 'T' in date_str else date_str
            
        projects = [{
            "id": idx,
            "title": item["project-name"],
            "production-site": item["production-site"],
            "product-family": item["product-family"],
            "customer": item["customer"],
            "SOP-date": format_date(item["SOP-date"]),
            "APP-responsible": item["APP-responsible"],
            "AEP-responsible": item["AEP-responsible"],
            "Line-number": item["Line-number"],
            "line-type": item["line-type"],
            "pre-acceptance-date": format_date(item["pre-acceptance-date"]),   
            "offline-debug-date": format_date(item["offline-debug-date"])
        } for idx, item in enumerate(items)]
        
        # 打印调试信息
        print(f"获取到 {len(projects)} 个项目")
        if projects:
            print(f"第一个项目示例: {projects[0]}")
            print(f"最后一个项目示例: {projects[-1]}")
        
        return projects
    except Exception as e:
        print(f"获取SharePoint项目列表失败：{str(e)}")
        import traceback
        traceback.print_exc()  # 打印完整的错误堆栈
        return []

@app.route('/projects', methods=['GET'])
def get_projects():
    """API端点：获取项目列表"""
    try:
        print("处理/projects请求...")
        projects = get_sharepoint_projects()
        # 打印调试信息
        print(f"返回 {len(projects)} 个项目到前端")
        
        # 检查项目名称是否都有效
        invalid_projects = [p for p in projects if not p.get('title')]
        if invalid_projects:
            print(f"警告: 发现 {len(invalid_projects)} 个无效项目（没有标题）")
        
        # 确保所有项目都有有效的ID和标题
        valid_projects = [p for p in projects if p.get('id') is not None and p.get('title')]
        if len(valid_projects) < len(projects):
            print(f"警告: 过滤掉了 {len(projects) - len(valid_projects)} 个无效项目")
            projects = valid_projects
        
        return jsonify(projects)
    except Exception as e:
        print(f"获取项目列表失败：{str(e)}")
        import traceback
        traceback.print_exc()  # 打印完整的错误堆栈
        return jsonify({'error': f'获取项目列表失败：{str(e)}'}), 500

@app.route('/get_parts', methods=['POST'])
def get_parts():
    """根据产品族和模板类型返回匹配的零件"""
    try:
        data = request.json
        product_family = data.get("product_family")
        template_type = data.get("template_type")

        if not product_family or not template_type:
            return jsonify({"error": "缺少必要参数"}), 400

        # 加载零件与模板映射关系
        mapping_file = os.path.join(os.path.dirname(__file__), 'config', 'template_parts_mapping.json')
        with open(mapping_file, 'r', encoding='utf-8') as f:
            template_parts_mapping = json.load(f)

        # 获取匹配的零件
        parts = template_parts_mapping.get(product_family, {}).get(template_type, [])
        return jsonify({"parts": [{"name": part} for part in parts]})
    except Exception as e:
        return jsonify({"error": f"获取零件失败：{str(e)}"}), 500

@app.route('/generate_prompt', methods=['POST'])
def generate_improved_prompt():
    """生成改进的标准格式prompt"""
    try:
        data = request.json
        product_type = data.get("product_type", "")
        parts_list = data.get("parts_list", [])
        project_requirements = data.get("project_requirements", "")

        if not product_type or not parts_list:
            return jsonify({"error": "缺少必要参数：产品类型和零件列表"}), 400

        # 生成改进的prompt
        improved_prompt = prompt_generator.generate_linespec_prompt(
            product_type=product_type,
            parts_list=parts_list,
            project_requirements=project_requirements
        )

        return jsonify({
            "prompt": improved_prompt,
            "product_type": product_type,
            "parts_count": len(parts_list)
        })
    except Exception as e:
        return jsonify({"error": f"生成prompt失败：{str(e)}"}), 500

@app.route('/analyze_linespec', methods=['POST'])
def analyze_linespec():
    """智能分析技术规范"""
    try:
        data = request.json
        product_family = data.get("product_family", "")
        components = data.get("components", [])
        project_requirements = data.get("project_requirements", "")

        if not product_family or not components:
            return jsonify({"error": "缺少必要参数：产品族和零件列表"}), 400

        # 使用分析器进行智能匹配
        result = linespec_analyzer.generate_linespec_response(
            product_family=product_family,
            components=components,
            project_requirements=project_requirements
        )

        return jsonify(result)
    except Exception as e:
        return jsonify({"error": f"分析失败：{str(e)}"}), 500

@app.route('/get_process_types', methods=['GET'])
def get_process_types():
    """获取所有工艺类型"""
    try:
        process_types = linespec_analyzer.get_all_process_types()
        return jsonify({"process_types": process_types})
    except Exception as e:
        return jsonify({"error": f"获取工艺类型失败：{str(e)}"}), 500

@app.route('/get_components/<process_type>', methods=['GET'])
def get_components_by_process_type(process_type):
    """获取指定工艺类型的零件列表"""
    try:
        components = linespec_analyzer.get_components_for_process_type(process_type)
        return jsonify({"components": components})
    except Exception as e:
        return jsonify({"error": f"获取零件列表失败：{str(e)}"}), 500

@app.route('/api/fixture_data')
def get_fixture_data():
    """获取夹具清单数据"""
    try:
        # 获取所有夹具数据
        all_fixture_data = linespec_analyzer.get_all_fixture_data()

        return jsonify({
            'success': True,
            'data': all_fixture_data,
            'message': '夹具数据获取成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取夹具数据失败'
        }), 500

@app.route('/api/fixture_data/<process_type>')
def get_fixture_data_by_type(process_type):
    """根据工艺类型获取夹具清单"""
    try:
        # 获取指定类型的夹具数据
        fixture_list = linespec_analyzer.get_fixture_list(process_type)

        return jsonify({
            'success': True,
            'data': fixture_list,
            'process_type': process_type,
            'message': f'{process_type}夹具数据获取成功'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'message': f'获取{process_type}夹具数据失败'
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)