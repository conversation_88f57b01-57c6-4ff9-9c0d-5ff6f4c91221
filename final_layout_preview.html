
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终布局预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .station-preview { 
            border: 1px solid #e0e0e0; 
            border-radius: 6px; 
            padding: 0.8rem; 
            margin-bottom: 1rem; 
            background: #fafbfc; 
        }
        .station-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.6rem; 
            padding-bottom: 0.4rem; 
            border-bottom: 1px solid #e0e0e0; 
        }
        .station-title-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .collapse-btn {
            background: none; 
            border: none; 
            color: #1a73e8; 
            cursor: pointer; 
            font-size: 0.8rem; 
            padding: 0; 
            width: 16px; 
            height: 16px; 
            display: flex; 
            align-items: center; 
            justify-content: center;
        }
        .station-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 1rem; 
            font-weight: 600; 
            cursor: pointer; 
            padding: 2px 4px; 
            border-radius: 3px; 
            transition: background-color 0.2s;
        }
        .station-title:hover {
            background-color: #f0f7ff;
        }
        .delete-btn {
            background: #ff4d4f; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            padding: 2px 6px; 
            cursor: pointer; 
            font-size: 0.7rem;
        }
        .step-preview {
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white;
        }
        .step-header {
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem;
        }
        .step-title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .step-title {
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500;
        }
        .operator-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        .operator-label {
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap;
        }
        .operator-select {
            width: 80px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 28px;
        }
        .step-delete-btn {
            background: #ff7875; 
            color: white; 
            border: none; 
            border-radius: 2px; 
            padding: 1px 4px; 
            cursor: pointer; 
            font-size: 0.65rem;
        }
        .content-section {
            margin-bottom: 0.4rem;
        }
        .content-section:last-child {
            margin-bottom: 0;
        }
        .content-label {
            display: block; 
            margin-bottom: 0.2rem; 
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555;
        }
        .content-textarea {
            width: 100%; 
            height: 35px; 
            padding: 0.3rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            resize: none; 
            font-size: 0.8rem; 
            line-height: 1.3;
        }
    </style>
    <script>
        function simulateDoubleClick(element) {
            const currentText = element.textContent.trim();
            const match = currentText.match(/^ST(\d+)\s*-\s*(.+)$/);
            if (!match) return;
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentText;
            input.style.cssText = `
                margin: 0;
                color: #1a73e8;
                font-size: 1rem;
                font-weight: 600;
                background: white;
                border: 2px solid #1a73e8;
                border-radius: 3px;
                padding: 2px 4px;
                width: 300px;
                font-family: inherit;
            `;
            
            element.style.display = 'none';
            element.parentNode.insertBefore(input, element);
            
            input.focus();
            input.select();
            
            const saveTitle = () => {
                const newValue = input.value.trim();
                const newMatch = newValue.match(/^ST(\d+)\s*-\s*(.+)$/);
                if (!newMatch) {
                    alert('请使用正确的格式：ST[数字] - [工站名称]');
                    input.focus();
                    return;
                }
                
                element.textContent = newValue;
                input.remove();
                element.style.display = '';
            };
            
            const cancelEdit = () => {
                input.remove();
                element.style.display = '';
            };
            
            input.addEventListener('blur', saveTitle);
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTitle();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });
        }
    </script>
</head>
<body>
    <div class="preview-container">
        <h2>最终布局预览</h2>
        <p>工站标题支持双击编辑，保留三个固定标题（工艺过程描述、产品特性要求、过程防错要求）</p>
        
        <div class="station-preview">
            <!-- 工站标题行：可双击编辑 -->
            <div class="station-header">
                <div class="station-title-group">
                    <button class="collapse-btn" title="折叠/展开">
                        <span>▼</span>
                    </button>
                    <h4 class="station-title" 
                        ondblclick="simulateDoubleClick(this)"
                        title="双击编辑工站标题">
                        ST10 - Pre-assembly
                    </h4>
                </div>
                <button class="delete-btn">删除</button>
            </div>
            
            <!-- 工艺步骤 -->
            <div class="step-preview">
                <!-- 步骤标题行 -->
                <div class="step-header">
                    <div class="step-title-group">
                        <h6 class="step-title">步骤 1</h6>
                        <div class="operator-group">
                            <label class="operator-label">人or设备:</label>
                            <select class="operator-select">
                                <option>人</option>
                            </select>
                        </div>
                    </div>
                    <button class="step-delete-btn">×</button>
                </div>
                
                <!-- 工艺过程描述 -->
                <div class="content-section">
                    <label class="content-label">工艺过程描述:</label>
                    <textarea class="content-textarea" placeholder="请输入工艺过程描述">人工检查包装材料完整性</textarea>
                </div>
                
                <!-- 产品特性要求 -->
                <div class="content-section">
                    <label class="content-label">产品特性要求:</label>
                    <textarea class="content-textarea" placeholder="请输入产品特性要求">包装材料无破损，标签清晰</textarea>
                </div>
                
                <!-- 过程防错要求 -->
                <div class="content-section">
                    <label class="content-label">过程防错要求:</label>
                    <textarea class="content-textarea" placeholder="请输入过程防错要求">目视检查，发现问题立即停止</textarea>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>最终布局特点：</h3>
            <ul>
                <li>✅ <strong>工站标题</strong>：支持双击编辑，消除重复的工站号和工站名称输入框</li>
                <li>✅ <strong>步骤标题</strong>："人or设备"在步骤号右边</li>
                <li>✅ <strong>三个固定标题</strong>：工艺过程描述、产品特性要求、过程防错要求（不可编辑）</li>
                <li>✅ <strong>内容区域</strong>：每个标题下方有对应的文本框用于输入内容</li>
                <li>✅ <strong>布局清晰</strong>：没有重复内容，层次分明</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px;">
            <h3>布局结构：</h3>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ ▼ ST10 - Pre-assembly (双击编辑)               删除 │  ← 可编辑工站标题
├─────────────────────────────────────────────────────────┤
│ 步骤 1    人or设备: [选择器]                      × │  ← 步骤标题行
├─────────────────────────────────────────────────────────┤
│ 工艺过程描述:                                         │  ← 固定标题
│ [──────────────文本框──────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求:                                         │  ← 固定标题
│ [──────────────文本框──────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求:                                         │  ← 固定标题
│ [──────────────文本框──────────────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
    </div>
</body>
</html>
        