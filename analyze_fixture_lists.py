"""
分析SAB夹具清单数据结构
"""
import pandas as pd

def analyze_fixture_lists():
    """分析所有SAB类别的夹具清单"""
    
    print("=== SAB夹具清单数据分析 ===\n")
    
    # 获取所有夹具清单工作表
    xl = pd.ExcelFile('Linespec-data-SAB-V0.xlsx')
    fixture_sheets = [sheet for sheet in xl.sheet_names if '夹具清单' in sheet]
    
    fixture_data = {}
    
    for sheet_name in fixture_sheets:
        print(f"📋 分析 {sheet_name}")
        
        try:
            df = pd.read_excel('Linespec-data-SAB-V0.xlsx', sheet_name=sheet_name)
            
            # 基本信息
            print(f"   行数: {len(df)}")
            print(f"   列数: {len(df.columns)}")
            print(f"   列名: {list(df.columns)}")
            
            # 显示前几行非空数据
            non_empty_df = df.dropna(how='all')
            if not non_empty_df.empty:
                print("   示例数据:")
                for i, (idx, row) in enumerate(non_empty_df.head(3).iterrows()):
                    print(f"     行{idx+1}:")
                    for col in df.columns:
                        if pd.notna(row[col]) and str(row[col]).strip():
                            print(f"       {col}: {row[col]}")
                    if i < 2:  # 只显示前3行
                        print()
            
            # 保存数据结构
            sab_type = sheet_name.replace('设备夹具清单', '').replace('-', '')
            fixture_data[sab_type] = {
                'sheet_name': sheet_name,
                'columns': list(df.columns),
                'row_count': len(df),
                'sample_data': non_empty_df.head(5).to_dict('records') if not non_empty_df.empty else []
            }
            
        except Exception as e:
            print(f"   ❌ 读取失败: {str(e)}")
        
        print()
    
    return fixture_data

def analyze_fixture_categories(fixture_data):
    """分析夹具类别和结构"""
    
    print("=== 夹具数据结构分析 ===\n")
    
    # 分析共同的列结构
    all_columns = set()
    for sab_type, data in fixture_data.items():
        all_columns.update(data['columns'])
    
    print("所有夹具清单的列名:")
    for col in sorted(all_columns):
        print(f"  - {col}")
    
    print()
    
    # 分析每个SAB类别的特点
    for sab_type, data in fixture_data.items():
        print(f"📊 {sab_type} 夹具特点:")
        print(f"   工作表: {data['sheet_name']}")
        print(f"   数据行数: {data['row_count']}")
        print(f"   列数: {len(data['columns'])}")
        
        # 分析数据内容
        if data['sample_data']:
            print("   主要夹具类型:")
            fixture_types = set()
            equipment_types = set()
            
            for row in data['sample_data']:
                # 查找夹具相关字段
                for col, value in row.items():
                    if pd.notna(value) and str(value).strip():
                        if '夹具' in col:
                            fixture_types.add(str(value))
                        elif '设备' in col:
                            equipment_types.add(str(value))
            
            if fixture_types:
                for ft in list(fixture_types)[:5]:  # 显示前5个
                    print(f"     - {ft}")
            
            if equipment_types:
                print("   相关设备:")
                for et in list(equipment_types)[:3]:  # 显示前3个
                    print(f"     - {et}")
        
        print()

def generate_fixture_ui_design(fixture_data):
    """生成夹具清单UI设计"""
    
    print("=== 夹具清单UI设计方案 ===\n")
    
    # 设计原则
    print("🎨 设计原则:")
    print("1. 按SAB类别分组显示夹具清单")
    print("2. 支持筛选和搜索功能")
    print("3. 表格形式展示详细信息")
    print("4. 支持导出和打印")
    print("5. 与工艺工站关联显示")
    print()
    
    # UI结构设计
    ui_structure = {
        'header': {
            'title': '🔧 夹具清单',
            'description': '根据选择的SAB工艺类型显示对应的夹具和设备清单',
            'controls': ['筛选器', '搜索框', '导出按钮']
        },
        'filter_section': {
            'sab_type_filter': 'SAB类别筛选 (A/B/C/D/E/F)',
            'equipment_type_filter': '设备类型筛选',
            'fixture_type_filter': '夹具类型筛选'
        },
        'content_section': {
            'fixture_table': '夹具清单表格',
            'equipment_table': '设备清单表格',
            'summary_cards': '统计卡片'
        },
        'action_section': {
            'export_options': ['导出Excel', '导出PDF', '打印清单'],
            'management_options': ['添加夹具', '编辑信息', '删除项目']
        }
    }
    
    print("📋 UI结构设计:")
    for section, details in ui_structure.items():
        print(f"  {section}:")
        if isinstance(details, dict):
            for key, value in details.items():
                print(f"    - {key}: {value}")
        elif isinstance(details, list):
            for item in details:
                print(f"    - {item}")
        else:
            print(f"    - {details}")
        print()
    
    # 生成HTML模板
    html_template = generate_fixture_html_template(fixture_data)
    
    return ui_structure, html_template

def generate_fixture_html_template(fixture_data):
    """生成夹具清单HTML模板"""
    
    html = '''
<!-- 夹具清单页面 -->
<div class="preview-page" id="page-fixture" style="display:none;">
    <div class="preview-header">
        <h2>🔧 夹具清单</h2>
        <p>根据工艺类型显示对应的夹具和设备清单</p>
    </div>
    
    <div class="preview-content">
        <!-- 筛选控制区域 -->
        <div class="fixture-controls">
            <div class="control-group">
                <label>SAB类别:</label>
                <select id="sab-type-filter" onchange="filterFixtures()">
                    <option value="">全部类别</option>
'''
    
    # 添加SAB类别选项
    for sab_type in fixture_data.keys():
        html += f'                    <option value="{sab_type}">{sab_type}</option>\n'
    
    html += '''
                </select>
            </div>
            
            <div class="control-group">
                <label>搜索:</label>
                <input type="text" id="fixture-search" placeholder="搜索夹具或设备名称..." onkeyup="searchFixtures()">
            </div>
            
            <div class="control-group">
                <button onclick="exportFixtureList()" class="btn primary">📄 导出清单</button>
                <button onclick="printFixtureList()" class="btn">🖨️ 打印</button>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="fixture-summary">
            <div class="summary-card">
                <div class="summary-number" id="total-fixtures">0</div>
                <div class="summary-label">夹具总数</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="total-equipment">0</div>
                <div class="summary-label">设备总数</div>
            </div>
            <div class="summary-card">
                <div class="summary-number" id="active-categories">0</div>
                <div class="summary-label">活跃类别</div>
            </div>
        </div>
        
        <!-- 夹具清单表格 -->
        <div class="fixture-table-container">
            <table class="fixture-table" id="fixture-table">
                <thead>
                    <tr>
                        <th>SAB类别</th>
                        <th>工站</th>
                        <th>夹具名称</th>
                        <th>设备名称</th>
                        <th>机械要求</th>
                        <th>电气要求</th>
                        <th>防错要求</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="fixture-table-body">
                    <!-- 夹具数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
        
        <!-- 详细信息面板 -->
        <div class="fixture-details" id="fixture-details" style="display:none;">
            <h3>夹具详细信息</h3>
            <div id="fixture-details-content">
                <!-- 详细信息内容 -->
            </div>
        </div>
    </div>
</div>
'''
    
    return html

def main():
    """主函数"""
    try:
        # 分析夹具清单数据
        fixture_data = analyze_fixture_lists()
        
        # 分析夹具类别
        analyze_fixture_categories(fixture_data)
        
        # 生成UI设计
        ui_structure, html_template = generate_fixture_ui_design(fixture_data)
        
        # 保存HTML模板
        with open('fixture_list_template.html', 'w', encoding='utf-8') as f:
            f.write(html_template)
        
        print("✅ 夹具清单HTML模板已保存到: fixture_list_template.html")
        
        # 保存分析结果
        import json
        with open('fixture_analysis_result.json', 'w', encoding='utf-8') as f:
            json.dump({
                'fixture_data': fixture_data,
                'ui_structure': ui_structure
            }, f, ensure_ascii=False, indent=2)
        
        print("✅ 分析结果已保存到: fixture_analysis_result.json")
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
