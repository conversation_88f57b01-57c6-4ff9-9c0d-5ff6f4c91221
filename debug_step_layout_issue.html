<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤布局问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-title {
            color: #1a73e8;
            margin-bottom: 15px;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
        }
        .debug-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #1557b0;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .layout-demo {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: #fafbfc;
        }
        .demo-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2 class="debug-title">步骤布局问题调试工具</h2>
        <p>此工具用于检查和修复工艺页面中步骤横向排列的问题。</p>
        
        <button class="debug-button" onclick="analyzeLayoutIssue()">分析布局问题</button>
        <button class="debug-button" onclick="checkContainerStyles()">检查容器样式</button>
        <button class="debug-button" onclick="fixVerticalLayout()">修复垂直布局</button>
        <button class="debug-button" onclick="createLayoutDemo()">创建布局演示</button>
        
        <div id="debug-output"></div>
    </div>

    <div class="debug-container" id="demo-container" style="display: none;">
        <h3 class="debug-title">布局演示</h3>
        <div id="demo-content"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }

        function analyzeLayoutIssue() {
            log('开始分析步骤布局问题...');
            
            // 检查步骤容器
            const stepsContainers = document.querySelectorAll('.process-steps-container');
            log(`找到 ${stepsContainers.length} 个步骤容器`);
            
            stepsContainers.forEach((container, index) => {
                const computedStyle = getComputedStyle(container);
                log(`步骤容器 ${index + 1}:`, 'info');
                log(`  display: ${computedStyle.display}`, 'info');
                log(`  flex-direction: ${computedStyle.flexDirection}`, 'info');
                log(`  width: ${computedStyle.width}`, 'info');
                
                // 检查容器内的步骤
                const steps = container.querySelectorAll('.process-step');
                log(`  包含 ${steps.length} 个步骤`, 'info');
                
                if (steps.length > 1) {
                    // 检查步骤是否横向排列
                    const firstStep = steps[0];
                    const secondStep = steps[1];
                    const firstRect = firstStep.getBoundingClientRect();
                    const secondRect = secondStep.getBoundingClientRect();
                    
                    const isHorizontal = Math.abs(firstRect.top - secondRect.top) < 10;
                    if (isHorizontal) {
                        log(`  ❌ 检测到步骤横向排列！`, 'error');
                        log(`    第一个步骤位置: top=${firstRect.top}, left=${firstRect.left}`, 'error');
                        log(`    第二个步骤位置: top=${secondRect.top}, left=${secondRect.left}`, 'error');
                    } else {
                        log(`  ✅ 步骤正确垂直排列`, 'success');
                    }
                }
                log('---', 'info');
            });
            
            // 检查工站容器
            const stationBlocks = document.querySelectorAll('.station-block');
            log(`找到 ${stationBlocks.length} 个工站容器`);
            
            stationBlocks.forEach((block, index) => {
                const computedStyle = getComputedStyle(block);
                log(`工站容器 ${index + 1}:`, 'info');
                log(`  display: ${computedStyle.display}`, 'info');
                log(`  flex-direction: ${computedStyle.flexDirection}`, 'info');
                log(`  width: ${computedStyle.width}`, 'info');
            });
        }

        function checkContainerStyles() {
            log('检查容器样式...');
            
            // 检查所有可能影响布局的元素
            const selectors = [
                '#stations-list',
                '.station-block',
                '.process-steps-container',
                '.process-step'
            ];
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                log(`${selector}: 找到 ${elements.length} 个元素`);
                
                elements.forEach((element, index) => {
                    const computedStyle = getComputedStyle(element);
                    const relevantStyles = {
                        display: computedStyle.display,
                        flexDirection: computedStyle.flexDirection,
                        flexWrap: computedStyle.flexWrap,
                        width: computedStyle.width,
                        float: computedStyle.float,
                        position: computedStyle.position
                    };
                    
                    log(`  元素 ${index + 1}: ${JSON.stringify(relevantStyles)}`, 'info');
                });
                log('---', 'info');
            });
        }

        function fixVerticalLayout() {
            log('开始修复垂直布局...');
            
            let fixedCount = 0;
            
            // 修复步骤容器
            const stepsContainers = document.querySelectorAll('.process-steps-container');
            stepsContainers.forEach((container, index) => {
                container.style.setProperty('display', 'flex', 'important');
                container.style.setProperty('flex-direction', 'column', 'important');
                container.style.setProperty('width', '100%', 'important');
                log(`修复步骤容器 ${index + 1}`, 'success');
                fixedCount++;
            });
            
            // 修复工站容器
            const stationBlocks = document.querySelectorAll('.station-block');
            stationBlocks.forEach((block, index) => {
                block.style.setProperty('display', 'block', 'important');
                block.style.setProperty('width', '100%', 'important');
                log(`修复工站容器 ${index + 1}`, 'success');
                fixedCount++;
            });
            
            // 修复步骤元素
            const allSteps = document.querySelectorAll('.process-step');
            allSteps.forEach((step, index) => {
                step.style.setProperty('display', 'block', 'important');
                step.style.setProperty('width', '100%', 'important');
                step.style.setProperty('box-sizing', 'border-box', 'important');
                step.style.setProperty('float', 'none', 'important');
                step.style.setProperty('clear', 'both', 'important');
                step.style.setProperty('flex', 'none', 'important');
                step.style.setProperty('margin-bottom', '0.5rem', 'important');
                log(`修复步骤元素 ${index + 1}`, 'success');
                fixedCount++;
            });
            
            log(`修复完成，共处理 ${fixedCount} 个元素`, 'success');
            
            // 重新检查
            setTimeout(() => {
                analyzeLayoutIssue();
            }, 100);
        }

        function createLayoutDemo() {
            log('创建布局演示...');
            
            const demoContainer = document.getElementById('demo-container');
            const demoContent = document.getElementById('demo-content');
            
            demoContainer.style.display = 'block';
            
            demoContent.innerHTML = `
                <div class="layout-demo">
                    <div class="demo-title">❌ 错误的横向布局（避免）</div>
                    <div style="display: flex; gap: 10px;">
                        <div style="border: 1px solid #e8e8e8; padding: 10px; border-radius: 4px; background: white; flex: 1;">
                            <h6 style="margin: 0; color: #1a73e8;">步骤 1</h6>
                            <div>工艺过程描述: 示例内容</div>
                            <div>产品特性要求: 示例内容</div>
                            <div>过程防错要求: 示例内容</div>
                        </div>
                        <div style="border: 1px solid #e8e8e8; padding: 10px; border-radius: 4px; background: white; flex: 1;">
                            <h6 style="margin: 0; color: #1a73e8;">步骤 2</h6>
                            <div>工艺过程描述: 示例内容</div>
                            <div>产品特性要求: 示例内容</div>
                            <div>过程防错要求: 示例内容</div>
                        </div>
                    </div>
                </div>
                
                <div class="layout-demo">
                    <div class="demo-title">✅ 正确的垂直布局（期望）</div>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <div style="border: 1px solid #e8e8e8; padding: 10px; border-radius: 4px; background: white; width: 100%;">
                            <h6 style="margin: 0; color: #1a73e8;">步骤 1</h6>
                            <div>工艺过程描述: 示例内容</div>
                            <div>产品特性要求: 示例内容</div>
                            <div>过程防错要求: 示例内容</div>
                        </div>
                        <div style="border: 1px solid #e8e8e8; padding: 10px; border-radius: 4px; background: white; width: 100%;">
                            <h6 style="margin: 0; color: #1a73e8;">步骤 2</h6>
                            <div>工艺过程描述: 示例内容</div>
                            <div>产品特性要求: 示例内容</div>
                            <div>过程防错要求: 示例内容</div>
                        </div>
                    </div>
                </div>
            `;
            
            log('布局演示已创建', 'success');
        }

        // 页面加载完成后自动运行分析
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('页面加载完成，开始自动分析...', 'info');
                analyzeLayoutIssue();
            }, 1000);
        });
    </script>
</body>
</html>
