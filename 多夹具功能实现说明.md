# 多夹具功能实现说明

## 功能概述

为设备工站实现了多夹具管理功能，每个工站可以添加多个夹具，每个夹具都有独立的文件上传区域和要求输入区域。

## 主要特性

### 1. 多夹具管理
- ✅ **动态添加夹具**：每个工站可以添加多个夹具
- ✅ **独立夹具块**：每个夹具都有独立的配置区域
- ✅ **夹具名称编辑**：支持双击编辑夹具名称
- ✅ **夹具删除**：支持删除单个夹具（至少保留一个）

### 2. 独立文件管理
- ✅ **每夹具独立文件上传**：每个夹具都有自己的文件上传区域
- ✅ **多文件支持**：支持JPG、PNG、PDF、DOC、DOCX格式
- ✅ **文件操作**：预览、下载、删除、清空功能
- ✅ **文件隔离**：不同夹具的文件完全独立管理

### 3. 独立要求配置
- ✅ **机械要求**：每个夹具独立的机械要求输入
- ✅ **电气要求**：每个夹具独立的电气要求输入
- ✅ **防错及点检要求**：每个夹具独立的防错要求输入

## 技术实现

### 1. HTML结构重构 (`static/js/station_generator.js`)

#### 1.1 夹具部分结构
```html
<!-- Fixture部分 -->
<div style="margin-bottom: 0.5rem; padding: 1rem; border: 1px solid #ffd6cc; border-radius: 6px; background: #fff7e6;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h5>二、夹具要求 (Fixture)</h5>
        <button onclick="addFixtureBlock(stationIndex)">➕ 添加夹具</button>
    </div>
    
    <!-- 夹具列表容器 -->
    <div id="fixtures-container-${stationIndex}">
        ${this.generateFixtureBlocks(details, stationIndex)}
    </div>
</div>
```

#### 1.2 单个夹具块结构
```html
<div class="fixture-block" data-fixture-index="${fixtureIndex}">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h6 ondblclick="makeFixtureNameEditable(...)">🔧 夹具名称</h6>
        <div>
            <button onclick="addFixtureBlockAfter(...)">➕ 添加</button>
            <button onclick="deleteFixtureBlock(...)">🗑️ 删除</button>
        </div>
    </div>
    
    <!-- 夹具文件上传区域 -->
    <div>
        <input type="file" id="fixture-files-input-${stationIndex}-${fixtureIndex}" multiple>
        <button onclick="上传文件">📁 上传文件</button>
        <button onclick="清空所有">🗑️ 清空所有</button>
        <div id="fixture-files-preview-${stationIndex}-${fixtureIndex}">
            <!-- 文件预览区域 -->
        </div>
    </div>
    
    <!-- 夹具要求输入 -->
    <textarea onchange="updateFixtureInfo(stationIndex, fixtureIndex, 'mechanical_requirements', this.value)">机械要求</textarea>
    <textarea onchange="updateFixtureInfo(stationIndex, fixtureIndex, 'electrical_requirements', this.value)">电气要求</textarea>
    <textarea onchange="updateFixtureInfo(stationIndex, fixtureIndex, 'error_prevention_requirements', this.value)">防错要求</textarea>
</div>
```

### 2. JavaScript函数实现 (`templates/index.html`)

#### 2.1 多夹具管理函数
```javascript
// 添加新夹具块
function addFixtureBlock(stationIndex) { ... }

// 在指定夹具后添加新夹具
function addFixtureBlockAfter(stationIndex, afterIndex) { ... }

// 删除夹具块
function deleteFixtureBlock(stationIndex, fixtureIndex) { ... }

// 重新索引夹具块
function reindexFixtureBlocks(stationIndex) { ... }

// 更新夹具块的ID和事件
function updateFixtureBlockIds(block, stationIndex, newIndex) { ... }
```

#### 2.2 夹具数据管理函数
```javascript
// 更新夹具信息
function updateFixtureInfo(stationIndex, fixtureIndex, field, value) { ... }

// 更新设备工站的夹具数据
function updateEquipmentStationFixtures(stationIndex) { ... }

// 编辑夹具名称
function makeFixtureNameEditable(stationIndex, fixtureIndex, element) { ... }
```

#### 2.3 文件管理函数（更新支持多夹具）
```javascript
// 处理夹具文件上传（支持多夹具）
function handleFixtureFilesUpload(event, stationIndex, fixtureIndex = 0) { ... }

// 更新夹具文件预览
function updateFixtureFilesPreview(stationIndex, fixtureIndex = 0) { ... }

// 预览夹具文件
function previewFixtureFile(stationIndex, fixtureIndex, fileIndex) { ... }

// 下载夹具文件
function downloadFixtureFile(stationIndex, fixtureIndex, fileIndex) { ... }

// 删除单个夹具文件
function deleteFixtureFile(stationIndex, fixtureIndex, fileIndex) { ... }

// 清空所有夹具文件
function clearAllFixtureFiles(stationIndex, fixtureIndex) { ... }
```

### 3. 数据结构重构

#### 3.1 新的数据结构
```javascript
// 全局夹具文件数据（新格式）
window.fixtureFilesData = {
    [stationIndex]: {
        [fixtureIndex]: [
            {
                fileData: "data:image/jpeg;base64,...",
                fileName: "夹具图1.jpg",
                fileSize: 1024000,
                fileType: "image/jpeg",
                uploadTime: "2024-01-01T12:00:00.000Z"
            },
            // ... 更多文件
        ],
        // ... 更多夹具
    },
    // ... 更多工站
};

// 设备工站数据结构
equipment_details: {
    fixtures: [
        {
            fixture_name: "夹具1",
            mechanical_requirements: "机械要求内容",
            electrical_requirements: "电气要求内容",
            error_prevention_requirements: "防错要求内容"
        },
        {
            fixture_name: "夹具2",
            mechanical_requirements: "机械要求内容",
            electrical_requirements: "电气要求内容",
            error_prevention_requirements: "防错要求内容"
        }
        // ... 更多夹具
    ],
    fixture_files_data: {
        0: [文件数组],  // 夹具1的文件
        1: [文件数组],  // 夹具2的文件
        // ... 更多夹具的文件
    }
}
```

#### 3.2 向后兼容处理
```javascript
// 数据恢复时的兼容性处理
if (Array.isArray(details.fixture_files_data)) {
    // 旧格式：转换为新格式
    window.fixtureFilesData[stationIndex] = {
        0: details.fixture_files_data
    };
} else if (typeof details.fixture_files_data === 'object') {
    // 新格式：直接使用
    window.fixtureFilesData[stationIndex] = details.fixture_files_data;
}
```

### 4. 数据管理 (`static/js/station_manager.js`)

#### 4.1 数据保存
```javascript
function saveEquipmentStationImageAndParameters(stationIndex) {
    // 保存夹具文件数据（支持多夹具）
    const fixtureFilesData = window.fixtureFilesData?.[stationIndex];
    if (fixtureFilesData && Object.keys(fixtureFilesData).length > 0) {
        details.fixture_files_data = fixtureFilesData;
        
        // 计算总文件数量
        let totalFiles = 0;
        Object.values(fixtureFilesData).forEach(fixtureFiles => {
            if (Array.isArray(fixtureFiles)) {
                totalFiles += fixtureFiles.length;
            }
        });
        details.fixture_files_count = totalFiles;
    }
}
```

#### 4.2 数据恢复
```javascript
function initializeEquipmentStationImageAndParameters(stationIndex) {
    // 恢复夹具文件数据（支持多夹具）
    if (details.fixture_files_data) {
        // 检查数据格式并转换
        // 为每个夹具更新预览
        Object.keys(window.fixtureFilesData[stationIndex]).forEach(fixtureIndex => {
            updateFixtureFilesPreview(stationIndex, parseInt(fixtureIndex));
        });
    }
}
```

## 用户界面设计

### 1. 夹具管理区域
- **标题栏**：显示"二、夹具要求 (Fixture)"和"➕ 添加夹具"按钮
- **夹具列表**：动态显示所有夹具块
- **橙色主题**：与夹具部分保持一致的视觉风格

### 2. 单个夹具块
- **夹具标题**：🔧 夹具名称（支持双击编辑）
- **操作按钮**：➕ 添加、🗑️ 删除
- **文件上传区域**：独立的文件管理
- **要求输入区域**：机械、电气、防错要求

### 3. 文件管理
- **独立上传**：每个夹具都有自己的文件上传按钮
- **独立预览**：每个夹具的文件列表独立显示
- **独立操作**：预览、下载、删除操作针对特定夹具

## 功能流程

### 1. 添加夹具流程
```
点击"添加夹具"按钮 → 创建新夹具数据 → 生成夹具HTML → 插入到容器 → 更新工站数据
```

### 2. 删除夹具流程
```
点击"删除"按钮 → 确认对话框 → 删除夹具块 → 重新索引 → 更新工站数据
```

### 3. 文件上传流程
```
选择文件 → 验证格式 → 读取文件 → 保存到对应夹具 → 更新预览 → 保存工站数据
```

### 4. 数据同步流程
```
任何夹具操作 → 更新夹具数据 → 调用updateEquipmentStationFixtures → 同步到设备工站数据
```

## 兼容性处理

### 1. 向后兼容
- 支持旧格式的单夹具数据自动转换为新格式
- 保持原有函数签名的向后兼容（通过默认参数）

### 2. 数据迁移
- 自动检测数据格式并进行转换
- 确保现有数据不会丢失

### 3. 渐进增强
- 新功能不影响现有功能
- 逐步迁移到新的多夹具格式

## 总结

多夹具功能已完全实现，提供了：

1. **完整的夹具管理**：添加、删除、编辑夹具
2. **独立的文件管理**：每个夹具都有自己的文件上传和管理
3. **灵活的配置**：每个夹具都有独立的要求配置
4. **数据持久化**：自动保存和恢复多夹具数据
5. **向后兼容**：支持现有数据的平滑迁移

现在每个设备工站都可以管理多个夹具，每个夹具都有完整的文件管理和配置功能！🎉
