#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建优化的Prompt Lab专用知识库
专门为大模型理解和应用设计的知识库格式
"""

import os
import re

def create_optimized_knowledge_base():
    """创建优化的知识库文件"""
    
    # SAB类别零件清单数据
    SAB_DATA = {
        'A': {
            'parts': ['Deflector', 'inflator', 'cushion', 'soft cover'],
            'cycle_time': '8',
            'complexity': '简单',
            'stations': ['ST10-预装', 'ST15-检测', 'ST20-包装']
        },
        'B': {
            'parts': ['Deflector', 'inflator', 'cushion', 'Harness', 'soft cover'],
            'cycle_time': '10', 
            'complexity': '简单',
            'stations': ['ST10-预装', 'ST15-线束装配', 'ST20-折叠', 'ST30-包装']
        },
        'C': {
            'parts': ['Deflector', 'inflator', 'cushion', 'Harness', 'Bracket', 'Nuts', 'soft cover'],
            'cycle_time': '20',
            'complexity': '复杂',
            'stations': ['ST10-预装', 'ST15-支架装配', 'ST20-线束装配', 'ST25-折叠', 'ST30-包装']
        },
        'D': {
            'parts': ['Deflector', 'inflator', 'cushion', 'Harness', 'hard cover'],
            'cycle_time': '15',
            'complexity': '中等',
            'stations': ['ST10-预装', 'ST15-线束装配', 'ST20-硬盖装配', 'ST25-包装']
        },
        'E': {
            'parts': ['Deflector', 'inflator', 'cushion', 'Bracket', 'Nuts', 'housing'],
            'cycle_time': '30',
            'complexity': '复杂',
            'stations': ['ST10-预装', 'ST15-支架装配', 'ST20-外壳装配', 'ST25-检测', 'ST30-包装']
        },
        'F': {
            'parts': ['Deflector', 'inflator', 'cushion', 'hard cover', 'housing', '3D heat'],
            'cycle_time': '30',
            'complexity': '复杂',
            'stations': ['ST10-预装', 'ST15-硬盖装配', 'ST20-外壳装配', 'ST25-加热装配', 'ST30-检测', 'ST35-包装']
        }
    }
    
    content = """SAB产品族工艺设备完整知识库

本知识库专为AI大模型设计，包含SAB产品族A-F所有类别的完整工艺流程和设备配置信息。

=== 核心功能 ===
1. 根据输入零件组合自动识别SAB类别
2. 生成对应的完整工艺流程
3. 提供详细的设备和夹具配置要求
4. 确保工艺部分和设备部分完全对应

=== SAB类别识别规则 ===
根据零件组合精确识别SAB类别：

SAB-A类 (4个零件，节拍8分钟，简单工艺):
- 零件: Deflector, inflator, cushion, soft cover
- 特征: 基础SAB配置，使用软包布
- 工站: ST10-预装, ST15-检测, ST20-包装

SAB-B类 (5个零件，节拍10分钟，简单工艺):
- 零件: Deflector, inflator, cushion, Harness, soft cover  
- 特征: 增加线束，使用软包布
- 工站: ST10-预装, ST15-线束装配, ST20-折叠, ST30-包装

SAB-C类 (7个零件，节拍20分钟，复杂工艺):
- 零件: Deflector, inflator, cushion, Harness, Bracket, Nuts, soft cover
- 特征: 增加支架和螺母，使用软包布
- 工站: ST10-预装, ST15-支架装配, ST20-线束装配, ST25-折叠, ST30-包装

SAB-D类 (5个零件，节拍15分钟，中等工艺):
- 零件: Deflector, inflator, cushion, Harness, hard cover
- 特征: 使用硬盖替代软包布
- 工站: ST10-预装, ST15-线束装配, ST20-硬盖装配, ST25-包装

SAB-E类 (6个零件，节拍30分钟，复杂工艺):
- 零件: Deflector, inflator, cushion, Bracket, Nuts, housing
- 特征: 无线束和覆盖材料，增加外壳
- 工站: ST10-预装, ST15-支架装配, ST20-外壳装配, ST25-检测, ST30-包装

SAB-F类 (6个零件，节拍30分钟，复杂工艺):
- 零件: Deflector, inflator, cushion, hard cover, housing, 3D heat
- 特征: 最复杂配置，包含加热元件
- 工站: ST10-预装, ST15-硬盖装配, ST20-外壳装配, ST25-加热装配, ST30-检测, ST35-包装

=== 零件功能说明 ===
- Deflector: 导流片，用于气流导向，所有SAB类别必备
- inflator: 发生器，气袋充气装置，所有SAB类别必备  
- cushion: 气袋，主要缓冲组件，所有SAB类别必备
- Harness: 线束，电气连接组件，用于B/C/D类
- Bracket: 支架，结构支撑组件，用于C/E类
- Nuts: 螺母，紧固件，与支架配套使用
- soft cover: 软包布，柔性覆盖材料，用于A/B/C类
- hard cover: 硬盖，刚性覆盖组件，用于D/F类
- housing: 外壳，保护性外罩，用于E/F类
- 3D heat: 3D加热元件，温控组件，仅F类使用

=== 输出格式要求 ===
必须严格按照以下Markdown格式输出，确保工艺部分和设备部分的工站号完全对应：

### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

2. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

#### ST15 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]
2. [具体要求]

（二）电气要求:
1. [具体要求]
2. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]
2. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

#### ST15 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

=== 重要提醒 ===
1. 必须根据零件组合准确识别SAB类别
2. 工艺部分和设备部分的工站必须完全对应
3. 每个工站的设备部分必须包含Equipment和Fixture两个子部分
4. 严格按照Markdown格式输出，使用正确的标题层级
5. 确保内容的专业性和准确性

=== 示例应用 ===
输入: "帮我写一份SAB的Linespec，零件为：cushion、inflator、deflector、Harness、softcover。"

识别过程:
1. 提取零件: cushion, inflator, deflector, Harness, softcover
2. 标准化零件名: cushion, inflator, Deflector, Harness, soft cover
3. 匹配SAB类别: 5个零件，包含Harness和soft cover → SAB-B类
4. 生成对应的工艺流程和设备配置

输出: 按照SAB-B类的标准格式生成完整的工艺部分和设备部分内容。

"""
    
    # 添加每个SAB类别的详细示例
    for category, data in SAB_DATA.items():
        content += f"\n{'='*60}\n"
        content += f"SAB-{category}类详细配置示例\n"
        content += f"{'='*60}\n\n"
        
        content += f"零件组合: {', '.join(data['parts'])}\n"
        content += f"节拍时间: {data['cycle_time']}分钟\n"
        content += f"工艺复杂度: {data['complexity']}\n"
        content += f"标准工站: {', '.join(data['stations'])}\n\n"
        
        content += f"典型工艺流程:\n"
        for i, station in enumerate(data['stations'], 1):
            station_num = 5 + i * 5  # ST10, ST15, ST20...
            content += f"ST{station_num} {station}\n"
        
        content += f"\n设备配置要点:\n"
        content += f"- 每个工站都需要Equipment（设备）和Fixture（夹具）\n"
        content += f"- 机械要求、电气要求、防错及点检要求三个方面\n"
        content += f"- 根据零件特性配置相应的检测和装配设备\n\n"
    
    return content

def main():
    """主函数"""
    
    print("🚀 创建优化的Prompt Lab知识库")
    print("=" * 50)
    
    # 创建输出目录
    output_dir = "Prompt_Lab知识库"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成优化的知识库内容
    content = create_optimized_knowledge_base()
    
    # 保存为TXT文件
    output_file = f"{output_dir}/SAB产品族优化知识库.txt"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        file_size = os.path.getsize(output_file)
        
        print(f"✅ 已创建优化知识库文件")
        print(f"📁 文件路径: {output_file}")
        print(f"📊 文件大小: {file_size:,} bytes")
        
        print(f"\n📋 文件特点:")
        print(f"- 专为AI大模型优化设计")
        print(f"- 包含完整的SAB类别识别规则")
        print(f"- 详细的零件功能说明")
        print(f"- 标准的输出格式要求")
        print(f"- 实用的应用示例")
        
        print(f"\n🎯 推荐上传方式:")
        print(f"1. 主要推荐: SAB产品族优化知识库.txt")
        print(f"2. 备选方案: SAB完整知识库.txt")
        print(f"3. 文件格式: TXT (Prompt Lab完全支持)")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建文件失败: {e}")
        return False

if __name__ == "__main__":
    main()
