import pandas as pd
import requests
import urllib3
import os
import time
from datetime import datetime

# Disable SSL warnings (only for development, not recommended for production)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def load_excel_file(file_path):
    """加载Excel文件并返回数据框"""
    try:
        # 检查路径是否是文件夹
        if os.path.isdir(file_path):
            print(f"错误: '{file_path}' 是一个文件夹，不是Excel文件。")
            return None
            
        # 检查文件扩展名
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in ['.xlsx', '.xls', '.xlsm']:
            print(f"警告: '{file_path}' 不是Excel文件格式(.xlsx, .xls, .xlsm)。尝试强制加载...")
        
        data = pd.read_excel(file_path)
        print(f"成功加载Excel文件，共有{len(data)}行数据。")
        return data
    except PermissionError:
        print(f"错误: 无法访问文件 '{file_path}'，权限被拒绝。")
        print("建议检查: 1. 文件是否已被其他程序打开 2. 您是否有读取该文件的权限")
        return None
    except Exception as e:
        print(f"加载Excel文件失败: {e}")
        return None

def get_api_token(base_url, account, app_key, app_secret):
    """
    从API获取临时令牌
    
    参数:
        base_url: API基础URL
        account: 账户名
        app_key: 应用键
        app_secret: 应用密钥
    
    返回:
        (token, error_message): 如果成功，返回token和None；如果失败，返回None和错误信息
    """
    token_url = f"{base_url}/api/token/get"
    print(f"尝试获取API令牌: {token_url}")
    
    # Token请求参数
    token_data = {
        'account': account,
        'appKey': app_key,
        'appSecret': app_secret
    }
    
    try:
        response = requests.post(
            token_url,
            json=token_data,
            verify=False,
            timeout=10
        )
        
        print(f"令牌请求状态码: {response.status_code}")
        
        # 检查状态码
        if response.status_code != 200:
            print(f"获取令牌失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None, f"获取令牌失败: 状态码 {response.status_code}, 信息: {response.text}"
        
        # 解析返回的JSON
        try:
            response_json = response.json()
            print(f"令牌响应: {response_json}")
            
            # 首先检查响应码是否为200
            if response_json.get('code') == 200:
                # 根据Postman显示，token保存在data字段
                if 'data' in response_json:
                    token = response_json['data']
                    print(f"成功获取令牌: {token[:10]}...{token[-10:] if len(token) > 20 else token}")
                    return token, None
                else:
                    print(f"响应中没有data字段: {response_json}")
                    return None, f"响应中没有data字段: {response_json}"
            else:
                error_msg = response_json.get('msg', '未知错误')
                print(f"获取令牌失败，错误码: {response_json.get('code')}, 错误信息: {error_msg}")
                return None, f"获取令牌失败: {error_msg}"
                
        except ValueError as e:
            print(f"解析令牌响应JSON失败: {e}")
            return None, f"解析令牌响应JSON失败: {e}"
            
    except requests.exceptions.RequestException as e:
        print(f"令牌请求异常: {e}")
        return None, f"令牌请求异常: {e}"

def test_api_connection(api_url, headers):
    """测试API连接是否正常 - 使用POST方法"""
    try:
        print(f"测试API连接: {api_url}")
        
        # 使用简单的测试请求体
        test_body = {
            'query': '测试连接',
            'chat_id': 9188,
            'stream': False
        }
        
        # 使用POST方法测试连接
        response = requests.post(
            api_url,
            headers=headers,
            json=test_body,
            verify=False,
            timeout=10
        )
        
        # 打印响应状态和内容以便调试
        print(f"测试响应状态码: {response.status_code}")
        print(f"测试响应内容: {response.text[:200]}...") # 仅显示前200个字符
        
        if response.status_code == 404:
            print("API端点404错误，请确认正确的API URL")
            return False, f"API URL不正确，状态码: 404"
        
        if response.status_code == 401:
            print("API认证错误 (401)，请检查您的认证信息（令牌和API密钥）")
            return False, f"认证失败，状态码: 401，消息: {response.text}"
            
        if response.status_code < 400:
            print(f"API连接测试成功! 状态码: {response.status_code}")
            return True, "连接正常"
        else:
            return False, f"API连接测试失败，状态码: {response.status_code}，信息: {response.text}"
    
    except requests.exceptions.RequestException as e:
        return False, f"连接测试失败: {e}"

def main():
    # 提示用户输入Excel文件路径
    while True:
        input_file_path = input("请输入Excel文件路径(包含文件名和扩展名，如example.xlsx): ").strip()
        
        # 检查文件是否存在
        if not os.path.exists(input_file_path):
            print(f"错误: 文件或文件夹 '{input_file_path}' 不存在。")
            continue  # 继续下一次循环要求用户重新输入
            
        # 如果输入是文件夹，列出该文件夹下的Excel文件供用户选择
        if os.path.isdir(input_file_path):
            print(f"'{input_file_path}' 是一个文件夹。以下是该文件夹中的Excel文件:")
            excel_files = [f for f in os.listdir(input_file_path) 
                          if f.endswith(('.xlsx', '.xls', '.xlsm'))]
            
            if not excel_files:
                print("该文件夹中没有Excel文件。请输入有效的Excel文件路径。")
                continue
            
            # 显示可用的Excel文件
            for i, file in enumerate(excel_files, 1):
                print(f"{i}. {file}")
                
            # 让用户选择
            try:
                choice = int(input("请输入要使用的文件编号: "))
                if 1 <= choice <= len(excel_files):
                    input_file_path = os.path.join(input_file_path, excel_files[choice-1])
                    print(f"已选择: {input_file_path}")
                    break
                else:
                    print("无效的选择。请重新输入文件路径。")
                    continue
            except ValueError:
                print("请输入有效的数字。")
                continue
        else:
            # 如果是文件，检查扩展名
            file_ext = os.path.splitext(input_file_path)[1].lower()
            if file_ext not in ['.xlsx', '.xls', '.xlsm']:
                use_anyway = input(f"警告: '{input_file_path}' 不是Excel文件(.xlsx, .xls, .xlsm)。是否仍然尝试使用? (y/n): ")
                if use_anyway.lower() != 'y':
                    continue
            break  # 文件有效，跳出循环
    
    # 加载数据
    data = load_excel_file(input_file_path)
    if data is None:
        return
    
    # 检查必要的列是否存在
    question_column = '常见问题'
    answer_column = 'AI生成的答案'
    
    if question_column not in data.columns:
        print(f"错误: 列 '{question_column}' 不存在。请确保Excel文件包含此列。")
        return
    
    # 如果答案列不存在，添加它
    if answer_column not in data.columns:
        data[answer_column] = ""
    
    # API配置 - 使用基础URL
    default_base_url = 'https://ach-sso01.ap.autoliv.int:18443'
    base_url = input(f"请输入API基础URL (默认为 {default_base_url}): ").strip() or default_base_url
    
    # 获取动态认证信息（令牌）
    print("\n根据文档，需要先获取临时令牌，请输入您的认证信息:")
    account = input("请输入账户名(account): ").strip()
    app_key = input("请输入应用键(appKey): ").strip()
    app_secret = input("请输入应用密钥(appSecret): ").strip()
    
    if not (account and app_key and app_secret):
        print("错误: 账户名、应用键和应用密钥都不能为空。")
        return
    
    # 获取临时令牌
    token, error = get_api_token(base_url, account, app_key, app_secret)
    
    if error:
        print(f"获取令牌失败: {error}")
        proceed = input("无法获取API令牌。是否继续? (y/n): ")
        if proceed.lower() != 'y':
            print("操作已取消。")
            return
        token = input("请手动输入令牌: ").strip()
        if not token:
            print("操作已取消。")
            return
    
    # 获取静态API密钥
    default_api_key = '0b2c4eec9-be01-46f6-a501-f313a348d979'  # 默认的静态API密钥
    print("\n请输入API的静态密钥 (X-API-Key)")
    api_key = input(f"请输入API静态密钥 (默认为 {default_api_key}): ").strip() or default_api_key
    
    # 设置请求头 - 包含两种认证方式
    headers = {
        'Authorization': f'Bearer {token}',
        'X-API-Key': api_key,  # 添加静态API密钥
        'Content-Type': 'application/json'
    }
    
    print("\n现在使用以下双重认证信息:")
    print(f"令牌 (Authorization): {token[:10]}...{token[-10:] if len(token) > 20 else token}")
    print(f"API密钥 (X-API-Key): {api_key}")
    
    # 设置API端点
    api_endpoint = 'api/v1/chat-messages'
    api_url = f"{base_url}/{api_endpoint}"
    print(f"使用API端点: {api_url}")
    
    # 设置聊天ID
    chat_id = input("请输入聊天ID (默认为 9188): ").strip() or "9188"
    chat_id = int(chat_id)
    
    # 测试API连接
    success, message = test_api_connection(api_url, headers)
    if not success:
        print(f"警告: {message}")
        
        # 如果是认证错误，提供更多信息
        if "401" in message:
            print("\n可能的解决方案:")
            print("1. 确认您的认证信息是否正确")
            print("2. 确保同时提供了动态令牌和静态API密钥")
            print("3. 令牌可能已过期，尝试重新获取")
            print("4. 联系API提供方获取有效的认证信息")
        
        proceed = input("API连接测试失败。是否仍要继续? (y/n): ")
        if proceed.lower() != 'y':
            print("操作已取消。")
            return
    
    # 创建一个带时间戳的输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file_path = os.path.splitext(input_file_path)[0] + f"_answers_{timestamp}.xlsx"
    
    # 处理计数器
    total_questions = len(data)
    success_count = 0
    error_count = 0
    
    print("\n开始处理问题...")
    
    # 遍历每一个问题，通过API获取答案
    for index, row in data.iterrows():
        question = row[question_column]
        print(f"\n处理问题 {index+1}/{total_questions}: {question[:50]}...")
        
        # API请求体 - 根据截图中的格式
        body = {
            'query': question,
            'chat_id': chat_id,  # 使用用户输入的聊天ID
            'stream': False
        }
        
        try:
            # 发送API请求，禁用SSL验证（仅用于测试环境）
            print(f"发送请求到: {api_url}")
            print(f"请求体: {body}")  # 打印请求体以便检查
            
            response = requests.post(
                api_url, 
                headers=headers, 
                json=body, 
                verify=False,
                timeout=60  # 增加超时时间到60秒
            )
            
            # 打印响应状态码
            print(f"响应状态: {response.status_code}")
            
            # 检查是否是认证错误
            if response.status_code == 401:
                error_message = f"API认证错误 (401): {response.text}"
                print(error_message)
                data.at[index, answer_column] = f"获取答案失败: {error_message}"
                error_count += 1
                # 由于认证错误，后续请求可能都会失败，询问用户是否更新认证信息
                retry = input("认证错误。是否尝试更新认证信息? (y/n): ")
                if retry.lower() == 'y':
                    # 更新认证信息选项
                    update_token = input("是否重新获取令牌? (y/n): ").strip().lower()
                    if update_token == 'y':
                        # 重新获取令牌
                        token, error = get_api_token(base_url, account, app_key, app_secret)
                        if error:
                            print(f"重新获取令牌失败: {error}")
                            manual_token = input("请手动输入令牌 (留空则终止处理): ").strip()
                            if not manual_token:
                                print("由于认证问题，终止处理。")
                                break
                            token = manual_token
                        
                        # 更新请求头中的令牌
                        headers['Authorization'] = f'Bearer {token}'
                        print(f"已更新令牌: {token[:10]}...{token[-10:] if len(token) > 20 else token}")
                    
                    # 更新API密钥选项
                    update_api_key = input("是否更新API密钥? (y/n): ").strip().lower()
                    if update_api_key == 'y':
                        new_api_key = input("请输入新的API密钥: ").strip()
                        if new_api_key:
                            headers['X-API-Key'] = new_api_key
                            api_key = new_api_key
                            print(f"已更新API密钥: {api_key}")
                    
                    print("已更新认证信息，继续处理...")
                    continue
                else:
                    print("由于认证问题，终止处理。")
                    break
            
            # 检查响应状态码
            response.raise_for_status()
            
            # 尝试解析JSON响应
            try:
                response_json = response.json()
                print(f"响应内容类型: {type(response_json)}")
                
                # 提取答案 - 根据截图中的响应格式
                if isinstance(response_json, dict):
                    # 尝试获取URL字段（如果存在）
                    if "url" in response_json:
                        answer = f"请点击以下链接下载结果: {response_json['url']}"
                    # 尝试获取可能的答案字段
                    elif "pre_prompt" in response_json:
                        answer = response_json["pre_prompt"]
                    elif "references" in response_json:
                        answer = response_json["references"]
                    else:
                        # 如果找不到特定字段，则保存整个响应
                        answer = str(response_json)
                else:
                    # 如果响应不是字典，直接转换为字符串
                    answer = str(response_json)
            except ValueError:
                # 如果不是JSON格式，则保存原始文本
                answer = response.text
            
            # 更新数据框
            data.at[index, answer_column] = answer
            success_count += 1
            print(f"成功获取答案，长度: {len(str(answer))} 字符")
            
        except requests.exceptions.HTTPError as http_err:
            error_message = f"HTTP错误: {http_err}"
            print(error_message)
            
            # 对于404错误，提供更详细的错误信息和建议
            if "404" in str(http_err):
                print("\n可能的解决方案:")
                print("1. 检查API URL是否正确，当前URL为:", api_url)
                print("2. 确认API服务是否在运行")
                print("3. 咨询API提供方以获取正确的端点")
                
            data.at[index, answer_column] = f"获取答案失败: {error_message}"
            error_count += 1
            
        except requests.exceptions.Timeout:
            error_message = "请求超时，请稍后再试。"
            print(error_message)
            data.at[index, answer_column] = f"获取答案失败: {error_message}"
            error_count += 1
            
        except Exception as e:
            error_message = f"发生未知错误: {e}"
            print(error_message)
            data.at[index, answer_column] = f"获取答案失败: {error_message}"
            error_count += 1
        
        # 每处理完5个问题，就保存一次进度
        if (index + 1) % 5 == 0 or index == total_questions - 1:
            try:
                data.to_excel(output_file_path, index=False)
                print(f"进度已保存至 {output_file_path}")
            except Exception as e:
                print(f"保存文件时出错: {e}")
        
        # 短暂暂停，避免API请求过于频繁
        time.sleep(1)
    
    # 最终结果保存
    try:
        data.to_excel(output_file_path, index=False)
        print(f"\n处理完成! 结果已保存至 {output_file_path}")
        print(f"总问题数: {total_questions}, 成功: {success_count}, 失败: {error_count}")
    except Exception as e:
        print(f"保存最终结果时出错: {e}")

if __name__ == "__main__":
    main() 