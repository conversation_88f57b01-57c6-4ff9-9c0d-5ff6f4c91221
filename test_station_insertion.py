#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工站插入功能测试脚本
测试新的工站和步骤插入功能是否正常工作
"""

import os
import re
import sys

def test_station_insertion_functions():
    """测试工站插入功能的实现"""
    print("🧪 测试工站插入功能...")
    
    try:
        # 读取station_manager.js文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_manager.js文件")
        
        # 1. 检查新的插入函数是否存在
        print("\n1. 检查插入函数...")
        
        required_functions = [
            'insertProcessStation',
            'insertProcessStationBefore', 
            'insertProcessStationAfter',
            'insertProcessStep',
            'insertProcessStepBefore',
            'insertProcessStepAfter'
        ]
        
        for func_name in required_functions:
            if f'function {func_name}(' in js_content:
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
                return False
        
        # 2. 检查原有的addNewProcessStation函数是否被修改
        print("\n2. 检查addNewProcessStation函数修改...")
        
        if 'insertProcessStation(processStationsData.length)' in js_content:
            print("✅ addNewProcessStation已修改为调用insertProcessStation")
        else:
            print("❌ addNewProcessStation未正确修改")
            return False
        
        # 3. 检查步骤重新编号逻辑
        print("\n3. 检查步骤重新编号逻辑...")
        
        renumber_pattern = r'steps\.forEach\(\(step, index\) => \{\s*step\.step_number = \(index \+ 1\)\.toString\(\);'
        if re.search(renumber_pattern, js_content, re.MULTILINE):
            print("✅ 步骤重新编号逻辑存在")
        else:
            print("❌ 步骤重新编号逻辑不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_station_generator_modifications():
    """测试工站生成器的修改"""
    print("\n🧪 测试工站生成器修改...")
    
    try:
        # 读取station_generator.js文件
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_generator.js文件")
        
        # 1. 检查工站插入按钮
        print("\n1. 检查工站插入按钮...")
        
        if 'insertProcessStationBefore' in js_content and 'insertProcessStationAfter' in js_content:
            print("✅ 工站插入按钮存在")
        else:
            print("❌ 工站插入按钮不存在")
            return False
        
        # 2. 检查步骤插入按钮
        print("\n2. 检查步骤插入按钮...")
        
        if 'insertProcessStepBefore' in js_content and 'insertProcessStepAfter' in js_content:
            print("✅ 步骤插入按钮存在")
        else:
            print("❌ 步骤插入按钮不存在")
            return False
        
        # 3. 检查步骤HTML函数参数修改
        print("\n3. 检查步骤HTML函数参数...")
        
        if 'createProcessStepHtml(step, stationIndex, stepIndex, totalSteps' in js_content:
            print("✅ 步骤HTML函数参数已更新")
        else:
            print("❌ 步骤HTML函数参数未更新")
            return False
        
        # 4. 检查最后步骤的插入按钮逻辑
        print("\n4. 检查最后步骤插入按钮...")
        
        if 'stepIndex === totalSteps - 1' in js_content:
            print("✅ 最后步骤插入按钮逻辑存在")
        else:
            print("❌ 最后步骤插入按钮逻辑不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_html_template_integration():
    """测试HTML模板集成"""
    print("\n🧪 测试HTML模板集成...")
    
    try:
        # 读取index.html文件
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print("✅ 成功读取index.html文件")
        
        # 检查添加工站按钮是否仍然存在
        if 'onclick="addNewProcessStation()"' in html_content:
            print("✅ 添加工站按钮仍然存在")
        else:
            print("❌ 添加工站按钮不存在")
            return False
        
        # 检查是否引用了相关的JS文件
        if 'station_manager.js' in html_content and 'station_generator.js' in html_content:
            print("✅ 相关JS文件已引用")
        else:
            print("❌ 相关JS文件未正确引用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试工站插入功能...")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_station_insertion_functions,
        test_station_generator_modifications,
        test_html_template_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    # 输出测试结果
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工站插入功能实现完成。")
        print("\n📝 功能说明:")
        print("1. ✅ 支持在任意位置插入新工站")
        print("2. ✅ 支持在任意位置插入新步骤")
        print("3. ✅ 自动重新编号和索引管理")
        print("4. ✅ 用户友好的插入界面")
        print("5. ✅ 不会清除已有内容")
        
        print("\n🎯 使用方法:")
        print("- 点击工站间的'↑ 在此前插入工站'按钮在指定位置前插入工站")
        print("- 点击工站右上角的'插入工站'按钮在该工站后插入新工站")
        print("- 点击步骤间的'↑ 插入步骤'按钮在指定位置前插入步骤")
        print("- 点击步骤右上角的'+'按钮在该步骤后插入新步骤")
        print("- 原有的'添加工艺工站'和'+添加'按钮仍然可用，会在末尾添加")
        
        return True
    else:
        print("❌ 部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
