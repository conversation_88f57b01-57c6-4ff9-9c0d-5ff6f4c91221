"""
改进的Prompt生成器
用于生成标准格式的AI查询prompt，确保返回内容能被正确解析
"""

import os
import json
from typing import List, Dict, Optional

class PromptGenerator:
    def __init__(self):
        """初始化Prompt生成器"""
        self.load_templates()
        self.load_parts_mapping()
    
    def load_templates(self):
        """加载prompt模板"""
        template_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'improved_prompt_template.txt')
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                self.prompt_template = f.read()
        except FileNotFoundError:
            # 默认模板
            self.prompt_template = """请按照以下标准格式生成Linespec内容：

【工艺部分】
ST10 [工站名称]
1. 工艺过程描述: [详细描述]
人or设备: [人工/设备]
产品特性要求: [质量要求]
过程防错要求: [防错措施]
【工艺部分结束】

【设备部分】
ST10设备信息
设备类型: [设备类型]
技术要求: [技术要求]
【设备部分结束】

产品类型：{product_type}
零件清单：{parts_list}"""
    
    def load_parts_mapping(self):
        """加载零件映射关系"""
        mapping_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'template_parts_mapping.json')
        try:
            with open(mapping_path, 'r', encoding='utf-8') as f:
                self.parts_mapping = json.load(f)
        except FileNotFoundError:
            self.parts_mapping = {}
    
    def generate_linespec_prompt(self, 
                                product_type: str, 
                                parts_list: List[str], 
                                project_requirements: Optional[str] = None) -> str:
        """
        生成Linespec查询的标准prompt
        
        Args:
            product_type: 产品类型 (如SAB, DAB等)
            parts_list: 零件列表
            project_requirements: 项目特殊要求
            
        Returns:
            格式化的prompt字符串
        """
        # 格式化零件列表
        parts_str = '、'.join(parts_list) if parts_list else '若干零件'
        
        # 确定工艺类型
        process_type = self.determine_process_type(product_type, parts_list)
        
        # 构建基础prompt
        base_prompt = f"""帮我写一份{product_type}模块的Linespec，涉及的零件有：{parts_str}。根据零件匹配该项目所属的工艺类型，根据不同工艺类型对应的工艺信息和设备信息输出推荐的工艺部分和设备部分，按照工站号顺序进行描述请特别注意：每个工艺步骤的"人or设备"字段必须明确填写，不能为空。
"""

        # 添加项目特殊要求
        if project_requirements:
            base_prompt += f"\n\n特殊要求：{project_requirements}"
        
        return base_prompt
    
    def determine_process_type(self, product_type: str, parts_list: List[str]) -> str:
        """
        根据产品类型和零件列表确定工艺类型
        
        Args:
            product_type: 产品类型
            parts_list: 零件列表
            
        Returns:
            工艺类型 (A, B, C, D, E, F)
        """
        if product_type not in self.parts_mapping:
            return "B"  # 默认类型
        
        product_mapping = self.parts_mapping[product_type]
        parts_set = set(part.lower() for part in parts_list)
        
        # 找到最匹配的工艺类型
        best_match = "B"
        max_match_count = 0
        
        for process_type, template_parts in product_mapping.items():
            template_parts_set = set(part.lower() for part in template_parts)
            match_count = len(parts_set.intersection(template_parts_set))
            
            if match_count > max_match_count:
                max_match_count = match_count
                best_match = process_type
        
        return best_match
    
    def generate_product_info_prompt(self, parts_data: List[Dict]) -> str:
        """
        生成产品信息查询的prompt
        
        Args:
            parts_data: 零件数据列表
            
        Returns:
            产品信息prompt
        """
        parts_info = []
        for part in parts_data:
            part_str = f"""
• 零件信息：
  - 项目名称：{part.get('projectName', '')}
  - 零件编号：{part.get('partNumber', '')}
  - 零件名称：{part.get('partName', '')}
  - 单位用量：{part.get('unitQuantity', '')}
  - 包装尺寸：{part.get('packageSize', '')}
  - 单位包装数量：{part.get('packageQuantity', '')}
  - 零件状态：{part.get('partStatus', '')}
  - 共享项目：{part.get('sharedProject', '')}
  - 备注：{part.get('remarks', '')}"""
            parts_info.append(part_str)
        
        return f"""请帮我完成产品零件信息的审核和整理。

零件清单：
{''.join(parts_info)}

请检查以上信息是否完整，并提供相关建议。同时请按照标准格式整理零件信息表格。"""

# 全局prompt生成器实例
prompt_generator = PromptGenerator()
