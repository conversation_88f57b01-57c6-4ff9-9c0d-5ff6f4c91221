# SAB-A类知识库

## 概述
SAB-A类产品的工艺和设备要求规范。

**产品族**: SAB
**工艺类型**: A
**标准节拍时间**: 20秒
**应用场景**: 适用于包含4个主要零件的SAB产品装配


## 零件清单
1. **Deflector** - 导流片，用于气流导向
2. **inflator** - 发生器，气袋充气装置
3. **cushion** - 气袋，主要缓冲组件
4. **soft cover** - 软包布，柔性覆盖材料


## 标准工艺流程

### 工艺部分

#### ST10 Pre-assembly
（一）
1. 工艺过程描述: 员工拿取发生器
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 将发生器放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 夹具自动夹紧
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 员工拿取一个导流片
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 设备自动影像检测导流片安装正确
   - 人or设备: 设备
   - 产品特性要求: 导流片安装方向正确
   - 过程防错要求: 无漏装，无错装

8. 工艺过程描述: 设备自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 正确的发生器

9. 工艺过程描述: 员工拿取一个气袋
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 将气袋套到发生器上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 设备自动影像检测气袋正确套入
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 无扫A做B

13. 工艺过程描述: 设备自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 正确的气袋

14. 工艺过程描述: 设备自动松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST20 Folding
（一）
1. 工艺过程描述: 员工将组件放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 夹具自动夹紧螺柱
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 员工将气袋拉直放入上夹头中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 上夹头自动夹紧
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 员工手持线束头，扫描线束条码
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 员工退出安全光栅
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 自动扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: nan

8. 工艺过程描述: 设备自动折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠
   - 过程防错要求: nan

9. 工艺过程描述: 员工将气袋套入包布中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 踩脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 员工将包布包好
   - 人or设备: nan
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST30 Package
（一）
1. 工艺过程描述: 员工将组件放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 设备自动夹紧螺柱
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 员工将线束头插入电检夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 手拍触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 安全门关闭
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 设备自动拍打模块
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 设备自动检测模块厚度
   - 人or设备: 设备
   - 产品特性要求: 正确的模块厚度
   - 过程防错要求: nan

8. 工艺过程描述: 设备自动检测模块电性能
   - 人or设备: 设备
   - 产品特性要求: 正确的模块电性能
   - 过程防错要求: nan

9. 工艺过程描述: 安全门打开
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 自动打印标签
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 员工将标签黏贴在模块上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 手拍触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

13. 工艺过程描述: 设备自动扫描总成标签
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

14. 工艺过程描述: 设备自动松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

15. 工艺过程描述: 下线，包装
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

### 设备部分

#### ST10 Pre-assembly
一、SAB-发生器预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器扫描OK/NG，气袋扫描OK/NG，影像OK/NG 等指示灯
6.发生器夹持治具下方台面配置黑色背景板
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
10.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.导流片安装方向正确、导流片无错装、漏装
导流片通过上方IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.无扫A做B
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、发生器、气袋预装治具-Fixture 
（一）机械要求:
1. 1.发生器平放插入治具内，以发生器塑料端面为基准，治具内带有传感器检测放置到位。
2.治具夹持不能对发生器端面造成磕碰损伤
3.治具上配置发生器螺柱导向功能，原位伸出，发生器夹持后缩回
4.治具松开，发生器不会掉落
5.治具夹紧发生器后不晃动、松动，治具夹持后间隙需小于发生器直径，夹持长度至少要15mm
6.发生器夹持治具整体设计哈丁成快换形式，到位后气缸自锁。
7.治具于产品接触部分无锐边，毛刺，表面需倒角
8.治具无夹手风险
9.治具带有防护罩，防止螺丝掉落

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 Folding
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断气袋是否歪斜
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备前方需要2组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。自动转折机构部分外部使用亚克力板进行物理防护
5.设备卷折需能实现inboard和outboard折叠的需求；折叠首折尺寸和折尺尺寸需要满足图纸的要求;
6.设备增设扫描枪，扫描气袋条码，增加滑轨，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代
12.设备由气袋卷折模组、上气袋夹持模组及产品定位机构3大部分组成
13.气袋卷折模组可实现上下，左右移动，上下移动由伺服实现，伺服行程450mm，极限位置都须有限位开关、机械硬限位及独立零位传感器；横向左右利用气缸完成移动，带有硬限位及缓冲装置；伺服控制旋转气缸从而满足折尺的正反转。
14.上部气袋夹持机构有传感器检测气袋放置到位；内部小气缸夹持气袋，开口大小≤4mm，无夹手风险
15.夹持机构整体上下可通过伺服移动。左右滑轨，可分别单独通过拉拔销进行移动。下方有托板防止异物掉落。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.伺服驱动器需要有STO功能
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.气袋不歪斜：
通过设备上方的相机影像来确保气袋夹持不歪斜
5.正确的折叠
通过预设的折叠参数保证气袋折叠。折叠参数上传追溯控制
方案1：[具体方案]

二、1.产品定位治具
2.折叠尺-Fixture 
（一）机械要求:
1. 产品定位治具：
1.左右共用一副工装，螺柱夹爪材质铜，增加螺纹。发生器螺柱夹持不松脱
2.治具带有发生器放反防错
3.治具设计轻便，不干涉操作
4.治具设计成哈丁接口快换
5.治具与产品接触部分无锐边，毛刺，表面需倒角
6.治具无夹手风险
7.传感器放大器安装位置便于维修调试
8.两侧安装把手，方便拿取
9.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
2. 折叠尺：
1.折尺尺寸根据实际产品图纸制作，表面光滑，无毛刺。折尺滑槽座，滑槽按ALV统一标准设计，由二维码防错，折尺上开凹槽粘贴二维码，避免剐蹭
2.折尺固定滑槽尺寸需与折尺滑槽座尺寸适宜，公差合理，折尺插入后，前端不晃动、歪斜

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST30 Package
一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
4.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
5.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
6.设备顶上带有蜂鸣器的红黄绿三色灯
7.设备设计合理，尺寸紧凑，空间无浪费
8.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
方案1：[具体方案]

二、1.产品定位治具
2.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
2.治具托板为平板
3.治具设计合理，无安全风险
4.治具设计轻便，整体重量≤7.5Kg
5.传感器放大器安装位置便于维修调试
6.传感器检测发生器螺柱，无需夹爪夹持
7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
9.两侧安装把手，方便拿取
10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
2. 厚度GT检测机构：
1.厚度检测机构按照最新的设备标机设计
2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
3.GT头下压过程中顺畅，不卡顿
4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
5.厚度检测能覆盖20mm-50mm之间的产品
6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
7.测厚压板左右考虑共用，下压时避开产品发生器区域
8.压板需设计成快换，并带有防错
9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
10.压板机构自重掉落过程中顺畅、不卡顿
11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
3. 归零、标准点检块：
1.归零块：对产品厚度检测机构GT进行归零
2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

