#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容解析器测试脚本
用于测试和验证AI生成内容的解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.content_parser import content_parser

def test_content_parsing():
    """测试内容解析功能"""
    
    # 测试用的AI生成内容
    test_content = """
【工艺部分】
ST10 SAB-D-Pre-assembly
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
人or设备: 人
产品特性要求: 导流片无错装、漏装、安装方向正确
过程防错要求: 通过MSA

2. 工艺过程描述: 将线束保护支架预装在发生器上
人or设备: 人
产品特性要求: 正确的支架
过程防错要求: 无夹伤，支架正确

ST15 SAB-D-Assembly
1. 工艺过程描述: 将充气器安装到发生器上
人or设备: 设备
产品特性要求: 充气器安装到位，无损伤
过程防错要求: 通过扭矩检测

ST20 Folding
1. 工艺过程描述: 将气囊折叠并装入发生器
人or设备: 设备
产品特性要求: 气囊折叠规范，无褶皱
过程防错要求: 视觉检测系统确认
【工艺部分结束】

【设备部分】
ST10设备信息
设备类型: 手动装配工位
技术要求: 提供导流片定位夹具
设备参数: 工作台高度800mm
安全要求: 防静电措施

ST15设备信息
设备类型: 半自动装配设备
技术要求: 充气器安装压力控制
设备参数: 压力范围50-100N
安全要求: 双手启动按钮
【设备部分结束】
"""
    
    print("=" * 60)
    print("测试AI生成内容解析功能")
    print("=" * 60)
    
    # 解析内容
    parsed_content = content_parser.parse_content(test_content)
    
    print("\n1. 基础内容分割结果:")
    print(f"工艺部分长度: {len(parsed_content.get('process_content', ''))}")
    print(f"设备部分长度: {len(parsed_content.get('equipment_content', ''))}")
    
    # 提取工站信息
    if parsed_content.get('process_content'):
        print("\n2. 工艺工站解析结果:")
        process_stations = content_parser.extract_station_info(parsed_content['process_content'])
        
        for station in process_stations:
            print(f"\n工站: ST{station['station_number']} - {station['station_name']}")
            print(f"步骤数量: {len(station.get('process_steps', []))}")
            
            for step in station.get('process_steps', []):
                print(f"  步骤 {step.get('step_number', '?')}:")
                print(f"    工艺过程描述: {step.get('description', '未识别')}")
                print(f"    人or设备: {step.get('operator', '未识别')}")
                print(f"    产品特性要求: {step.get('quality_requirements', '未识别')}")
                print(f"    过程防错要求: {step.get('error_prevention', '未识别')}")
    
    # 提取设备工站信息
    if parsed_content.get('equipment_content'):
        print("\n3. 设备工站解析结果:")
        equipment_stations = content_parser.extract_equipment_station_info(parsed_content['equipment_content'])
        
        for station in equipment_stations:
            print(f"\n设备工站: ST{station['station_number']}")
            details = station.get('equipment_details', {})
            print(f"  设备类型: {details.get('equipment_type', '未识别')}")
            print(f"  技术要求: {details.get('technical_requirements', '未识别')}")
            print(f"  设备参数: {details.get('equipment_parameters', '未识别')}")
            print(f"  安全要求: {details.get('safety_requirements', '未识别')}")

def test_edge_cases():
    """测试边缘情况"""
    
    print("\n" + "=" * 60)
    print("测试边缘情况")
    print("=" * 60)
    
    # 测试没有明确标记的内容
    edge_case_content = """
ST10 SAB-B-Pre-assembly
1. 拿取导流片，将导流片安装到发生器上
人: 人工操作
产品特性要求: 导流片无错装、漏装
过程防错要求: 通过MSA

2. 将线束保护支架预装在发生器上
人or设备: 人
质量要求: 正确的支架
防错: 无夹伤，支架正确
"""
    
    print("测试内容:")
    print(edge_case_content)
    
    stations = content_parser.extract_station_info(edge_case_content)
    
    print(f"\n解析结果: {len(stations)} 个工站")
    for station in stations:
        print(f"\n工站: ST{station['station_number']} - {station['station_name']}")
        for step in station.get('process_steps', []):
            print(f"  步骤 {step.get('step_number', '?')}:")
            print(f"    描述: {step.get('description', '未识别')}")
            print(f"    操作者: {step.get('operator', '未识别')}")

def test_format_variations():
    """测试不同格式变体"""
    
    print("\n" + "=" * 60)
    print("测试格式变体")
    print("=" * 60)
    
    # 测试不同的格式变体
    variations = [
        # 变体1: 冒号后换行
        """
ST10 Test-Station
1. 工艺过程描述:
   拿取零件并安装
人or设备:
   人工操作
产品特性要求:
   安装正确
""",
        # 变体2: 中文冒号
        """
ST15 Test-Station-2
1. 工艺过程描述：拿取零件并安装
人or设备：设备
产品特性要求：安装正确
""",
        # 变体3: 没有冒号
        """
ST20 Test-Station-3
1. 工艺过程描述 拿取零件并安装
人or设备 设备
产品特性要求 安装正确
"""
    ]
    
    for i, content in enumerate(variations, 1):
        print(f"\n变体 {i}:")
        stations = content_parser.extract_station_info(content)
        
        for station in stations:
            print(f"工站: ST{station['station_number']}")
            for step in station.get('process_steps', []):
                print(f"  描述: {step.get('description', '未识别')}")
                print(f"  操作者: {step.get('operator', '未识别')}")

if __name__ == "__main__":
    test_content_parsing()
    test_edge_cases()
    test_format_variations()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
