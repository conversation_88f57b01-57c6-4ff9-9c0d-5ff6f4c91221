
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同行布局预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .station-preview { 
            border: 1px solid #e0e0e0; 
            border-radius: 6px; 
            padding: 0.8rem; 
            margin-bottom: 1rem; 
            background: #fafbfc; 
        }
        .station-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.6rem; 
            padding-bottom: 0.4rem; 
            border-bottom: 1px solid #e0e0e0; 
        }
        .station-title-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .collapse-btn {
            background: none; 
            border: none; 
            color: #1a73e8; 
            cursor: pointer; 
            font-size: 0.8rem; 
            padding: 0; 
            width: 16px; 
            height: 16px; 
            display: flex; 
            align-items: center; 
            justify-content: center;
        }
        .station-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 1rem; 
            font-weight: 600; 
            cursor: pointer; 
            padding: 2px 4px; 
            border-radius: 3px; 
            transition: background-color 0.2s;
        }
        .station-title:hover {
            background-color: #f0f7ff;
        }
        .delete-btn {
            background: #ff4d4f; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            padding: 2px 6px; 
            cursor: pointer; 
            font-size: 0.7rem;
        }
        .step-preview {
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white;
        }
        .step-header {
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem;
        }
        .step-title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .step-title {
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500;
        }
        .operator-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        .operator-label {
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap;
        }
        .operator-select {
            width: 80px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 28px;
        }
        .step-delete-btn {
            background: #ff7875; 
            color: white; 
            border: none; 
            border-radius: 2px; 
            padding: 1px 4px; 
            cursor: pointer; 
            font-size: 0.65rem;
        }
        
        /* 同行布局样式 */
        .content-row {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.4rem;
        }
        .content-row:last-child {
            margin-bottom: 0;
        }
        .content-label {
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap; 
            min-width: 90px;
        }
        .content-textarea {
            flex: 1; 
            height: 35px; 
            padding: 0.3rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            resize: none; 
            font-size: 0.8rem; 
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>同行布局预览</h2>
        <p>三个标题与内容在同一行显示，使用flex布局实现</p>
        
        <div class="station-preview">
            <!-- 工站标题行 -->
            <div class="station-header">
                <div class="station-title-group">
                    <button class="collapse-btn" title="折叠/展开">
                        <span>▼</span>
                    </button>
                    <h4 class="station-title" title="双击编辑工站标题">
                        ST10 - Pre-assembly
                    </h4>
                </div>
                <button class="delete-btn">删除</button>
            </div>
            
            <!-- 工艺步骤 -->
            <div class="step-preview">
                <!-- 步骤标题行 -->
                <div class="step-header">
                    <div class="step-title-group">
                        <h6 class="step-title">步骤 1</h6>
                        <div class="operator-group">
                            <label class="operator-label">人or设备:</label>
                            <select class="operator-select">
                                <option>人</option>
                            </select>
                        </div>
                    </div>
                    <button class="step-delete-btn">×</button>
                </div>
                
                <!-- 同行布局的三个内容行 -->
                <div class="content-row">
                    <label class="content-label">工艺过程描述:</label>
                    <textarea class="content-textarea" placeholder="请输入工艺过程描述">人工检查包装材料完整性</textarea>
                </div>
                
                <div class="content-row">
                    <label class="content-label">产品特性要求:</label>
                    <textarea class="content-textarea" placeholder="请输入产品特性要求">包装材料无破损，标签清晰</textarea>
                </div>
                
                <div class="content-row">
                    <label class="content-label">过程防错要求:</label>
                    <textarea class="content-textarea" placeholder="请输入过程防错要求">目视检查，发现问题立即停止</textarea>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>同行布局特点：</h3>
            <ul>
                <li>✅ <strong>标题与内容同行</strong>：使用flex布局，标题和文本框在同一行</li>
                <li>✅ <strong>标签固定宽度</strong>：min-width: 90px，保持对齐</li>
                <li>✅ <strong>文本框自适应</strong>：flex: 1，占据剩余空间</li>
                <li>✅ <strong>垂直居中</strong>：align-items: center，标题和文本框垂直居中</li>
                <li>✅ <strong>适当间距</strong>：gap: 0.5rem，标题和文本框之间有间距</li>
                <li>✅ <strong>防止换行</strong>：white-space: nowrap，标题不会换行</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px;">
            <h3>布局对比：</h3>
            
            <h4>修改前（两行布局）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 工艺过程描述:                                         │
│ [──────────────文本框──────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求:                                         │
│ [──────────────文本框──────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求:                                         │
│ [──────────────文本框──────────────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
            
            <h4>修改后（同行布局）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 工艺过程描述: [────────────文本框────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求: [────────────文本框────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求: [────────────文本框────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #fff7e6; border-radius: 4px;">
            <h3>技术实现：</h3>
            <h4>CSS Flexbox布局：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
.content-row {
    display: flex;           /* 使用flex布局 */
    align-items: center;     /* 垂直居中对齐 */
    gap: 0.5rem;            /* 元素间距 */
}

.content-label {
    min-width: 90px;        /* 固定最小宽度 */
    white-space: nowrap;    /* 防止换行 */
}

.content-textarea {
    flex: 1;                /* 占据剩余空间 */
}
            </pre>
        </div>
    </div>
</body>
</html>
        