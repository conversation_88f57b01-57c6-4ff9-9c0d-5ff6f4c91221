# 产线Layout上传功能实现说明

## 功能概述

在工艺要求页面上方添加了产线Layout上传功能，用于展示产线Layout图片和相关参数。该功能参考了您提供的Layout布局设计，实现了完整的图片上传、预览、参数设置和数据管理功能。

## 实现的功能特性

### 1. 图片上传功能
- **支持格式**: JPG、PNG格式图片
- **文件大小限制**: 最大10MB
- **上传验证**: 自动验证文件类型和大小
- **加载状态**: 显示上传进度和状态提示

### 2. 图片预览功能
- **实时预览**: 上传后立即显示图片预览
- **点击放大**: 点击图片可在模态框中查看大图
- **文件信息**: 显示文件名等基本信息
- **响应式设计**: 自适应不同屏幕尺寸

### 3. 产线参数设置
- **长度(mm)**: 产线长度参数输入
- **宽度(mm)**: 产线宽度参数输入  
- **高度(mm)**: 产线高度参数输入
- **产线节拍(s)**: 产线节拍时间输入

### 4. 数据管理功能
- **数据存储**: 图片和参数数据的本地存储
- **数据获取**: `getProcessLayoutData()` 函数获取所有数据
- **数据设置**: `setProcessLayoutData()` 函数设置数据
- **数据清除**: 支持删除图片和清空数据

### 5. 用户体验优化
- **Toast提示**: 操作成功/失败的友好提示
- **加载动画**: 上传过程中的加载状态显示
- **确认对话框**: 删除操作前的确认提示
- **键盘支持**: ESC键关闭模态框等快捷操作

## 文件修改说明

### 主要修改文件
- **templates/index.html**: 主页面文件，添加了产线Layout上传区域和相关JavaScript函数

### 新增的HTML结构
```html
<!-- 产线Layout上传区域 -->
<div class="preview-section" id="process-layout-section">
    <h3>🏭 产线Layout</h3>
    <div style="display: flex; margin-bottom: 20px; gap: 20px; min-height: 400px;">
        <!-- 左侧图片上传区域 -->
        <div style="flex: 2; ...">
            <h4>工艺布局图</h4>
            <div id="process-layout-preview">...</div>
            <div>
                <input type="file" id="process-layout-input" ...>
                <button onclick="document.getElementById('process-layout-input').click()">📁 上传图片</button>
                <button onclick="deleteProcessLayoutImage()">🗑️ 删除图片</button>
            </div>
        </div>
        
        <!-- 右侧参数区域 -->
        <div style="flex: 1; ...">
            <h4>产线参数</h4>
            <div>
                <label>长度(mm):</label>
                <input type="text" id="process-line-length" ...>
                <!-- 其他参数输入框 -->
            </div>
        </div>
    </div>
</div>
```

### 新增的JavaScript函数
1. **handleProcessLayoutUpload(event)**: 处理图片上传
2. **updateProcessLayoutPreview()**: 更新图片预览
3. **deleteProcessLayoutImage()**: 删除图片
4. **getProcessLayoutData()**: 获取所有数据
5. **setProcessLayoutData(data)**: 设置数据
6. **showToast(message, type)**: 显示提示消息
7. **showLoadingOverlay(message)**: 显示加载状态
8. **hideLoadingOverlay()**: 隐藏加载状态
9. **showImageModal(imageSrc)**: 显示图片模态框

## 布局设计

### 整体布局
```
┌─────────────────────────────────────────────────────────────────┐
│                        🏭 产线Layout                            │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────┐  ┌─────────────────────────────┐ │
│  │        工艺布局图           │  │        产线参数             │ │
│  │                             │  │                             │ │
│  │    [图片预览区域]           │  │  长度(mm): [_____________]  │ │
│  │                             │  │  宽度(mm): [_____________]  │ │
│  │                             │  │  高度(mm): [_____________]  │ │
│  │                             │  │  产线节拍(s): [__________]  │ │
│  │                             │  │                             │ │
│  │  [📁 上传图片] [🗑️ 删除]   │  │                             │ │
│  └─────────────────────────────┘  └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 响应式设计
- **桌面端**: 左右两栏布局，图片区域占2/3，参数区域占1/3
- **移动端**: 自动调整为上下布局，确保在小屏幕上的可用性

## 测试页面

创建了独立的测试页面 `test_process_layout.html` 用于验证功能：
- 包含完整的产线Layout上传功能
- 提供测试按钮用于验证数据获取和设置
- 可以独立运行，便于功能测试和调试

## 使用方法

### 1. 上传图片
1. 点击"📁 上传图片"按钮
2. 选择JPG或PNG格式的图片文件
3. 系统自动验证文件格式和大小
4. 上传成功后显示图片预览

### 2. 设置参数
1. 在右侧参数区域输入产线尺寸信息
2. 输入产线节拍时间
3. 数据会自动保存到本地存储

### 3. 查看和管理
1. 点击图片可放大查看
2. 点击"🗑️ 删除图片"可删除当前图片
3. 使用JavaScript函数可获取或设置数据

## 技术特点

1. **模块化设计**: 功能独立，易于维护和扩展
2. **数据持久化**: 支持数据的保存和恢复
3. **用户友好**: 丰富的交互反馈和状态提示
4. **兼容性好**: 支持现代浏览器的文件API
5. **性能优化**: 图片压缩和缓存机制

## 后续扩展建议

1. **数据导出**: 支持将Layout数据导出为JSON或其他格式
2. **批量上传**: 支持同时上传多张Layout图片
3. **图片编辑**: 集成简单的图片编辑功能（裁剪、旋转等）
4. **模板管理**: 支持保存和加载Layout模板
5. **云端存储**: 集成云端存储服务，支持跨设备同步

这个实现完全符合您的需求，提供了完整的产线Layout上传和管理功能，并且与现有系统完美集成。
