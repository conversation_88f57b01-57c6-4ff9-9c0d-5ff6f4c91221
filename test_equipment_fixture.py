#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Equipment和Fixture分离解析功能
"""

import re

def test_equipment_fixture_parsing():
    """测试Equipment和Fixture分离解析"""
    
    # 模拟智能匹配生成的完整设备内容
    equipment_content = """
#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯。
2. ST10工站配置支架压装、插线束高度检测以及发生器预装3小分站，可以独立进行作业。
3. 设备左右和前方需要3组安全光栅，硬件要接入安全回路进行控制。

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足。
2. 3个小分站的气动需要分开控制，气压表需要设置在正对人侧方便点检使用。

（三）防错及点检要求:
无

二、预装夹具-Fixture 
（一）机械要求:
1. 发生器检测到位选用大款传感器，检测整个端面。
2. 治具夹紧发生器后不晃动、松动。

（二）电气要求:
无

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

#### ST20 SAB-B-Folding
一、折叠设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯。
2. 设备前方需要2组安全光栅，硬件要接入安全回路进行控制。

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足。

（三）防错及点检要求:
无

二、折叠夹具-Fixture 
（一）机械要求:
1. 折尺尺寸根据实际产品图纸制作，折尺划槽座，划槽按统一标准设计。

（二）电气要求:
无

（三）防错及点检要求:
要求1：不同项目的折尺需防错
方案1：使用二维码防错

#### ST30 SAB-B-E-check
一、测厚设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯。
2. 设备前方需要1组安全光栅，硬件要接入安全回路进行控制。

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足。

（三）防错及点检要求:
无

二、测厚夹具-Fixture 
（一）机械要求:
1. 同一个项目左右产品使用，不同项目治具能实现快速换型。

（二）电气要求:
无

（三）防错及点检要求:
要求1：产品定位治具工装要互防
方案1：增加传感器防错
"""
    
    print("=" * 80)
    print("测试Equipment和Fixture分离解析")
    print("=" * 80)
    
    # 解析设备工站
    stations = extract_equipment_stations(equipment_content)
    
    print(f"解析到 {len(stations)} 个设备工站")
    
    for station in stations:
        print(f"\n工站: ST{station['station_number']} - {station['station_name']}")
        details = station['equipment_details']
        
        print(f"  设备类型: {details.get('equipment_type', '未识别')}")
        
        # Equipment部分
        print(f"\n  【设备要求 - Equipment】")
        print(f"    机械要求: {details.get('equipment_mechanical_requirements', '未识别')[:80]}...")
        print(f"    电气要求: {details.get('equipment_electrical_requirements', '未识别')[:80]}...")
        print(f"    防错要求: {details.get('equipment_error_prevention_requirements', '未识别')[:80]}...")
        
        # Fixture部分
        print(f"\n  【夹具要求 - Fixture】")
        print(f"    机械要求: {details.get('fixture_mechanical_requirements', '未识别')[:80]}...")
        print(f"    电气要求: {details.get('fixture_electrical_requirements', '未识别')[:80]}...")
        print(f"    防错要求: {details.get('fixture_error_prevention_requirements', '未识别')[:80]}...")
        
        # 合并字段
        print(f"\n  【合并字段】")
        print(f"    机械要求: {details.get('mechanical_requirements', '未识别')[:100]}...")
        print(f"    电气要求: {details.get('electrical_requirements', '未识别')[:100]}...")

def extract_equipment_stations(content):
    """提取设备工站信息"""
    stations = []
    
    # 查找工站
    markdown_pattern = r'####\s+ST(\d+)\s+([^\n]+)'
    markdown_matches = list(re.finditer(markdown_pattern, content))
    
    for match in markdown_matches:
        station_number = match.group(1)
        station_name = match.group(2).strip()
        
        # 提取工站内容
        start_pos = match.start()
        next_station_pattern = r"####\s+ST\d+"
        remaining_content = content[match.end():]
        
        end_pos = len(content)
        next_station_match = re.search(next_station_pattern, remaining_content)
        if next_station_match:
            end_pos = match.end() + next_station_match.start()
        
        station_content = content[start_pos:end_pos].strip()
        
        # 解析详细信息
        equipment_details = extract_equipment_details(station_content)
        
        stations.append({
            'station_number': station_number,
            'station_name': station_name,
            'content': station_content,
            'equipment_details': equipment_details
        })
    
    return stations

def extract_equipment_details(equipment_content):
    """提取设备详细信息，支持Equipment和Fixture分离"""
    details = {
        'equipment_type': '',
        
        # Equipment部分
        'equipment_mechanical_requirements': '',
        'equipment_electrical_requirements': '',
        'equipment_error_prevention_requirements': '',
        
        # Fixture部分
        'fixture_mechanical_requirements': '',
        'fixture_electrical_requirements': '',
        'fixture_error_prevention_requirements': '',
        
        # 合并字段
        'mechanical_requirements': '',
        'electrical_requirements': '',
        'error_prevention_requirements': ''
    }
    
    lines = equipment_content.split('\n')
    current_section = None
    current_subsection = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 识别主要部分
        if re.match(r'^[一二三四五六七八九十]、.*Equipment', line):
            current_section = 'equipment'
            # 提取设备类型
            if '设备' in line:
                equipment_name = line.split('、')[1].strip()
                details['equipment_type'] = equipment_name
            continue
        elif re.match(r'^[一二三四五六七八九十]、.*Fixture', line):
            current_section = 'fixture'
            continue
        
        # 识别子部分
        if re.match(r'^（[一二三四五六七八九十]）', line):
            if '机械要求' in line:
                current_subsection = 'mechanical'
            elif '电气要求' in line:
                current_subsection = 'electrical'
            elif '防错及点检要求' in line:
                current_subsection = 'error_prevention'
            continue
        
        # 收集内容
        if current_section and current_subsection and line and not line.startswith(('####', '###', '一、', '二、')):
            field_key = f"{current_section}_{current_subsection}_requirements"
            
            if details[field_key]:
                details[field_key] += '\n' + line
            else:
                details[field_key] = line
    
    # 合并Equipment和Fixture内容
    details['mechanical_requirements'] = combine_equipment_fixture_content(
        details['equipment_mechanical_requirements'],
        details['fixture_mechanical_requirements']
    )
    details['electrical_requirements'] = combine_equipment_fixture_content(
        details['equipment_electrical_requirements'],
        details['fixture_electrical_requirements']
    )
    details['error_prevention_requirements'] = combine_equipment_fixture_content(
        details['equipment_error_prevention_requirements'],
        details['fixture_error_prevention_requirements']
    )
    
    return details

def combine_equipment_fixture_content(equipment_content, fixture_content):
    """合并Equipment和Fixture的内容"""
    combined = []
    
    if equipment_content and equipment_content.strip():
        combined.append(f"【设备要求】\n{equipment_content.strip()}")
    
    if fixture_content and fixture_content.strip():
        combined.append(f"【夹具要求】\n{fixture_content.strip()}")
    
    return '\n\n'.join(combined)

def test_content_structure():
    """测试内容结构验证"""
    
    print("\n" + "=" * 80)
    print("测试内容结构验证")
    print("=" * 80)
    
    # 验证每个工站都有Equipment和Fixture
    test_content = """
#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备要求1
（二）电气要求:
1. 电气要求1
（三）防错及点检要求:
无

二、预装夹具-Fixture 
（一）机械要求:
1. 夹具要求1
（二）电气要求:
无
（三）防错及点检要求:
要求1：防错要求1
"""
    
    stations = extract_equipment_stations(test_content)
    
    for station in stations:
        details = station['equipment_details']
        print(f"\n工站: ST{station['station_number']}")
        
        # 检查Equipment部分
        has_equipment = any([
            details.get('equipment_mechanical_requirements'),
            details.get('equipment_electrical_requirements'),
            details.get('equipment_error_prevention_requirements')
        ])
        
        # 检查Fixture部分
        has_fixture = any([
            details.get('fixture_mechanical_requirements'),
            details.get('fixture_electrical_requirements'),
            details.get('fixture_error_prevention_requirements')
        ])
        
        print(f"  Equipment部分: {'✓' if has_equipment else '✗'}")
        print(f"  Fixture部分: {'✓' if has_fixture else '✗'}")
        
        if has_equipment and has_fixture:
            print(f"  结构完整: ✓")
        else:
            print(f"  结构完整: ✗")

if __name__ == "__main__":
    test_equipment_fixture_parsing()
    test_content_structure()
    
    print("\n" + "=" * 80)
    print("Equipment和Fixture分离解析测试完成")
    print("=" * 80)
