<指令>
作为高级工程师，请基于已知信息执行以下结构化分析：根据输入的产品族Product family和含有的零件BOM，匹配该项目所属的工艺类型，根据不同工艺类型对应的工艺信息输出推荐的工艺部分和设备部分。

## 输出要求：
1. **内容结构**：工艺部分和设备部分必须完全对应，确保每个工站在两部分都有相应内容
2. **格式要求**：使用Markdown格式，严格按照以下结构输出
3. **工站对应**：工艺部分的每个工站都必须在设备部分有对应的Equipment和Fixture信息
4. **SAB类别支持**：支持A、B、C、D、E、F所有类别的SAB产品分析

## 输出格式模板：
```markdown
### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

2. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

#### ST15 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]
2. [具体要求]

（二）电气要求:
1. [具体要求]
2. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]
2. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

#### ST15 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]
```

## 重要说明：
- 必须确保工艺部分和设备部分的工站号完全对应
- 每个工站的设备部分必须包含Equipment和Fixture两个子部分
- 每个子部分必须包含机械要求、电气要求、防错及点检要求三个方面
- 严格按照Markdown格式输出，使用正确的标题层级
- 根据不同SAB类别（A-F）提供相应的工艺和设备配置

### 下列为输入输出示例
输入：帮我写一份SAB的Linespec，零件为：cushion、inflator、deflector、Harness、softcover。
输出：
根据输入的零件，按照SAB 模块的工艺类型与零件的对应关系得知：零件为cushion、inflator、deflector、Harness、softcover的该SAB项目的工艺类型为B类。生成Linespec包含工艺部分和设备部分，两部分内容都按照工站号描述如下：

### 一、工艺部分

#### ST10 SAB-B-Pre-assembly
（一）
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: 导流片无错装、漏装、安装方向正确
   - 过程防错要求: 通过MSA

2. 工艺过程描述: 将发生器放入夹具，设备自动夹紧发生器，自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器
   - 过程防错要求: 无夹伤，发生器正确，发生器插入方向正确，发生器螺柱方向正确

#### ST15 SAB-B-Harness Assembly
（一）
1. 工艺过程描述: 将线束保护支架预装在发生器上，将发生器固定在工装上，下压工装安装支架
   - 人or设备: 人
   - 产品特性要求: 正确的支架
   - 过程防错要求: 无夹伤，支架正确，支架安装方向和位置正确

2. 工艺过程描述: 拿取气袋，将发生器安装进气袋
   - 人or设备: 人
   - 产品特性要求: 发生器装入气袋方向正确
   - 过程防错要求: 不伤螺丝

#### ST20 SAB-B-Folding
（一）
1. 工艺过程描述: 扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器

2. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋

3. 工艺过程描述: 将气袋拉直，上夹具夹紧气袋
   - 人or设备: 人
   - 产品特性要求: 夹具光滑无毛刺，向上拉直需要左右上下可调

4. 工艺过程描述: 退出光栅，踩脚踏，设备自动进行折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠方式，气袋无破损，无异物
   - 过程防错要求: 折叠动作要同步，折叠尺尽量不晃动，折叠尺可换，折叠尺防错

5. 工艺过程描述: 踩脚踏，取下组件，整理好包布，扣入螺柱，送入下一工位
   - 人or设备: 人

#### ST30 SAB-B-Package
（一）
1. 工艺过程描述: 拿取产品放入夹具，气缸夹紧螺柱
   - 人or设备: 设备

2. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备

3. 工艺过程描述: 高度检测
   - 人or设备: 设备
   - 产品特性要求: 正确的模块尺寸

4. 工艺过程描述: 打印客户标签，正确的条码
   - 人or设备: 设备

5. 工艺过程描述: 粘贴客户标签
   - 人or设备: 设备
   - 产品特性要求: 正确的客户标签内容
   - 过程防错要求: 条码阈值，条码数量，条码角度可控制

6. 工艺过程描述: 按双手按钮，扫描客户标签条码，释放组件，包装
   - 人or设备: 设备

### 二、设备部分

#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部
2. ST10工站配置支架压装、插线束高度检测以及发生器预装3小分站

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地
2. 3个小分站的气动需要分开控制，气压表需要设置在正对人侧

（三）防错及点检要求:
无

二、预装夹具-Fixture 
（一）机械要求:
1. 发生器检测到位选用大款传感器，检测整个端面
2. 治具夹紧发生器后不晃动、松动

（二）电气要求:
无

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

#### ST15 SAB-B-Harness Assembly
一、线束装配设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
2. 配置线束装配工位和检测功能

（二）电气要求:
1. 电控系统满足安全要求
2. 气动控制系统独立控制

（三）防错及点检要求:
要求1：线束装配方向和位置正确
方案1：通过传感器和视觉系统检测

二、线束装配夹具-Fixture 
（一）机械要求:
1. 支架定位准确，夹紧可靠
2. 气袋装配导向功能完善

（二）电气要求:
无

（三）防错及点检要求:
要求1：支架和气袋装配防错
方案1：增加传感器检测装配状态

#### ST20 SAB-B-Folding
一、折叠设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
2. 设备前方需要2组安全光栅

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地

（三）防错及点检要求:
无

二、折叠夹具-Fixture 
（一）机械要求:
1. 折尺尺寸根据实际产品图纸制作，折尺划槽座，划槽按统一标准设计

（二）电气要求:
无

（三）防错及点检要求:
要求1：不同项目的折尺需防错
方案1：使用二维码防错

#### ST30 SAB-B-Package
一、测厚设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部
2. 设备前方需要1组安全光栅，硬件要接入安全回路进行控制

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地

（三）防错及点检要求:
无

二、测厚夹具-Fixture 
（一）机械要求:
1. 同一个项目左右产品使用，不同项目治具能实现快速换型

（二）电气要求:
无

（三）防错及点检要求:
要求1：产品定位治具工装要互防
方案1：增加传感器防错

</指令>

<已知信息>{{ context }}</已知信息>
<问题>{{ question }}</问题>
