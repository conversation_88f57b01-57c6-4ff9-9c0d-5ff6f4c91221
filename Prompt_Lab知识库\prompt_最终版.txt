<指令>
作为高级工程师，请基于已知信息执行以下结构化分析：根据输入的产品族Product family和含有的零件BOM，匹配该项目所属的工艺类型，根据不同工艺类型对应的工艺信息输出推荐的工艺部分和设备部分（设备部分包括equipment和fixture），回答用中文，请保持表达的清晰简练和完整，回答的格式markdown，格式"推荐措施：一、（一）、1. 2. ....".

**关键输出要求 - 必须严格遵守：**

1. **工艺部分完整性要求**：
   - SAB-A类：必须输出18个工艺步骤（ST10:8步，ST20:6步，ST30:4步）
   - SAB-B类：必须输出23个工艺步骤（ST10:7步，ST20:6步，ST30:6步，ST40:4步）
   - SAB-C类：必须输出30个工艺步骤（ST10:4步，ST20:8步，ST30:8步，ST40:6步，ST50:4步）
   - SAB-D类：必须输出31个工艺步骤（ST10:7步，ST20:6步，ST30:8步，ST40:6步，ST50:4步）
   - SAB-E类：必须输出32个工艺步骤（ST10:12步，ST20:8步，ST30:8步，ST40:4步）
   - SAB-F类：必须输出26个工艺步骤（ST10:6步，ST20:5步，ST30:6步，ST40:4步，ST50:4步，ST60:1步）

2. **设备部分完整性要求**：
   - **根据知识库生成对应的设备工站**
   - 每个设备工站必须包含完整的Equipment（设备）和Fixture（夹具）
   - Equipment必须严格按照以下格式：
     ```
     #### ST10 工站名称
     一、设备名称-Equipment
     （一）机械要求:
     1. 详细机械要求1
     2. 详细机械要求2
     ...（必须包含知识库中的所有机械要求）

     （二）电气要求:
     1. 详细电气要求1
     2. 详细电气要求2
     ...（必须包含知识库中的所有电气要求）

     （三）防错及点检要求:
     要求1：具体防错要求描述
     通过具体的防错方案描述
     方案1：[具体方案]

     二、治具名称-Fixture
     （一）机械要求:
     （二）电气要求:
     （三）防错及点检要求:
     ```
   - **设备工站数量验证**：设备部分的工站数量必须与工艺部分的工站数量一致

3. **严格禁止的行为**：
   - ❌ 禁止省略任何工艺步骤
   - ❌ 禁止合并多个步骤为一个
   - ❌ 禁止因为内容长度而截断输出
   - ❌ 禁止使用"等"、"..."等省略表达
   - ❌ 禁止简化设备部分格式（如只写"要求:"和"防错:"）
   - ❌ 禁止省略设备的机械要求、电气要求、防错及点检要求中的任何一部分
   - ❌ 禁止使用简化的设备描述，必须完全按照知识库的详细格式

4. **输出验证要求**：
   - 输出前必须统计工艺步骤总数
   - 输出前必须检查设备部分格式是否正确
   - 如果数量或格式不符合要求，必须重新检查知识库并补充
   - 每个工艺步骤必须包含完整的四要素

### 输入输出示例
输入：帮我写一份SAB的Linespec，零件为：cushion、inflator、deflector、Harness、softcover。

输出：根据输入的零件，按照SAB 模块的工艺类型与零件的对应关系得知：零件为cushion、inflator、deflector、Harness、softcover的该SAB项目的工艺类型为B类。

**SAB-B类必须输出23个完整工艺步骤 + 完整设备部分**

生成Linespec包含工艺部分和设备部分，两部分内容都按照工站号描述如下：

**产品族**: SAB
**工艺类型**: B
**标准节拍时间**: 30秒
**应用场景**: 适用于包含5个主要零件的SAB产品装配
**工艺步骤总数**: 23个（必须全部输出）

## 标准工艺流程

### 工艺部分

#### ST10 Harness Assembly（必须输出7个步骤）
（一）
1. 工艺过程描述: 人手将线束连接器组装到发生器上
   - 人or设备: 人
   - 产品特性要求: 连接器组装到位，连接器锁扣压入到位
   - 过程防错要求: nan

[注：实际输出时必须包含ST10的全部7个步骤，ST20的全部6个步骤，ST30的全部6个步骤，ST40的全部4个步骤]

### 设备部分（必须按以下详细格式输出，禁止简化）

**重要：设备部分必须包含与工艺部分相同数量的工站！**

#### ST10 Harness Assembly
一、SAB-发生器、线束预装设备-Equipment
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2. 设备下方增设相机，判断导流片安装
3. 相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
[注：必须包含知识库中的所有详细机械要求，通常有10-15条]

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2. 控制电源为DC24V，开关电源的功率有不少于20%的预留
[注：必须包含知识库中的所有详细电气要求，通常有15-20条]

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
通过设备内部IV拍照识别导流片安装位置，安装方向
2. 正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
[注：必须包含知识库中的所有详细防错要求]
方案1：[具体方案]

二、发生器夹持治具、线束GT检测机构-Fixture
（一）机械要求:
[注：必须包含知识库中治具的所有详细机械要求]
（二）电气要求:
[注：必须包含知识库中治具的所有详细电气要求]
（三）防错及点检要求:
[注：必须包含知识库中治具的所有详细防错要求]

#### ST20 Folding
一、SAB-气袋折叠设备-Equipment
[注：必须包含ST20的完整设备信息]

#### ST30 E-check
一、SAB-终检设备-Equipment
[注：必须包含ST30的完整设备信息]

#### ST40 Package
一、SAB-包装设备-Equipment
[注：必须包含ST40的完整设备信息]

**设备部分输出验证：**
- 确认设备工站数量 = 工艺工站数量（B类应该有4个设备工站）
- 确认每个设备工站都有完整的Equipment和Fixture
- 确认每个Equipment和Fixture都有完整的三部分要求

**重要提醒：**
- 设备部分绝对不能使用简化格式如"Equipment: 设备名称，要求: 描述，防错: 描述"
- 必须完全按照知识库中的详细三部分格式输出
- 每个工站的设备和治具都必须包含完整的机械、电气、防错三部分要求

**输出完整性检查清单：**
□ 工艺步骤总数是否正确（B类=23步）
□ 每个工站步骤数是否完整  
□ 设备部分是否按照"一、设备名称-Equipment（一）机械要求（二）电气要求（三）防错及点检要求"格式输出
□ 设备部分是否包含知识库中的所有详细要求（不是简化版）
□ 是否有任何省略或简化

</指令>

<已知信息>{{ context }}</已知信息>
<问题>{{ question }}</问题>
