<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产线Layout上传功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .btn.primary {
            background: #1a73e8;
            color: white;
        }
        
        .btn.primary:hover {
            background: #1557b0;
        }
        
        /* 加载动画和状态提示 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            backdrop-filter: blur(2px);
        }

        .loading-content {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #1a73e8;
            font-weight: 500;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>产线Layout上传功能测试</h1>
        
        <!-- 产线Layout上传区域 -->
        <div id="process-layout-section">
            <h3>🏭 产线Layout</h3>
            <div style="display: flex; margin-bottom: 20px; gap: 20px; min-height: 400px;">
                <!-- 左侧图片上传区域 -->
                <div style="flex: 2; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: flex; flex-direction: column;">
                    <h4 style="margin-bottom: 15px; font-size: 16px; color: #333;">工艺布局图</h4>
                    <div id="process-layout-preview" style="flex: 1; overflow: hidden; text-align: center; margin-bottom: 15px; min-height: 300px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                        <div style="color: #999;">
                            <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                            <p>点击下方按钮上传产线Layout图</p>
                            <small>支持 JPG, PNG 格式图片</small>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: center; gap: 10px;">
                        <input type="file" id="process-layout-input" accept="image/*" style="display: none;" onchange="handleProcessLayoutUpload(event)">
                        <button onclick="document.getElementById('process-layout-input').click()" class="btn primary">
                            📁 上传图片
                        </button>
                        <button onclick="deleteProcessLayoutImage()" class="btn" style="background: #ff7875; color: white; display: none;" id="delete-process-layout-btn">
                            🗑️ 删除图片
                        </button>
                    </div>
                </div>
                
                <!-- 右侧参数区域 -->
                <div style="flex: 1; background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <h4 style="margin-bottom: 15px; font-size: 16px; color: #333;">产线参数</h4>
                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">长度(mm):</label>
                            <input type="text" id="process-line-length" placeholder="请输入产线长度" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">宽度(mm):</label>
                            <input type="text" id="process-line-width" placeholder="请输入产线宽度" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">高度(mm):</label>
                            <input type="text" id="process-line-height" placeholder="请输入产线高度" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">产线节拍(s):</label>
                            <input type="text" id="process-line-takt" placeholder="请输入产线节拍" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试按钮 -->
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="testGetData()" class="btn primary" style="margin-right: 10px;">获取数据</button>
            <button onclick="testSetData()" class="btn primary">设置测试数据</button>
        </div>
        
        <!-- 数据显示区域 -->
        <div id="data-display" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; display: none;">
            <h4>当前数据：</h4>
            <pre id="data-content"></pre>
        </div>
    </div>

    <!-- 加载遮罩层 -->
    <div class="loading-overlay" id="loading-overlay" style="display:none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loading-text">正在智能分析中...</div>
        </div>
    </div>

    <script>
        // 产线Layout数据存储
        window.processLayoutData = {};

        // ====== 产线Layout相关函数 ======
        /**
         * 处理产线Layout图片上传
         */
        function handleProcessLayoutUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showToast('请选择图片文件（JPG、PNG格式）', 'error');
                event.target.value = '';
                return;
            }

            // 验证文件大小（限制为10MB）
            if (file.size > 10 * 1024 * 1024) {
                showToast('图片文件大小不能超过10MB', 'error');
                event.target.value = '';
                return;
            }

            // 显示加载状态
            showLoadingOverlay('正在上传产线Layout图片...');

            const reader = new FileReader();
            reader.onload = function(e) {
                // 保存图片数据
                window.processLayoutData.layoutImage = e.target.result;
                window.processLayoutData.fileName = file.name;
                window.processLayoutData.fileSize = file.size;
                window.processLayoutData.uploadTime = new Date().toISOString();

                // 更新预览
                updateProcessLayoutPreview();
                
                // 显示删除按钮
                const deleteBtn = document.getElementById('delete-process-layout-btn');
                if (deleteBtn) {
                    deleteBtn.style.display = 'inline-block';
                }

                hideLoadingOverlay();
                showToast('产线Layout图片上传成功！', 'success');
                
                console.log('产线Layout上传成功:', file.name);
            };

            reader.onerror = function() {
                hideLoadingOverlay();
                showToast('图片读取失败，请重试', 'error');
                event.target.value = '';
            };

            reader.readAsDataURL(file);
        }

        /**
         * 更新产线Layout预览
         */
        function updateProcessLayoutPreview() {
            const previewArea = document.getElementById('process-layout-preview');
            if (!previewArea) return;

            if (window.processLayoutData && window.processLayoutData.layoutImage) {
                previewArea.innerHTML = `
                    <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                        <img src="${window.processLayoutData.layoutImage}" 
                             style="max-width: 100%; max-height: 100%; object-fit: contain; cursor: pointer; border-radius: 4px;" 
                             onclick="showImageModal(this.src)" 
                             alt="产线Layout图">
                        <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                            ${window.processLayoutData.fileName}
                        </div>
                    </div>
                `;
            } else {
                previewArea.innerHTML = `
                    <div style="color: #999;">
                        <div style="font-size: 48px; margin-bottom: 10px;">📋</div>
                        <p>点击下方按钮上传产线Layout图</p>
                        <small>支持 JPG, PNG 格式图片</small>
                    </div>
                `;
            }
        }

        /**
         * 删除产线Layout图片
         */
        function deleteProcessLayoutImage() {
            if (!window.processLayoutData || !window.processLayoutData.layoutImage) {
                return;
            }

            if (confirm('确定要删除当前的产线Layout图片吗？')) {
                // 清除数据
                window.processLayoutData = {};
                
                // 更新预览
                updateProcessLayoutPreview();
                
                // 隐藏删除按钮
                const deleteBtn = document.getElementById('delete-process-layout-btn');
                if (deleteBtn) {
                    deleteBtn.style.display = 'none';
                }
                
                // 清除文件输入
                const inputElement = document.getElementById('process-layout-input');
                if (inputElement) {
                    inputElement.value = '';
                }
                
                showToast('产线Layout图片已删除', 'success');
                console.log('产线Layout图片已删除');
            }
        }

        /**
         * 获取产线Layout数据
         */
        function getProcessLayoutData() {
            const data = {
                layoutImage: window.processLayoutData?.layoutImage || '',
                fileName: window.processLayoutData?.fileName || '',
                lineLength: document.getElementById('process-line-length')?.value || '',
                lineWidth: document.getElementById('process-line-width')?.value || '',
                lineHeight: document.getElementById('process-line-height')?.value || '',
                lineTakt: document.getElementById('process-line-takt')?.value || ''
            };
            return data;
        }

        /**
         * 设置产线Layout数据
         */
        function setProcessLayoutData(data) {
            if (data.layoutImage) {
                window.processLayoutData = {
                    layoutImage: data.layoutImage,
                    fileName: data.fileName || '产线Layout图.jpg'
                };
                updateProcessLayoutPreview();
                
                const deleteBtn = document.getElementById('delete-process-layout-btn');
                if (deleteBtn) {
                    deleteBtn.style.display = 'inline-block';
                }
            }
            
            // 设置参数值
            if (data.lineLength) {
                const lengthInput = document.getElementById('process-line-length');
                if (lengthInput) lengthInput.value = data.lineLength;
            }
            if (data.lineWidth) {
                const widthInput = document.getElementById('process-line-width');
                if (widthInput) widthInput.value = data.lineWidth;
            }
            if (data.lineHeight) {
                const heightInput = document.getElementById('process-line-height');
                if (heightInput) heightInput.value = data.lineHeight;
            }
            if (data.lineTakt) {
                const taktInput = document.getElementById('process-line-takt');
                if (taktInput) taktInput.value = data.lineTakt;
            }
        }

        // ====== 辅助函数 ======
        /**
         * 显示Toast提示消息
         */
        function showToast(message, type = 'info') {
            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = 'toast-message';
            toast.textContent = message;
            
            // 设置样式
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#ff4d4f' : type === 'success' ? '#52c41a' : '#1890ff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-size: 14px;
                max-width: 300px;
                word-wrap: break-word;
                animation: slideInRight 0.3s ease;
            `;
            
            // 添加动画样式
            if (!document.querySelector('#toast-styles')) {
                const style = document.createElement('style');
                style.id = 'toast-styles';
                style.textContent = `
                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }
                    @keyframes slideOutRight {
                        from {
                            transform: translateX(0);
                            opacity: 1;
                        }
                        to {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                    }
                `;
                document.head.appendChild(style);
            }
            
            // 添加到页面
            document.body.appendChild(toast);
            
            // 3秒后自动移除
            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        /**
         * 显示加载遮罩层
         */
        function showLoadingOverlay(message = '正在处理中...') {
            const overlay = document.getElementById('loading-overlay');
            const text = document.getElementById('loading-text');
            
            if (overlay && text) {
                text.textContent = message;
                overlay.style.display = 'flex';
            }
        }

        /**
         * 隐藏加载遮罩层
         */
        function hideLoadingOverlay() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        /**
         * 显示图片模态框
         */
        function showImageModal(imageSrc) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10001;
                cursor: pointer;
            `;
            
            // 创建图片元素
            const img = document.createElement('img');
            img.src = imageSrc;
            img.style.cssText = `
                max-width: 90%;
                max-height: 90%;
                object-fit: contain;
                border-radius: 8px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            `;
            
            modal.appendChild(img);
            document.body.appendChild(modal);
            
            // 点击关闭
            modal.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
            
            // ESC键关闭
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    document.body.removeChild(modal);
                    document.removeEventListener('keydown', handleKeydown);
                }
            };
            document.addEventListener('keydown', handleKeydown);
        }

        // ====== 测试函数 ======
        function testGetData() {
            const data = getProcessLayoutData();
            const display = document.getElementById('data-display');
            const content = document.getElementById('data-content');
            
            content.textContent = JSON.stringify(data, null, 2);
            display.style.display = 'block';
            
            console.log('当前产线Layout数据:', data);
        }

        function testSetData() {
            const testData = {
                lineLength: '5000',
                lineWidth: '2000',
                lineHeight: '2500',
                lineTakt: '60'
            };
            
            setProcessLayoutData(testData);
            showToast('测试数据已设置', 'success');
        }
    </script>
</body>
</html>
