#!/usr/bin/env python3
"""
测试HTML结构变更
验证产品附件从工艺页面移动到产品信息页面的HTML结构是否正确
"""

import re
import requests

def test_html_structure():
    """测试HTML结构是否正确"""
    
    print("=== 测试HTML结构变更 ===\n")
    
    try:
        # 获取页面HTML
        response = requests.get('http://localhost:5000', timeout=10)
        if response.status_code != 200:
            print(f"❌ 无法访问页面，状态码: {response.status_code}")
            return False
            
        html_content = response.text
        print("✅ 成功获取页面HTML")
        
        # 1. 检查产品信息页面是否包含产品附件区域
        print("\n1. 检查产品信息页面...")
        
        # 查找产品信息页面的开始和结束
        product_page_pattern = r'<div class="preview-page" id="page-product".*?</div>\s*<!-- 目录2：工艺要求 -->'
        product_page_match = re.search(product_page_pattern, html_content, re.DOTALL)
        
        if product_page_match:
            product_page_content = product_page_match.group(0)
            print("✅ 找到产品信息页面")
            
            # 检查是否包含产品附件区域
            if 'product-attachments-section' in product_page_content:
                print("✅ 产品信息页面包含产品附件区域")
                
                # 检查三个附件类型
                attachment_types = [
                    'explosion-diagram-input',
                    'folding-diagram-input',
                    'assembly-diagram-input'
                ]
                
                for attachment_type in attachment_types:
                    if attachment_type in product_page_content:
                        print(f"✅ 产品信息页面包含 {attachment_type}")
                    else:
                        print(f"❌ 产品信息页面缺少 {attachment_type}")
                        
            else:
                print("❌ 产品信息页面不包含产品附件区域")
                return False
        else:
            print("❌ 未找到产品信息页面")
            return False
        
        # 2. 检查工艺页面是否移除了产品附件区域
        print("\n2. 检查工艺页面...")
        
        # 查找工艺页面的开始和结束
        process_page_pattern = r'<div class="preview-page" id="page-process".*?</div>\s*<!-- 目录3：设备要求 -->'
        process_page_match = re.search(process_page_pattern, html_content, re.DOTALL)
        
        if process_page_match:
            process_page_content = process_page_match.group(0)
            print("✅ 找到工艺页面")
            
            # 检查是否还包含产品附件区域（应该不包含）
            if 'product-attachments-section' not in process_page_content:
                print("✅ 工艺页面已成功移除产品附件区域")
            else:
                print("❌ 工艺页面仍然包含产品附件区域")
                return False
                
        else:
            print("❌ 未找到工艺页面")
            return False
        
        # 3. 检查JavaScript文件是否正确引入
        print("\n3. 检查JavaScript文件...")
        
        if 'product_attachments.js' in html_content:
            print("✅ product_attachments.js 文件已正确引入")
        else:
            print("❌ product_attachments.js 文件未引入")
            return False
        
        # 4. 检查CSS样式是否存在
        print("\n4. 检查CSS样式...")
        
        css_patterns = [
            r'\.attachments-container\s*{',
            r'\.attachment-item\s*{',
            r'\.attachment-header\s*{',
            r'\.upload-btn\s*{'
        ]
        
        for pattern in css_patterns:
            if re.search(pattern, html_content):
                print(f"✅ CSS样式 {pattern} 存在")
            else:
                print(f"❌ CSS样式 {pattern} 不存在")
        
        # 5. 统计附件相关元素
        print("\n5. 统计附件相关元素...")
        
        # 统计各种元素的数量
        stats = {
            'product-attachments-section': len(re.findall(r'id="product-attachments-section"', html_content)),
            'explosion-diagram-input': len(re.findall(r'id="explosion-diagram-input"', html_content)),
            'folding-diagram-input': len(re.findall(r'id="folding-diagram-input"', html_content)),
            'assembly-diagram-input': len(re.findall(r'id="assembly-diagram-input"', html_content)),
            'upload-btn': len(re.findall(r'class="upload-btn"', html_content)),
            'remove-btn': len(re.findall(r'class="remove-btn"', html_content))
        }
        
        for element, count in stats.items():
            if element.endswith('-input') and count == 1:
                print(f"✅ {element}: {count} 个（正确）")
            elif element == 'product-attachments-section' and count == 1:
                print(f"✅ {element}: {count} 个（正确）")
            elif element in ['upload-btn', 'remove-btn'] and count == 3:
                print(f"✅ {element}: {count} 个（正确）")
            else:
                print(f"⚠️ {element}: {count} 个（可能异常）")
        
        print("\n=== HTML结构测试完成 ===")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
        print("请确保Flask应用正在运行 (python app.py)")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def test_functionality_integration():
    """测试功能集成"""
    print("\n=== 测试功能集成 ===")
    
    try:
        # 读取HTML文件内容
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查关键JavaScript函数调用
        js_functions = [
            'handleFileUpload',
            'removeAttachment', 
            'clearAllAttachments',
            'exportAttachmentsData'
        ]
        
        for func in js_functions:
            if func in html_content:
                print(f"✅ JavaScript函数 {func} 在HTML中被调用")
            else:
                print(f"❌ JavaScript函数 {func} 在HTML中未被调用")
        
        # 检查事件绑定
        event_patterns = [
            r'onchange="handleFileUpload\(',
            r'onclick="removeAttachment\(',
            r'onclick="clearAllAttachments\(',
            r'onclick="exportAttachmentsData\('
        ]
        
        for pattern in event_patterns:
            matches = re.findall(pattern, html_content)
            if matches:
                print(f"✅ 事件绑定 {pattern} 找到 {len(matches)} 个")
            else:
                print(f"❌ 事件绑定 {pattern} 未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能集成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试产品附件移动功能...\n")
    
    # 运行HTML结构测试
    structure_ok = test_html_structure()
    
    # 运行功能集成测试
    integration_ok = test_functionality_integration()
    
    print(f"\n{'='*50}")
    if structure_ok and integration_ok:
        print("🎉 所有测试通过！")
        print("✅ 产品附件已成功从工艺页面移动到产品信息页面")
        print("✅ HTML结构正确")
        print("✅ JavaScript功能集成正常")
        print("✅ CSS样式完整")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    print(f"{'='*50}")
