# AI生成内容自动分割功能使用说明

## 功能概述

本系统已升级支持AI生成内容的自动识别和分割，能够将AI返回的内容自动分配到对应的模块框内，包括：
- **工艺部分**：自动识别并填充到工艺要求模块
- **设备部分**：自动识别并填充到设备要求模块
- **产品信息**：其他相关信息填充到产品信息模块

## 新增功能特性

### 1. 智能内容解析
- 自动识别AI生成内容中的工艺部分和设备部分
- 支持标准格式标记解析（推荐）
- 支持关键词智能分割（备用方案）

### 2. 标准格式Prompt生成
- 根据产品类型和零件列表自动生成标准格式的查询prompt
- 确保AI返回内容符合解析要求
- 自动匹配工艺类型

### 3. 工站信息提取
- 自动提取工站号（ST10、ST15等）
- 结构化显示工艺信息

## 使用方法

### 方法一：使用标准格式Prompt（推荐）

1. **在产品信息页面**：
   - 选择产品类型（SAB、DAB等）
   - 选择模板分类（A、B、C等）
   - 添加零件信息
   - 点击"一键生成"按钮

2. **系统自动操作**：
   - 生成标准格式的prompt
   - 自动填入聊天输入框
   - prompt包含格式标记，确保AI返回标准格式内容

3. **发送查询**：
   - 点击发送按钮
   - AI返回标准格式内容
   - 系统自动解析并分割到对应模块

### 方法二：手动输入查询

如果手动输入查询，建议在prompt中包含以下要求：

```
请按照以下格式返回内容：

【工艺部分】
ST10 [工站名称]
1. 工艺过程描述: [详细描述]
人or设备: [人工/设备]
产品特性要求: [质量要求]
过程防错要求: [防错措施]
【工艺部分结束】

【设备部分】
ST10设备信息
设备类型: [设备类型]
技术要求: [技术要求]
【设备部分结束】
```

## AI生成内容格式要求

### 标准格式（推荐）

AI生成的内容应包含以下标记：

```
【工艺部分】
ST10 SAB-B-Pre-assembly
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
人or设备: 人
产品特性要求: 导流片无错装、漏装、安装方向正确
过程防错要求: 通过MSA

ST15 SAB-B-Assembly
1. 工艺过程描述: 将线束保护支架预装在发生器上
人or设备: 人
产品特性要求: 正确的支架
过程防错要求: 无夹伤，支架正确
【工艺部分结束】

【设备部分】
ST10设备信息
设备类型: 手动装配工位
技术要求: 提供导流片定位夹具
设备参数: 工作台高度800mm
安全要求: 防静电措施

ST15设备信息
设备类型: 半自动装配设备
技术要求: 支架安装压力控制
设备参数: 压力范围50-100N
安全要求: 双手启动按钮
【设备部分结束】
```

### 关键词识别（备用方案）

如果AI返回的内容没有标准标记，系统会根据以下关键词进行智能分割：

**工艺部分关键词**：
- 工艺过程描述
- 人or设备
- 产品特性要求
- 过程防错要求
- ST[数字]

**设备部分关键词**：
- 设备信息
- 设备类型
- 技术要求
- 设备参数
- ST[数字]设备

## 数据处理建议

### 1. 现有知识库优化

当前知识库是Excel转PDF的简单格式，建议优化为：

**结构化数据格式**：
```json
{
  "product_type": "SAB",
  "process_type": "B",
  "stations": [
    {
      "station_id": "ST10",
      "station_name": "SAB-B-Pre-assembly",
      "processes": [
        {
          "description": "拿取导流片，将导流片安装到发生器上",
          "operator": "人",
          "quality_requirements": "导流片无错装、漏装、安装方向正确",
          "error_prevention": "通过MSA"
        }
      ],
      "equipment": {
        "type": "手动装配工位",
        "requirements": "提供导流片定位夹具",
        "parameters": "工作台高度800mm",
        "safety": "防静电措施"
      }
    }
  ]
}
```

### 2. Prompt优化建议

**当前Prompt问题**：
- 格式不够标准化
- 缺少明确的分割标记
- 工艺和设备信息混合

**改进后的Prompt特点**：
- 包含明确的格式标记
- 结构化的信息组织
- 自动匹配工艺类型
- 标准化的字段要求

## 技术实现

### 后端组件
- `utils/content_parser.py`: 内容解析器
- `utils/prompt_generator.py`: Prompt生成器
- `config/content_format_template.json`: 格式配置
- `config/improved_prompt_template.txt`: Prompt模板

### 前端功能
- 自动内容分割显示
- 标准格式Prompt生成
- 智能错误处理

### API端点
- `/generate_prompt`: 生成标准格式prompt
- `/chat`: 增强的聊天接口，支持内容解析

## 故障排除

### 常见问题

1. **内容没有自动分割**
   - 检查AI返回内容是否包含标准标记
   - 查看浏览器控制台是否有解析错误
   - 尝试使用标准格式Prompt重新查询

2. **工站信息显示不完整**
   - 确认AI返回内容包含完整的工站信息
   - 检查工站号格式是否为ST+数字

3. **Prompt生成失败**
   - 确认已选择产品类型
   - 确认已添加零件信息
   - 检查网络连接

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console标签页的错误信息
3. 检查Network标签页的API请求响应
4. 查看后端日志输出

## 未来改进方向

1. **增加更多产品类型支持**
2. **优化内容解析算法**
3. **支持自定义格式配置**
4. **增加内容质量检查**
5. **支持批量处理**
