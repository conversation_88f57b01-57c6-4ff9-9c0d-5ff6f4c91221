
// 产品附件功能测试代码
// 在浏览器控制台中运行此代码来测试附件功能

console.log("开始测试产品附件功能...");

// 测试数据
const testAttachments = {
    "explosion-diagram": {
        "file": {
            "name": "explosion_diagram.png",
            "type": "image/png",
            "size": 15234,
            "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "uploadTime": "2025-06-27T08:30:00.000Z"
        },
        "notes": "这是产品爆炸图的测试数据"
    },
    "folding-diagram": {
        "file": {
            "name": "folding_diagram.png", 
            "type": "image/png",
            "size": 12456,
            "data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "uploadTime": "2025-06-27T08:30:00.000Z"
        },
        "notes": "这是产品折叠图的测试数据"
    }
};

// 测试函数
function testProductAttachments() {
    console.log("1. 测试设置附件数据...");
    
    if (typeof setAllAttachments === 'function') {
        setAllAttachments(testAttachments);
        console.log("✅ 附件数据设置成功");
    } else {
        console.log("❌ setAllAttachments 函数不存在");
    }
    
    console.log("2. 测试获取附件数据...");
    
    if (typeof getAllAttachments === 'function') {
        const attachments = getAllAttachments();
        console.log("✅ 获取到附件数据:", attachments);
    } else {
        console.log("❌ getAllAttachments 函数不存在");
    }
    
    console.log("3. 测试导出附件数据...");
    
    if (typeof exportAttachmentsData === 'function') {
        const exportData = exportAttachmentsData();
        console.log("✅ 导出数据成功:", exportData);
    } else {
        console.log("❌ exportAttachmentsData 函数不存在");
    }
    
    console.log("4. 测试文件大小格式化...");
    
    if (typeof formatFileSize === 'function') {
        console.log("✅ 1024 bytes =", formatFileSize(1024));
        console.log("✅ 1048576 bytes =", formatFileSize(1048576));
    } else {
        console.log("❌ formatFileSize 函数不存在");
    }
}

// 运行测试
testProductAttachments();

console.log("产品附件功能测试完成！");
