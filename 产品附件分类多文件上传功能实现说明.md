# 产品附件分类多文件上传功能实现说明

## 功能概述

根据用户要求，重新设计了产品页的附件上传功能，保持原来的三个固定分类（产品爆炸图、产品折叠图、产品总成图），但每个分类都支持上传多个附件，并且每个附件都可以单独查看、编辑和删除。

## 主要特性

### 1. 三个固定分类
- ✅ **产品爆炸图** (🔧)：展示产品各部件分解状态的技术图纸
- ✅ **产品折叠图** (📐)：展示产品折叠或收纳状态的技术图纸  
- ✅ **产品总成图** (🏗️)：展示产品完整装配状态的技术图纸

### 2. 每个分类支持多附件
- ✅ **批量上传**：每个分类支持一次选择多个文件进行上传
- ✅ **拖拽上传**：支持拖拽文件到对应分类区域
- ✅ **格式支持**：JPG、PNG、PDF、DOC、DOCX格式
- ✅ **大小限制**：单个文件最大10MB
- ✅ **智能验证**：自动验证文件格式和大小

### 3. 单个附件管理
- ✅ **查看预览**：图片直接预览，PDF/DOC显示文件信息
- ✅ **编辑功能**：双击编辑文件名，编辑备注说明
- ✅ **下载功能**：一键下载任意附件
- ✅ **删除功能**：单独删除特定附件
- ✅ **分类清空**：清空特定分类的所有附件

### 4. 全局管理
- ✅ **清空所有**：一键清空所有分类的所有附件
- ✅ **数据导出**：导出所有分类附件数据为JSON格式
- ✅ **数据导入**：支持导入之前导出的附件数据

## 技术实现

### 1. HTML结构设计 (`templates/index.html`)

#### 1.1 整体布局
```html
<div class="preview-section" id="product-attachments-section">
    <h3>📎 产品附件</h3>
    <div class="attachments-container">
        <!-- 三个分类区域 -->
        <div class="attachment-category">产品爆炸图</div>
        <div class="attachment-category">产品折叠图</div>
        <div class="attachment-category">产品总成图</div>
    </div>
    
    <!-- 全局管理按钮 -->
    <div style="text-align: center;">
        <button onclick="clearAllCategoryAttachments()">🗑️ 清空所有附件</button>
        <button onclick="exportAllCategoryAttachments()">💾 导出附件数据</button>
    </div>
</div>
```

#### 1.2 单个分类结构
```html
<div class="attachment-category">
    <div class="attachment-category-header">
        <h4>🔧 产品爆炸图</h4>
        <div class="attachment-category-controls">
            <input type="file" id="explosion-diagram-input" accept="image/*,.pdf,.doc,.docx" multiple>
            <button onclick="上传文件">📁 上传文件</button>
            <button onclick="清空分类">🗑️ 清空</button>
        </div>
    </div>
    <div class="attachment-category-content" id="explosion-diagram-content">
        <!-- 动态生成的附件列表或空状态 -->
    </div>
</div>
```

#### 1.3 单个附件项结构
```html
<div class="category-attachment-item" data-attachment-id="${attachment.id}">
    <div class="category-attachment-header">
        <div class="category-attachment-info">
            <div class="category-attachment-icon">🖼️</div>
            <div class="category-attachment-details">
                <h4 class="category-attachment-name" ondblclick="编辑名称">文件名.jpg</h4>
                <p class="category-attachment-meta">1.2 MB • 2024-01-01 12:00:00</p>
            </div>
        </div>
        <div class="category-attachment-controls">
            <button class="category-attachment-btn view">👁️</button>
            <button class="category-attachment-btn edit">✏️</button>
            <button class="category-attachment-btn download">⬇️</button>
            <button class="category-attachment-btn delete">🗑️</button>
        </div>
    </div>
    
    <!-- 预览和编辑区域 -->
    <div class="category-attachment-preview">图片预览</div>
    <div class="category-attachment-notes">备注编辑</div>
</div>
```

### 2. CSS样式设计 (`templates/index.html`)

#### 2.1 分类容器样式
```css
.attachments-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.attachment-category {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
```

#### 2.2 分类头部样式
```css
.attachment-category-header {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attachment-category-header h4 {
    margin: 0;
    color: #1a73e8;
    font-size: 1rem;
}
```

#### 2.3 附件项样式
```css
.category-attachment-item {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.8rem;
    margin-bottom: 0.8rem;
    background: #fafafa;
    transition: box-shadow 0.2s, border-color 0.2s;
}

.category-attachment-item:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    border-color: #40a9ff;
}
```

#### 2.4 操作按钮样式
```css
.category-attachment-btn.view { background: #52c41a; }
.category-attachment-btn.edit { background: #faad14; }
.category-attachment-btn.delete { background: #ff4d4f; }
.category-attachment-btn.download { background: #1890ff; }
```

### 3. JavaScript功能实现 (`static/js/product_attachments.js`)

#### 3.1 数据结构
```javascript
// 存储上传的附件数据（按分类组织，每个分类支持多个附件）
let categoryAttachments = {
    'explosion-diagram': [],
    'folding-diagram': [],
    'assembly-diagram': []
};

// 附件类型配置
const ATTACHMENT_CATEGORIES = {
    'explosion-diagram': {
        name: '产品爆炸图',
        icon: '🔧',
        description: '展示产品各部件分解状态的技术图纸'
    },
    'folding-diagram': {
        name: '产品折叠图',
        icon: '📐',
        description: '展示产品折叠或收纳状态的技术图纸'
    },
    'assembly-diagram': {
        name: '产品总成图',
        icon: '🏗️',
        description: '展示产品完整装配状态的技术图纸'
    }
};

// 单个附件数据结构
{
    id: 'explosion-diagram_1',
    name: '产品爆炸图.jpg',
    type: 'image/jpeg',
    size: 1024000,
    data: 'data:image/jpeg;base64,...',
    uploadTime: '2024-01-01T12:00:00.000Z',
    notes: '产品各部件分解图'
}
```

#### 3.2 核心功能函数
```javascript
// 分类文件上传处理
function handleCategoryFileUpload(category, event) {
    // 验证文件格式和大小
    // 批量读取文件数据
    // 添加到对应分类
    // 更新界面显示
}

// 更新分类附件列表显示
function updateCategoryAttachmentsList(category) {
    // 检查是否有附件
    // 生成附件项HTML
    // 更新DOM
    // 显示/隐藏清空按钮
}

// 附件操作功能
function toggleCategoryAttachmentPreview(category, index) { /* 切换预览 */ }
function toggleCategoryAttachmentEdit(category, index) { /* 切换编辑 */ }
function downloadCategoryAttachment(category, index) { /* 下载附件 */ }
function deleteCategoryAttachment(category, index) { /* 删除附件 */ }
function editCategoryAttachmentName(category, index, element) { /* 编辑名称 */ }
```

#### 3.3 分类管理功能
```javascript
// 清空分类附件
function clearCategoryAttachments(category) {
    // 确认删除
    // 清空分类数组
    // 更新界面
}

// 清空所有分类附件
function clearAllCategoryAttachments() {
    // 确认删除
    // 清空所有分类
    // 更新所有界面
}
```

#### 3.4 数据管理
```javascript
// 获取所有分类附件数据
function getAllCategoryAttachments() {
    return Object.keys(categoryAttachments).reduce((result, category) => {
        result[category] = categoryAttachments[category].map(attachment => ({ ...attachment }));
        return result;
    }, {});
}

// 设置分类附件数据（用于加载）
function setAllCategoryAttachments(attachments) {
    // 重置数据
    // 加载数据
    // 更新计数器
    // 更新所有分类的显示
}

// 导出所有分类附件数据
function exportAllCategoryAttachments() {
    const jsonData = JSON.stringify(getAllCategoryAttachments(), null, 2);
    // 创建下载链接
    // 触发下载
}
```

### 4. 拖拽上传功能

#### 4.1 分类拖拽支持
```javascript
// 为每个分类添加拖拽上传功能
Object.keys(ATTACHMENT_CATEGORIES).forEach(category => {
    const categoryElement = document.getElementById(`${category}-content`).closest('.attachment-category');
    
    // 拖拽悬停
    categoryElement.addEventListener('dragover', function(e) {
        e.preventDefault();
        categoryElement.style.backgroundColor = '#f0f9ff';
        categoryElement.style.borderColor = '#40a9ff';
    });
    
    // 文件放置
    categoryElement.addEventListener('drop', function(e) {
        e.preventDefault();
        const files = Array.from(e.dataTransfer.files);
        // 处理拖拽的文件到对应分类
    });
});
```

## 用户操作流程

### 1. 上传附件到分类
```
选择分类 → 点击"上传文件"/拖拽文件 → 验证格式和大小 → 读取文件数据 → 添加到分类 → 显示在分类中
```

### 2. 查看分类附件
```
点击附件的"👁️"按钮 → 切换预览显示 → 图片直接显示/其他文件显示信息
```

### 3. 编辑分类附件
```
点击"✏️"按钮 → 进入编辑模式 → 编辑备注 → 自动保存
双击文件名 → 内联编辑 → Enter保存/Escape取消
```

### 4. 下载分类附件
```
点击"⬇️"按钮 → 创建下载链接 → 触发浏览器下载
```

### 5. 删除分类附件
```
点击"🗑️"按钮 → 确认对话框 → 从分类数组中移除 → 更新分类显示
```

### 6. 分类管理
```
清空分类：点击分类的"🗑️ 清空"按钮 → 确认 → 清空该分类所有附件
清空所有：点击"🗑️ 清空所有附件"按钮 → 确认 → 清空所有分类的所有附件
```

## 数据持久化

### 1. 数据格式
```json
{
    "explosion-diagram": [
        {
            "id": "explosion-diagram_1",
            "name": "产品爆炸图.jpg",
            "type": "image/jpeg",
            "size": 1024000,
            "data": "data:image/jpeg;base64,...",
            "uploadTime": "2024-01-01T12:00:00.000Z",
            "notes": "产品各部件分解图"
        }
    ],
    "folding-diagram": [
        {
            "id": "folding-diagram_1",
            "name": "产品折叠图.pdf",
            "type": "application/pdf",
            "size": 2048000,
            "data": "data:application/pdf;base64,...",
            "uploadTime": "2024-01-01T12:05:00.000Z",
            "notes": "产品折叠状态说明"
        }
    ],
    "assembly-diagram": []
}
```

### 2. 保存和加载
```javascript
// 保存到本地存储或服务器
const attachmentsData = getAllCategoryAttachments();
localStorage.setItem('categoryAttachments', JSON.stringify(attachmentsData));

// 从本地存储或服务器加载
const savedData = localStorage.getItem('categoryAttachments');
if (savedData) {
    setAllCategoryAttachments(JSON.parse(savedData));
}
```

## 界面布局

### 1. 三列网格布局
- 使用CSS Grid自动适应屏幕宽度
- 每个分类最小宽度350px
- 响应式设计，小屏幕自动换行

### 2. 分类区域设计
- 清晰的分类标题和图标
- 独立的上传和清空按钮
- 统一的视觉风格

### 3. 附件项设计
- 紧凑的布局，节省空间
- 清晰的文件信息显示
- 直观的操作按钮

## 总结

成功实现了产品附件的分类多文件上传管理系统：

1. **保持分类结构**：维持了原有的三个固定分类
2. **多文件支持**：每个分类都支持多个附件
3. **完整功能**：查看、编辑、下载、删除一应俱全
4. **用户友好**：直观的分类界面和流畅的交互体验
5. **技术先进**：支持拖拽上传、实时预览、内联编辑
6. **数据安全**：完整的验证机制和错误处理
7. **灵活管理**：支持分类清空和全局管理

现在用户可以按照产品文档的类型分类管理各种附件，既保持了原有的组织结构，又获得了强大的多文件管理能力！🎉
