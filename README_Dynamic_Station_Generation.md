# 动态工站生成功能使用说明

## 功能概述

本系统现已支持根据AI生成内容中的工站数量动态创建工站位，并将相应内容自动识别分配到对应工站中。参考index1.html的外观设计，提供了现代化的工站管理界面。

## 主要功能特性

### 🎯 智能工站识别
- **自动识别工站数量**：从AI生成内容中自动识别ST10、ST15、ST20等工站
- **内容自动分配**：将工艺描述、设备信息等自动分配到对应工站
- **结构化数据提取**：提取工艺步骤、设备参数等详细信息

### 🏗️ 动态界面生成
- **工艺页面**：根据识别的工站数量动态生成工艺工站界面
- **设备页面**：根据识别的设备工站数量动态生成设备工站界面
- **响应式设计**：适配不同屏幕尺寸，参考index1.html的现代化设计

### ⚙️ 工站管理功能
- **添加/删除工站**：支持手动添加新工站或删除现有工站
- **编辑工站信息**：实时编辑工站号、名称、工艺步骤等
- **步骤管理**：为每个工站添加、删除、编辑工艺步骤

## 使用方法

### 1. AI自动生成工站（推荐）

#### 步骤1：准备标准格式的查询
使用产品信息页面的"一键生成"功能，或手动输入包含格式标记的查询：

```
请按照以下格式生成SAB产品的Linespec：

【工艺部分】
ST10 SAB-B-Pre-assembly
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
人or设备: 人
产品特性要求: 导流片无错装、漏装、安装方向正确
过程防错要求: 通过MSA

ST15 SAB-B-Assembly
1. 工艺过程描述: 将线束保护支架预装在发生器上
人or设备: 人
产品特性要求: 正确的支架
过程防错要求: 无夹伤，支架正确
【工艺部分结束】

【设备部分】
ST10设备信息
设备类型: 手动装配工位
技术要求: 提供导流片定位夹具
设备参数: 工作台高度800mm
安全要求: 防静电措施

ST15设备信息
设备类型: 半自动装配设备
技术要求: 支架安装压力控制
设备参数: 压力范围50-100N
安全要求: 双手启动按钮
【设备部分结束】
```

#### 步骤2：发送查询并查看结果
- 点击发送按钮
- 系统自动识别工站信息
- 工站自动生成在工艺页面和设备页面

#### 步骤3：查看生成的工站
- 切换到"2.工艺要求（APP）"页面查看工艺工站
- 切换到"3.设备要求（AEP）"页面查看设备工站

### 2. 手动管理工站

#### 添加新工站
- 在工艺页面点击"添加工艺工站"按钮
- 在设备页面点击"添加设备工站"按钮
- 系统自动分配工站号（ST10、ST15、ST20...）

#### 编辑工站信息
- 直接在输入框中修改工站号、名称
- 修改工艺步骤的各个字段
- 所有修改实时保存

#### 管理工艺步骤
- 点击"添加步骤"按钮为工站添加新的工艺步骤
- 点击步骤右上角的"删除"按钮删除步骤
- 步骤编号自动重新排序

## 界面说明

### 工艺页面布局

```
工艺要求（APP）
├── 提示信息区域
├── 工站列表容器
│   ├── ST10工站
│   │   ├── 工站基本信息（工站号、名称）
│   │   └── 工艺步骤列表
│   │       ├── 步骤1（工艺过程描述、人or设备、产品特性要求、过程防错要求）
│   │       ├── 步骤2
│   │       └── [添加步骤按钮]
│   ├── ST15工站
│   └── ...
└── 操作按钮区域
    ├── 添加工艺工站
    └── 清空所有工站
```

### 设备页面布局

```
设备要求（AEP）
├── 提示信息区域
├── 设备工站列表容器
│   ├── ST10设备信息
│   │   ├── 设备类型
│   │   ├── 技术要求
│   │   ├── 设备参数
│   │   └── 安全要求
│   ├── ST15设备信息
│   └── ...
├── 操作按钮区域
└── 其他设备信息区域（备用）
```

## 数据结构

### 工艺工站数据结构
```javascript
{
  station_number: "10",           // 工站号
  station_name: "SAB-B-Pre-assembly", // 工站名称
  content: "原始内容",            // 原始AI生成内容
  process_steps: [                // 工艺步骤数组
    {
      step_number: "1",           // 步骤编号
      description: "工艺过程描述", // 工艺描述
      operator: "人",             // 操作者类型
      quality_requirements: "质量要求", // 产品特性要求
      error_prevention: "防错要求"  // 过程防错要求
    }
  ]
}
```

### 设备工站数据结构
```javascript
{
  station_number: "10",           // 工站号
  content: "原始内容",            // 原始AI生成内容
  equipment_details: {            // 设备详细信息
    equipment_type: "设备类型",    // 设备类型
    technical_requirements: "技术要求", // 技术要求
    equipment_parameters: "设备参数",   // 设备参数
    safety_requirements: "安全要求"     // 安全要求
  }
}
```

## 技术实现

### 核心组件

1. **内容解析器** (`utils/content_parser.py`)
   - 提取工站信息
   - 解析工艺步骤
   - 提取设备详细信息

2. **工站生成器** (`static/js/station_generator.js`)
   - 动态生成工站HTML
   - 管理工站显示逻辑

3. **工站管理器** (`static/js/station_manager.js`)
   - 处理工站增删改查
   - 管理工站数据状态

4. **样式文件** (`static/css/station_styles.css`)
   - 现代化工站界面样式
   - 响应式设计支持

### API接口

- **POST /chat**: 增强的聊天接口，返回解析后的工站信息
- **POST /generate_prompt**: 生成标准格式的prompt

### 前端集成

- 在`templates/index.html`中集成工站生成功能
- 自动处理AI返回的工站数据
- 实时更新工站界面

## 使用示例

### 示例1：SAB产品工站生成

**输入**：
```
帮我写一份SAB模块的Linespec，涉及的零件有：Deflector、inflator、cushion、Harness、soft cover
```

**AI返回**（标准格式）：
```
【工艺部分】
ST10 SAB-B-Pre-assembly
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
人or设备: 人
产品特性要求: 导流片无错装、漏装、安装方向正确
过程防错要求: 通过MSA

ST15 SAB-B-Harness Assembly
1. 工艺过程描述: 将线束保护支架预装在发生器上
人or设备: 人
产品特性要求: 正确的支架
过程防错要求: 无夹伤，支架正确
【工艺部分结束】

【设备部分】
ST10设备信息
设备类型: 手动装配工位
技术要求: 提供导流片定位夹具
【设备部分结束】
```

**结果**：
- 自动生成2个工艺工站（ST10、ST15）
- 自动生成1个设备工站（ST10）
- 工艺步骤自动填充到对应工站
- 设备信息自动填充到对应工站

## 故障排除

### 常见问题

1. **工站没有自动生成**
   - 检查AI返回内容是否包含标准格式标记
   - 确认工站号格式为ST+数字（如ST10、ST15）
   - 查看浏览器控制台是否有错误信息

2. **工站信息显示不完整**
   - 检查AI返回内容的字段是否完整
   - 确认字段名称符合标准格式要求

3. **界面显示异常**
   - 确认CSS样式文件正确加载
   - 检查JavaScript文件是否正确引入

### 调试方法

1. 打开浏览器开发者工具
2. 查看Console标签页的日志信息
3. 检查Network标签页的API请求
4. 查看Elements标签页的DOM结构

## 未来改进

1. **工站模板管理**：支持保存和复用工站模板
2. **批量操作**：支持批量编辑多个工站
3. **导入导出**：支持工站数据的导入导出
4. **版本控制**：支持工站信息的版本管理
5. **协作功能**：支持多人协作编辑工站信息
