"""
AI生成内容解析器
用于自动识别和分割工艺部分与设备部分的内容
"""

import re
import json
import os
from typing import Dict, List, Tuple, Optional

class ContentParser:
    def __init__(self):
        """初始化内容解析器"""
        self.load_format_config()
    
    def load_format_config(self):
        """加载格式配置"""
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'content_format_template.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            # 默认配置
            self.config = {
                "format_specification": {
                    "structure": {
                        "process_section": {
                            "marker": "【工艺部分】",
                            "end_marker": "【工艺部分结束】"
                        },
                        "equipment_section": {
                            "marker": "【设备部分】",
                            "end_marker": "【设备部分结束】"
                        }
                    }
                },
                "parsing_rules": {
                    "process_keywords": ["工艺过程描述", "人or设备", "产品特性要求", "过程防错要求", "ST\\d+"],
                    "equipment_keywords": ["设备信息", "设备类型", "技术要求", "设备参数", "ST\\d+设备"]
                }
            }
    
    def parse_content(self, content: str) -> Dict[str, str]:
        """
        解析AI生成的内容，分割为工艺部分和设备部分
        
        Args:
            content: AI生成的原始内容
            
        Returns:
            包含process_content和equipment_content的字典
        """
        result = {
            'process_content': '',
            'equipment_content': '',
            'other_content': ''
        }
        
        # 方法1：使用标记分割（推荐格式）
        process_content = self._extract_by_markers(content, 'process_section')
        equipment_content = self._extract_by_markers(content, 'equipment_section')
        
        if process_content or equipment_content:
            result['process_content'] = process_content
            result['equipment_content'] = equipment_content
            return result
        
        # 方法2：使用关键词智能分割
        return self._smart_split_content(content)
    
    def _extract_by_markers(self, content: str, section_type: str) -> str:
        """使用标记提取特定部分的内容，支持多个标记"""
        section_config = self.config['format_specification']['structure'][section_type]

        # 支持新的多标记格式
        if 'markers' in section_config:
            markers = section_config['markers']
            end_markers = section_config['end_markers']
        else:
            # 兼容旧格式
            markers = [section_config.get('marker', '')]
            end_markers = [section_config.get('end_marker', '')]

        print(f"[DEBUG] 尝试提取 {section_type} 部分")
        print(f"[DEBUG] 开始标记: {markers}")
        print(f"[DEBUG] 结束标记: {end_markers}")

        # 尝试每个标记组合
        for marker in markers:
            print(f"[DEBUG] 检查标记: '{marker}'")
            if marker in content:
                print(f"[DEBUG] 找到开始标记: '{marker}'")
                start_pos = content.find(marker)
                if start_pos != -1:
                    start_pos += len(marker)
                    remaining_content = content[start_pos:]

                    # 查找结束位置
                    end_pos = len(remaining_content)
                    found_end_marker = None

                    for end_marker in end_markers:
                        if end_marker in remaining_content:
                            marker_pos = remaining_content.find(end_marker)
                            if marker_pos < end_pos:
                                end_pos = marker_pos
                                found_end_marker = end_marker

                    if found_end_marker:
                        print(f"[DEBUG] 找到结束标记: '{found_end_marker}' 在位置 {end_pos}")
                    else:
                        print(f"[DEBUG] 未找到结束标记，使用文档末尾")

                    extracted_content = remaining_content[:end_pos].strip()
                    print(f"[DEBUG] 提取内容长度: {len(extracted_content)}")

                    if extracted_content:
                        return extracted_content

        print(f"[DEBUG] 未能提取到 {section_type} 内容")
        return ""
    
    def _smart_split_content(self, content: str) -> Dict[str, str]:
        """智能分割内容"""
        lines = content.split('\n')
        process_lines = []
        equipment_lines = []
        current_section = 'other'

        process_keywords = self.config['parsing_rules']['process_keywords']
        equipment_keywords = self.config['parsing_rules']['equipment_keywords']

        # 强制分割标记 - 优先级最高
        equipment_section_markers = [
            '### 设备部分', '## 设备部分', '# 设备部分',
            '### 二、设备部分', '## 二、设备部分',
            '【设备部分】', '设备部分：', '二、设备部分'
        ]

        process_section_markers = [
            '### 工艺部分', '## 工艺部分', '# 工艺部分',
            '### 一、工艺部分', '## 一、工艺部分',
            '【工艺部分】', '工艺部分：', '一、工艺部分'
        ]

        for line in lines:
            line_stripped = line.strip()
            line_lower = line.lower()

            # 检查强制分割标记（优先级最高）
            if any(marker.lower() in line_lower for marker in equipment_section_markers):
                current_section = 'equipment'
                equipment_lines.append(line)
                continue
            elif any(marker.lower() in line_lower for marker in process_section_markers):
                current_section = 'process'
                process_lines.append(line)
                continue

            # 检查是否是设备工站标题（#### ST10 工站名称 在设备部分）
            if re.match(r'####\s+ST\d+', line_stripped) and current_section == 'equipment':
                equipment_lines.append(line)
                continue
            elif re.match(r'####\s+ST\d+', line_stripped) and current_section != 'equipment':
                # 如果在非设备部分遇到工站标题，判断为工艺部分
                current_section = 'process'
                process_lines.append(line)
                continue

            # 检查设备相关的特殊格式
            if re.match(r'^[一二三四五六七八九十]、.*Equipment', line_stripped):
                current_section = 'equipment'
                equipment_lines.append(line)
                continue
            elif re.match(r'^[一二三四五六七八九十]、.*Fixture', line_stripped):
                current_section = 'equipment'
                equipment_lines.append(line)
                continue

            # 检查是否包含工艺关键词
            if any(re.search(keyword.lower(), line_lower) for keyword in process_keywords):
                if current_section != 'equipment':  # 避免在设备部分误判
                    current_section = 'process'
                process_lines.append(line)
            # 检查是否包含设备关键词
            elif any(re.search(keyword.lower(), line_lower) for keyword in equipment_keywords):
                current_section = 'equipment'
                equipment_lines.append(line)
            # 根据当前上下文分配
            elif current_section == 'process':
                process_lines.append(line)
            elif current_section == 'equipment':
                equipment_lines.append(line)

        return {
            'process_content': '\n'.join(process_lines),
            'equipment_content': '\n'.join(equipment_lines),
            'other_content': content if not process_lines and not equipment_lines else ''
        }
    
    def extract_station_info(self, content: str) -> List[Dict[str, str]]:
        """
        提取工站信息

        Args:
            content: 工艺内容

        Returns:
            工站信息列表
        """
        stations = []

        # 首先尝试Markdown格式：#### ST10 工站名称
        markdown_pattern = r'####\s+ST(\d+)\s+([^\n]+)'
        markdown_matches = list(re.finditer(markdown_pattern, content))

        if markdown_matches:
            # 使用Markdown格式解析
            station_matches = markdown_matches
            is_markdown = True
        else:
            # 回退到传统格式：ST10 工站名称
            station_pattern = r'ST(\d+)\s+([^\n]+)'
            station_matches = re.finditer(station_pattern, content)
            is_markdown = False

        for match in station_matches:
            station_number = match.group(1)
            station_name = match.group(2).strip()

            # 根据格式类型提取工站内容
            if is_markdown:
                station_content = self._extract_markdown_station_content(content, f"ST{station_number}")
                # 解析Markdown格式的工艺步骤
                process_steps = self._extract_markdown_process_steps(station_content)
                if not process_steps:
                    process_steps = self._extract_process_steps_advanced(station_content)
            else:
                station_content = self._extract_station_content(content, f"ST{station_number}")
                # 解析传统格式的工艺步骤
                process_steps = self._extract_process_steps_advanced(station_content)
                if not process_steps:
                    process_steps = self._extract_process_steps(station_content)

            # 调试日志
            format_type = "Markdown" if is_markdown else "传统"
            print(f"解析工站 ST{station_number} ({format_type}格式):")
            print(f"  工站名称: {station_name}")
            print(f"  原始内容: {station_content[:100]}...")
            print(f"  解析到 {len(process_steps)} 个步骤")
            for i, step in enumerate(process_steps):
                print(f"    步骤{step.get('step_number', i+1)}: {step.get('description', '')[:50]}...")
                print(f"      人or设备: {step.get('operator', '')}")

            stations.append({
                'station_number': station_number,
                'station_name': station_name,
                'content': station_content,
                'process_steps': process_steps
            })

        return stations

    def _extract_process_steps(self, station_content: str) -> List[Dict[str, str]]:
        """
        提取工站内的工艺步骤

        Args:
            station_content: 工站内容

        Returns:
            工艺步骤列表
        """
        steps = []
        lines = station_content.split('\n')
        current_step = {}

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 匹配步骤编号 (1. 2. 3. 等) - 更灵活的匹配
            step_match = re.match(r'^(\d+)\.\s*(.*)', line)
            if step_match:
                # 保存上一个步骤
                if current_step:
                    steps.append(current_step)

                # 开始新步骤
                step_number = step_match.group(1)
                remaining_text = step_match.group(2).strip()

                current_step = {
                    'step_number': step_number,
                    'description': '',
                    'operator': '',
                    'quality_requirements': '',
                    'error_prevention': ''
                }

                # 检查步骤编号后是否直接跟着工艺过程描述
                if remaining_text:
                    if '工艺过程描述:' in remaining_text:
                        current_step['description'] = remaining_text.split('工艺过程描述:')[1].strip()
                    else:
                        # 如果没有明确标识，将剩余文本作为工艺描述
                        current_step['description'] = remaining_text
                continue

            # 解析具体字段 - 更智能的解析
            if current_step:  # 确保当前有步骤在处理
                if '工艺过程描述:' in line:
                    current_step['description'] = line.split('工艺过程描述:')[1].strip()
                elif '人or设备:' in line:
                    current_step['operator'] = line.split('人or设备:')[1].strip()
                elif '产品特性要求:' in line:
                    current_step['quality_requirements'] = line.split('产品特性要求:')[1].strip()
                elif '过程防错要求:' in line:
                    current_step['error_prevention'] = line.split('过程防错要求:')[1].strip()
                elif line.startswith('人or设备'):  # 处理没有冒号的情况
                    current_step['operator'] = line.replace('人or设备', '').strip()
                elif line.startswith('产品特性要求'):
                    current_step['quality_requirements'] = line.replace('产品特性要求', '').strip()
                elif line.startswith('过程防错要求'):
                    current_step['error_prevention'] = line.replace('过程防错要求', '').strip()
                elif not any(keyword in line for keyword in ['工艺过程描述', '人or设备', '产品特性要求', '过程防错要求']):
                    # 如果没有明确的字段标识，且当前步骤的描述为空，则作为描述
                    if not current_step['description'] and line:
                        current_step['description'] = line

        # 添加最后一个步骤
        if current_step:
            steps.append(current_step)

        return steps

    def _extract_process_steps_advanced(self, station_content: str) -> List[Dict[str, str]]:
        """
        高级工艺步骤提取方法，处理更复杂的AI生成内容格式

        Args:
            station_content: 工站内容

        Returns:
            工艺步骤列表
        """
        steps = []
        lines = station_content.split('\n')
        current_step = None

        # 预处理：合并可能被分割的行
        processed_lines = []
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if not line:
                i += 1
                continue

            # 检查是否是字段行但内容在下一行
            if line.endswith(':') and i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and not any(keyword in next_line for keyword in ['工艺过程描述', '人or设备', '产品特性要求', '过程防错要求']):
                    line = line + ' ' + next_line
                    i += 1

            processed_lines.append(line)
            i += 1

        for line in processed_lines:
            if not line:
                continue

            # 匹配步骤编号
            step_match = re.match(r'^(\d+)\.\s*(.*)', line)
            if step_match:
                # 保存上一个步骤
                if current_step:
                    steps.append(current_step)

                step_number = step_match.group(1)
                remaining_text = step_match.group(2).strip()

                current_step = {
                    'step_number': step_number,
                    'description': '',
                    'operator': '',
                    'quality_requirements': '',
                    'error_prevention': ''
                }

                # 处理步骤编号后的内容
                if remaining_text:
                    if '工艺过程描述:' in remaining_text:
                        desc_part = remaining_text.split('工艺过程描述:')[1].strip()
                        current_step['description'] = desc_part
                    else:
                        current_step['description'] = remaining_text
                continue

            # 解析字段内容
            if current_step:
                self._parse_field_content(line, current_step)

        # 添加最后一个步骤
        if current_step:
            steps.append(current_step)

        return steps

    def _parse_field_content(self, line: str, current_step: Dict[str, str]):
        """
        解析字段内容的辅助方法

        Args:
            line: 当前行内容
            current_step: 当前步骤字典
        """
        # 定义字段映射，包括更多变体
        field_mappings = [
            # 标准字段
            ('工艺过程描述', 'description'),
            ('人or设备', 'operator'),
            ('产品特性要求', 'quality_requirements'),
            ('过程防错要求', 'error_prevention'),
            # 变体字段
            ('人', 'operator'),  # 简化版本
            ('设备', 'operator'),
            ('操作者', 'operator'),
            ('质量要求', 'quality_requirements'),
            ('防错要求', 'error_prevention'),
            ('防错', 'error_prevention'),
        ]

        # 首先尝试精确匹配
        for keyword, field_name in field_mappings:
            # 处理各种冒号情况
            patterns = [
                f'{keyword}:',    # 英文冒号
                f'{keyword}：',   # 中文冒号
                f'{keyword} ',    # 空格分隔
                keyword           # 直接匹配
            ]

            for pattern in patterns:
                if pattern in line:
                    if pattern.endswith(':') or pattern.endswith('：'):
                        # 带冒号的情况
                        value = line.split(pattern)[1].strip()
                    elif pattern.endswith(' '):
                        # 空格分隔的情况
                        value = line.split(pattern)[1].strip()
                    else:
                        # 直接替换的情况
                        value = line.replace(pattern, '').strip()

                    # 清理值中的多余字符
                    value = value.strip('：: ')

                    if value:  # 只有当值不为空时才设置
                        current_step[field_name] = value
                        return

        # 特殊处理：如果行以数字开头但不是步骤编号，可能是描述的一部分
        if re.match(r'^\d+[^.]', line):
            if not current_step['description']:
                current_step['description'] = line
            return

        # 如果没有匹配到任何字段，且当前步骤的描述为空，则作为描述
        if not current_step['description'] and line and not line.startswith(('ST', '工站')):
            current_step['description'] = line

    def extract_equipment_station_info(self, content: str) -> List[Dict[str, str]]:
        """
        提取设备工站信息，支持新的Markdown格式

        Args:
            content: 设备内容

        Returns:
            设备工站信息列表
        """
        equipment_stations = []

        print(f"[DEBUG] 开始解析设备内容，长度: {len(content)}")
        print(f"[DEBUG] 设备内容前200字符: {content[:200]}")

        # 首先尝试Markdown格式：#### ST10 工站名称
        markdown_pattern = r'####\s+ST(\d+)\s+([^\n]*)'
        markdown_matches = list(re.finditer(markdown_pattern, content))

        print(f"[DEBUG] Markdown格式匹配到 {len(markdown_matches)} 个工站")

        if markdown_matches:
            # 使用Markdown格式解析
            for i, match in enumerate(markdown_matches):
                station_number = match.group(1)
                station_name = match.group(2).strip()

                print(f"[DEBUG] 处理Markdown工站 ST{station_number}: {station_name}")

                # 提取该工站的设备信息
                equipment_content = self._extract_markdown_equipment_content(content, f"ST{station_number}")
                print(f"[DEBUG] ST{station_number} 设备内容长度: {len(equipment_content)}")

                # 解析设备详细信息
                equipment_details = self._extract_markdown_equipment_details(equipment_content)

                equipment_stations.append({
                    'station_number': station_number,
                    'station_name': station_name,
                    'content': equipment_content,
                    'equipment_details': equipment_details
                })
        else:
            # 回退到传统格式：ST10设备信息
            station_pattern = r'ST(\d+)设备信息'
            station_matches = list(re.finditer(station_pattern, content))

            print(f"[DEBUG] 传统格式匹配到 {len(station_matches)} 个工站")

            for match in station_matches:
                station_number = match.group(1)

                print(f"[DEBUG] 处理传统工站 ST{station_number}")

                # 提取该工站的设备信息
                equipment_content = self._extract_equipment_content(content, f"ST{station_number}设备信息")

                # 解析设备详细信息
                equipment_details = self._extract_equipment_details(equipment_content)

                equipment_stations.append({
                    'station_number': station_number,
                    'content': equipment_content,
                    'equipment_details': equipment_details
                })

        # 如果没有找到任何工站，尝试从工艺工站推断设备工站
        if not equipment_stations:
            print("[DEBUG] 没有找到设备工站，尝试从内容推断")
            equipment_stations = self._infer_equipment_stations_from_content(content)

        print(f"[DEBUG] 最终解析到 {len(equipment_stations)} 个设备工站")
        return equipment_stations

    def _infer_equipment_stations_from_content(self, content: str) -> List[Dict[str, str]]:
        """
        从设备内容中推断设备工站信息
        当没有明确的工站标题时使用
        """
        equipment_stations = []

        # 查找所有可能的工站引用
        station_refs = re.findall(r'ST(\d+)', content)
        unique_stations = list(set(station_refs))

        print(f"[DEBUG] 从内容中推断到工站: {unique_stations}")

        for station_num in sorted(unique_stations):
            # 尝试提取该工站相关的设备信息
            station_content = self._extract_station_related_content(content, station_num)

            if station_content.strip():
                equipment_details = self._extract_equipment_details(station_content)

                equipment_stations.append({
                    'station_number': station_num,
                    'station_name': f'ST{station_num}',
                    'content': station_content,
                    'equipment_details': equipment_details
                })

        return equipment_stations

    def _extract_station_related_content(self, content: str, station_num: str) -> str:
        """
        提取与特定工站相关的内容
        """
        lines = content.split('\n')
        related_lines = []

        for line in lines:
            if f'ST{station_num}' in line:
                related_lines.append(line)
            elif related_lines and line.strip() and not re.match(r'ST\d+', line):
                # 如果已经开始收集内容，继续收集直到遇到下一个工站
                related_lines.append(line)
            elif related_lines and re.match(r'ST\d+', line):
                # 遇到下一个工站，停止收集
                break

        return '\n'.join(related_lines)

    def _extract_equipment_content(self, content: str, station_id: str) -> str:
        """提取特定设备工站的内容"""
        lines = content.split('\n')
        equipment_lines = []
        capturing = False

        for line in lines:
            if station_id in line:
                capturing = True
                equipment_lines.append(line)
            elif capturing and re.match(r'ST\d+设备信息', line):
                # 遇到下一个设备工站，停止捕获
                break
            elif capturing:
                equipment_lines.append(line)

        return '\n'.join(equipment_lines)

    def _extract_equipment_details(self, equipment_content: str) -> Dict[str, str]:
        """
        提取设备详细信息

        Args:
            equipment_content: 设备内容

        Returns:
            设备详细信息字典
        """
        details = {
            'equipment_type': '',
            'technical_requirements': '',
            'equipment_parameters': '',
            'safety_requirements': ''
        }

        lines = equipment_content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            if '设备类型:' in line:
                details['equipment_type'] = line.split('设备类型:')[1].strip()
            elif '技术要求:' in line:
                details['technical_requirements'] = line.split('技术要求:')[1].strip()
            elif '设备参数:' in line:
                details['equipment_parameters'] = line.split('设备参数:')[1].strip()
            elif '安全要求:' in line:
                details['safety_requirements'] = line.split('安全要求:')[1].strip()

        return details
    
    def _extract_station_content(self, content: str, station_id: str) -> str:
        """提取特定工站的内容"""
        lines = content.split('\n')
        station_lines = []
        capturing = False
        
        for line in lines:
            if station_id in line:
                capturing = True
                station_lines.append(line)
            elif capturing and re.match(r'ST\d+', line):
                # 遇到下一个工站，停止捕获
                break
            elif capturing:
                station_lines.append(line)
        
        return '\n'.join(station_lines)
    
    def format_for_display(self, content: str, content_type: str = 'process') -> str:
        """
        格式化内容用于显示
        
        Args:
            content: 原始内容
            content_type: 内容类型 ('process' 或 'equipment')
            
        Returns:
            格式化后的HTML内容
        """
        if not content:
            return ""
        
        # 转换为HTML格式
        html_content = content.replace('\n', '<br>')
        
        # 高亮工站号
        html_content = re.sub(r'(ST\d+)', r'<strong style="color: #1a73e8;">\1</strong>', html_content)
        
        # 高亮关键字段
        if content_type == 'process':
            keywords = ['工艺过程描述:', '人or设备:', '产品特性要求:', '过程防错要求:']
        else:
            keywords = ['设备信息:', '设备类型:', '技术要求:', '设备参数:']
        
        for keyword in keywords:
            html_content = html_content.replace(keyword, f'<strong>{keyword}</strong>')
        
        return html_content

    def _extract_markdown_station_content(self, content: str, station_id: str) -> str:
        """
        提取Markdown格式的工站内容

        Args:
            content: 完整内容
            station_id: 工站ID (如ST10)

        Returns:
            工站内容
        """
        # 查找工站标题：#### ST10 工站名称
        pattern = f"####\\s+{re.escape(station_id)}\\s+[^\\n]+"
        match = re.search(pattern, content)

        if not match:
            return ""

        start_pos = match.start()

        # 查找下一个工站或部分的开始
        next_station_pattern = r"####\s+ST\d+"
        next_section_pattern = r"###\s+"

        # 从当前位置之后查找
        remaining_content = content[match.end():]

        end_pos = len(content)

        # 查找下一个工站
        next_station_match = re.search(next_station_pattern, remaining_content)
        if next_station_match:
            end_pos = min(end_pos, match.end() + next_station_match.start())

        # 查找下一个主要部分
        next_section_match = re.search(next_section_pattern, remaining_content)
        if next_section_match:
            end_pos = min(end_pos, match.end() + next_section_match.start())

        return content[start_pos:end_pos].strip()

    def _extract_markdown_process_steps(self, station_content: str) -> List[Dict[str, str]]:
        """
        提取Markdown格式的工艺步骤

        Args:
            station_content: 工站内容

        Returns:
            工艺步骤列表
        """
        steps = []
        lines = station_content.split('\n')
        current_step = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 匹配步骤编号：1. 工艺过程描述: xxx
            step_match = re.match(r'^(\d+)\.\s*工艺过程描述:\s*(.*)', line)
            if step_match:
                # 保存上一个步骤
                if current_step:
                    steps.append(current_step)

                # 开始新步骤
                step_number = step_match.group(1)
                description = step_match.group(2).strip()

                current_step = {
                    'step_number': step_number,
                    'description': description,
                    'operator': '',
                    'quality_requirements': '',
                    'error_prevention': ''
                }
                continue

            # 匹配缩进的字段：   - 人or设备: xxx
            if current_step and line.startswith('-'):
                field_content = line[1:].strip()  # 移除 '-'

                if field_content.startswith('人or设备:'):
                    current_step['operator'] = field_content.split('人or设备:')[1].strip()
                elif field_content.startswith('产品特性要求:'):
                    current_step['quality_requirements'] = field_content.split('产品特性要求:')[1].strip()
                elif field_content.startswith('过程防错要求:'):
                    current_step['error_prevention'] = field_content.split('过程防错要求:')[1].strip()

        # 添加最后一个步骤
        if current_step:
            steps.append(current_step)

        return steps

    def _extract_markdown_equipment_content(self, content: str, station_id: str) -> str:
        """
        提取Markdown格式的设备工站内容

        Args:
            content: 完整内容
            station_id: 工站ID (如ST10)

        Returns:
            设备工站内容
        """
        # 查找工站标题：#### ST10 工站名称
        pattern = f"####\\s+{re.escape(station_id)}\\s+[^\\n]+"
        match = re.search(pattern, content)

        if not match:
            return ""

        start_pos = match.start()

        # 查找下一个工站或部分的开始
        next_station_pattern = r"####\s+ST\d+"
        next_section_pattern = r"###\s+"

        # 从当前位置之后查找
        remaining_content = content[match.end():]

        end_pos = len(content)

        # 查找下一个工站
        next_station_match = re.search(next_station_pattern, remaining_content)
        if next_station_match:
            end_pos = min(end_pos, match.end() + next_station_match.start())

        # 查找下一个主要部分
        next_section_match = re.search(next_section_pattern, remaining_content)
        if next_section_match:
            end_pos = min(end_pos, match.end() + next_section_match.start())

        return content[start_pos:end_pos].strip()

    def _extract_markdown_equipment_details(self, equipment_content: str) -> Dict[str, str]:
        """
        提取Markdown格式的设备详细信息，支持Equipment和Fixture分离

        Args:
            equipment_content: 设备内容

        Returns:
            设备详细信息字典
        """
        details = {
            'equipment_type': '',
            'technical_requirements': '',
            'equipment_parameters': '',
            'safety_requirements': '',

            # Equipment部分
            'equipment_mechanical_requirements': '',
            'equipment_electrical_requirements': '',
            'equipment_error_prevention_requirements': '',

            # Fixture部分
            'fixture_mechanical_requirements': '',
            'fixture_electrical_requirements': '',
            'fixture_error_prevention_requirements': '',

            # 向后兼容字段
            'mechanical_requirements': '',
            'electrical_requirements': '',
            'error_prevention_requirements': ''
        }

        lines = equipment_content.split('\n')
        current_section = None
        current_subsection = None

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 识别主要部分：一、预装设备-Equipment 或 二、预装夹具-Fixture
            if re.match(r'^[一二三四五六七八九十]、.*Equipment', line):
                current_section = 'equipment'
                # 提取设备类型
                if '设备' in line:
                    equipment_name = line.split('、')[1].strip()
                    details['equipment_type'] = equipment_name
                continue
            elif re.match(r'^[一二三四五六七八九十]、.*Fixture', line):
                current_section = 'fixture'
                continue

            # 识别子部分：（一）机械要求: （二）电气要求: （三）防错及点检要求:
            if re.match(r'^（[一二三四五六七八九十]）', line):
                if '机械要求' in line:
                    current_subsection = 'mechanical'
                elif '电气要求' in line:
                    current_subsection = 'electrical'
                elif '防错及点检要求' in line:
                    current_subsection = 'error_prevention'
                continue

            # 收集内容到对应的Equipment或Fixture字段
            if current_section and current_subsection and line and not line.startswith(('####', '###', '一、', '二、')):
                field_key = f"{current_section}_{current_subsection}_requirements"

                if details[field_key]:
                    details[field_key] += '\n' + line
                else:
                    details[field_key] = line

        # 合并Equipment和Fixture内容到向后兼容字段
        details['mechanical_requirements'] = self._combine_equipment_fixture_content(
            details['equipment_mechanical_requirements'],
            details['fixture_mechanical_requirements']
        )
        details['electrical_requirements'] = self._combine_equipment_fixture_content(
            details['equipment_electrical_requirements'],
            details['fixture_electrical_requirements']
        )
        details['error_prevention_requirements'] = self._combine_equipment_fixture_content(
            details['equipment_error_prevention_requirements'],
            details['fixture_error_prevention_requirements']
        )

        # 为了向后兼容，将合并后的字段映射到旧字段
        details['technical_requirements'] = details['mechanical_requirements']
        details['equipment_parameters'] = details['electrical_requirements']
        details['safety_requirements'] = details['error_prevention_requirements']

        return details

    def _combine_equipment_fixture_content(self, equipment_content: str, fixture_content: str) -> str:
        """
        合并Equipment和Fixture的内容

        Args:
            equipment_content: 设备内容
            fixture_content: 夹具内容

        Returns:
            合并后的内容
        """
        combined = []

        if equipment_content:
            combined.append(f"【设备要求】\n{equipment_content}")

        if fixture_content:
            combined.append(f"【夹具要求】\n{fixture_content}")

        return '\n\n'.join(combined)

# 全局解析器实例
content_parser = ContentParser()
