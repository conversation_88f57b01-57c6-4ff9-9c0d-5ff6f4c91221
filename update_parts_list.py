#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新SAB类别零件清单脚本
根据提供的零件清单更新各个SAB类别的Markdown文件
"""

import os
import re

# SAB类别零件清单数据
SAB_PARTS_DATA = {
    'A': {
        'process_type': 'A',
        'cycle_time': '8',
        'parts': ['Deflector', 'inflator', 'cushion', 'soft cover']
    },
    'B': {
        'process_type': 'B', 
        'cycle_time': '10',
        'parts': ['Deflector', 'inflator', 'cushion', 'Harness', 'soft cover']
    },
    'C': {
        'process_type': 'C',
        'cycle_time': '20', 
        'parts': ['Deflector', 'inflator', 'cushion', 'Harness', 'Bracket', 'Nuts', 'soft cover']
    },
    'D': {
        'process_type': 'D',
        'cycle_time': '15',
        'parts': ['Deflector', 'inflator', 'cushion', 'Harness', 'hard cover']
    },
    'E': {
        'process_type': 'E',
        'cycle_time': '30',
        'parts': ['Deflector', 'inflator', 'cushion', 'Bracket', 'Nuts', 'housing']
    },
    'F': {
        'process_type': 'F',
        'cycle_time': '30',
        'parts': ['Deflector', 'inflator', 'cushion', 'hard cover', 'housing', '3D heat']
    }
}

def update_parts_in_markdown(category):
    """更新指定SAB类别的零件清单"""
    
    file_path = f"SAB知识库/SAB-{category}类.md"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        # 读取现有文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 获取该类别的零件数据
        parts_data = SAB_PARTS_DATA[category]
        
        # 构建新的概述部分
        new_overview = f"""## 概述
SAB-{category}类产品的工艺和设备要求规范。

**产品族**: SAB
**工艺类型**: {parts_data['process_type']}
**标准节拍时间**: {parts_data['cycle_time']}分钟
**应用场景**: 适用于包含{len(parts_data['parts'])}个主要零件的SAB产品装配"""

        # 构建新的零件清单部分
        new_parts_section = "## 零件清单\n"
        for i, part in enumerate(parts_data['parts'], 1):
            new_parts_section += f"{i}. **{part}** - {get_part_description(part)}\n"
        
        # 替换概述部分
        content = re.sub(
            r'## 概述\n.*?(?=\n## )',
            new_overview + '\n\n',
            content,
            flags=re.DOTALL
        )
        
        # 替换零件清单部分
        content = re.sub(
            r'## 零件清单\n.*?(?=\n## )',
            new_parts_section + '\n',
            content,
            flags=re.DOTALL
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已更新 SAB-{category}类.md")
        print(f"   - 零件数量: {len(parts_data['parts'])}")
        print(f"   - 节拍时间: {parts_data['cycle_time']}分钟")
        print(f"   - 零件清单: {', '.join(parts_data['parts'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新文件失败 {file_path}: {e}")
        return False

def get_part_description(part_name):
    """获取零件描述"""
    descriptions = {
        'Deflector': '导流片，用于气流导向',
        'inflator': '发生器，气袋充气装置',
        'cushion': '气袋，主要缓冲组件',
        'Harness': '线束，电气连接组件',
        'Bracket': '支架，结构支撑组件',
        'Nuts': '螺母，紧固件',
        'soft cover': '软包布，柔性覆盖材料',
        'hard cover': '硬盖，刚性覆盖组件',
        'housing': '外壳，保护性外罩',
        '3D heat': '3D加热元件，温控组件'
    }
    
    return descriptions.get(part_name, '专用零件组件')

def update_all_categories():
    """更新所有SAB类别的零件清单"""
    
    print("🔄 开始更新SAB类别零件清单...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(SAB_PARTS_DATA)
    
    for category in SAB_PARTS_DATA.keys():
        print(f"\n📝 更新 SAB-{category}类...")
        if update_parts_in_markdown(category):
            success_count += 1
    
    print(f"\n{'=' * 60}")
    print(f"📊 更新完成统计:")
    print(f"✅ 成功更新: {success_count}/{total_count} 个文件")
    
    if success_count == total_count:
        print("🎉 所有文件更新成功！")
        
        # 显示更新摘要
        print(f"\n📋 零件清单更新摘要:")
        for category, data in SAB_PARTS_DATA.items():
            print(f"SAB-{category}: {len(data['parts'])}个零件, CT={data['cycle_time']}min")
    else:
        print(f"⚠️  有 {total_count - success_count} 个文件更新失败")
    
    return success_count == total_count

def verify_updates():
    """验证更新结果"""
    
    print(f"\n🔍 验证更新结果...")
    print("-" * 40)
    
    for category in SAB_PARTS_DATA.keys():
        file_path = f"SAB知识库/SAB-{category}类.md"
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含新的零件信息
                parts_data = SAB_PARTS_DATA[category]
                parts_found = 0
                
                for part in parts_data['parts']:
                    if part in content:
                        parts_found += 1
                
                # 检查是否包含节拍时间信息
                has_cycle_time = parts_data['cycle_time'] in content
                
                print(f"SAB-{category}: {parts_found}/{len(parts_data['parts'])} 零件 | CT: {'✅' if has_cycle_time else '❌'}")
                
            except Exception as e:
                print(f"SAB-{category}: ❌ 验证失败 - {e}")
        else:
            print(f"SAB-{category}: ❌ 文件不存在")

def main():
    """主函数"""
    
    # 检查SAB知识库目录是否存在
    if not os.path.exists("SAB知识库"):
        print("❌ SAB知识库目录不存在，请先运行数据转换脚本")
        return
    
    print("🚀 SAB零件清单更新工具")
    print("=" * 60)
    
    # 显示要更新的数据
    print("📋 将要更新的零件清单数据:")
    for category, data in SAB_PARTS_DATA.items():
        parts_str = ', '.join(data['parts'])
        print(f"  SAB-{category}: CT={data['cycle_time']}min | {parts_str}")
    
    print(f"\n确认更新? (y/n): ", end="")
    
    # 在脚本中自动确认
    print("y")
    
    # 执行更新
    if update_all_categories():
        # 验证更新结果
        verify_updates()
        
        print(f"\n✅ 零件清单更新完成！")
        print(f"📁 更新的文件位于: SAB知识库/")
        print(f"🔄 现在可以重新上传这些文件到知识库系统")
    else:
        print(f"\n❌ 更新过程中出现错误，请检查日志")

if __name__ == "__main__":
    main()
