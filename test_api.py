import requests
import json

# 测试智能分析API
def test_analyze_linespec():
    url = "http://localhost:5000/analyze_linespec"
    data = {
        "product_family": "SAB",
        "components": ["Deflector", "inflator", "cushion", "Harness", "soft cover"],
        "project_requirements": "测试项目要求"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_analyze_linespec()
