# 夹具多文件上传功能实现说明

## 功能概述

为设备工站的夹具部分添加了多文件上传功能，支持上传多种格式的文件，包括图片、PDF、Word文档等，用于存储夹具分解构图和相关技术文档。

## 主要特性

### 1. 多文件支持
- ✅ **支持同时选择多个文件**
- ✅ **支持多种文件格式**：JPG、PNG、PDF、DOC、DOCX
- ✅ **文件大小限制**：单个文件最大10MB
- ✅ **文件类型验证**：自动检查文件格式和扩展名

### 2. 用户界面
- ✅ **专用上传区域**：位于夹具要求部分顶部
- ✅ **橙色主题**：与夹具部分的橙色主题保持一致
- ✅ **文件列表显示**：显示已上传文件的详细信息
- ✅ **操作按钮**：预览、下载、删除单个文件、清空所有文件

### 3. 文件管理
- ✅ **文件预览**：图片文件支持模态框预览
- ✅ **文件下载**：支持下载任何已上传的文件
- ✅ **单个删除**：可删除指定的单个文件
- ✅ **批量清空**：一键清空所有夹具文件

## 技术实现

### 1. HTML结构修改 (`static/js/station_generator.js`)

#### 1.1 夹具文件上传区域
```html
<!-- 夹具文件上传区域 -->
<div style="margin-bottom: 1rem;">
    <h6 style="color: #fa8c16;">📎 夹具分解构图</h6>
    <div style="background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>夹具文件</span>
            <div style="display: flex; gap: 8px;">
                <input type="file" id="fixture-files-input-${index}" accept="image/*,.pdf,.doc,.docx" multiple>
                <button onclick="上传文件">📁 上传文件</button>
                <button onclick="清空所有">🗑️ 清空所有</button>
            </div>
        </div>
        <div id="fixture-files-preview-${index}">
            <!-- 文件预览区域 -->
        </div>
    </div>
</div>
```

### 2. JavaScript函数实现 (`templates/index.html`)

#### 2.1 多文件上传处理
```javascript
function handleFixtureFilesUpload(event, stationIndex) {
    const files = Array.from(event.target.files);
    
    // 文件类型和大小验证
    // 批量文件读取和处理
    // 数据保存到全局变量
    // 更新预览显示
}
```

#### 2.2 文件预览更新
```javascript
function updateFixtureFilesPreview(stationIndex) {
    // 根据文件数据生成文件列表
    // 支持不同文件类型的图标显示
    // 提供操作按钮（预览、下载、删除）
}
```

#### 2.3 文件操作功能
```javascript
// 图片预览（模态框）
function previewFixtureFile(stationIndex, fileIndex) { ... }

// 文件下载
function downloadFixtureFile(stationIndex, fileIndex) { ... }

// 删除单个文件
function deleteFixtureFile(stationIndex, fileIndex) { ... }

// 清空所有文件
function clearAllFixtureFiles(stationIndex) { ... }
```

#### 2.4 辅助函数
```javascript
// 获取文件图标
function getFileIcon(fileName, fileType) { ... }

// 格式化文件大小
function formatFileSize(bytes) { ... }
```

### 3. 数据管理 (`static/js/station_manager.js`)

#### 3.1 数据结构
```javascript
// 全局夹具文件数据
window.fixtureFilesData = {
    [stationIndex]: [
        {
            fileData: "data:image/jpeg;base64,...",
            fileName: "夹具图1.jpg",
            fileSize: 1024000,
            fileType: "image/jpeg",
            uploadTime: "2024-01-01T12:00:00.000Z"
        },
        // ... 更多文件
    ]
};
```

#### 3.2 数据持久化
```javascript
function saveEquipmentStationImageAndParameters(stationIndex) {
    // 保存夹具文件数据到设备工站数据结构中
    const fixtureFiles = window.fixtureFilesData?.[stationIndex];
    if (fixtureFiles && fixtureFiles.length > 0) {
        details.fixture_files_data = fixtureFiles;
        details.fixture_files_count = fixtureFiles.length;
    }
}
```

#### 3.3 数据恢复
```javascript
function initializeEquipmentStationImageAndParameters(stationIndex) {
    // 从设备工站数据中恢复夹具文件
    if (details.fixture_files_data && Array.isArray(details.fixture_files_data)) {
        window.fixtureFilesData[stationIndex] = details.fixture_files_data;
        updateFixtureFilesPreview(stationIndex);
    }
}
```

## 用户界面设计

### 1. 上传区域
- **位置**：夹具要求部分顶部，在机械要求之前
- **标题**：📎 夹具分解构图
- **按钮**：橙色主题的上传按钮和红色的清空按钮
- **提示**：支持的文件格式和多选提示

### 2. 文件列表
- **文件图标**：根据文件类型显示不同图标
  - 🖼️ 图片文件 (JPG, PNG)
  - 📄 PDF文件
  - 📝 Word文档 (DOC, DOCX)
  - 📎 其他文件
- **文件信息**：文件名、大小、上传时间
- **操作按钮**：
  - 👁️ 预览（仅图片）
  - ⬇️ 下载
  - 🗑️ 删除

### 3. 空状态显示
```
📎
点击上方按钮上传夹具文件
支持 JPG, PNG, PDF, DOC, DOCX 格式，可选择多个文件
```

### 4. 文件统计
- 显示总文件数量
- 滚动条支持（超过一定高度时）

## 功能流程

### 1. 文件上传流程
```
用户选择多个文件 → 文件验证 → 批量读取 → 保存数据 → 更新预览 → 显示成功提示
```

### 2. 文件预览流程
```
点击预览按钮 → 检查文件类型 → 创建模态框 → 显示图片 → 支持关闭操作
```

### 3. 文件下载流程
```
点击下载按钮 → 创建下载链接 → 触发下载 → 显示下载提示
```

### 4. 文件删除流程
```
点击删除按钮 → 确认对话框 → 从数组删除 → 更新预览 → 保存数据
```

## 数据流程

### 1. 上传数据流
```
文件选择 → FileReader读取 → Base64编码 → 保存到window.fixtureFilesData → 调用保存函数 → 存储到设备工站数据
```

### 2. 恢复数据流
```
设备工站生成 → 延迟初始化 → 读取fixture_files_data → 恢复到window.fixtureFilesData → 更新预览显示
```

### 3. 数据同步流
```
任何文件操作 → 更新window.fixtureFilesData → 调用saveEquipmentStationImageAndParameters → 同步到设备工站数据
```

## 兼容性和错误处理

### 1. 文件类型检查
- 支持MIME类型检查
- 支持文件扩展名检查
- 双重验证确保安全性

### 2. 文件大小限制
- 单个文件最大10MB
- 超出限制时显示友好提示

### 3. 错误处理
- 文件读取失败时的错误提示
- DOM元素不存在时的保护机制
- 数据恢复时的容错处理

### 4. 浏览器兼容性
- 使用标准的FileReader API
- 支持现代浏览器的文件操作
- 优雅降级处理

## 全局函数暴露

所有夹具文件相关函数都已暴露到全局作用域：
- `handleFixtureFilesUpload`
- `updateFixtureFilesPreview`
- `getFileIcon`
- `formatFileSize`
- `previewFixtureFile`
- `downloadFixtureFile`
- `deleteFixtureFile`
- `clearAllFixtureFiles`

## 总结

夹具多文件上传功能已完全实现，提供了：

1. **完整的文件管理**：上传、预览、下载、删除
2. **多格式支持**：图片、PDF、Word文档
3. **用户友好界面**：直观的操作和清晰的反馈
4. **数据持久化**：自动保存和恢复文件数据
5. **错误处理**：完善的验证和错误提示

用户现在可以为每个设备工站的夹具部分上传多个相关文件，大大提升了文档管理的便利性！🎉
