#!/usr/bin/env python3
"""
测试产品信息页面的产品附件功能
验证产品附件从工艺页面移动到产品信息页面后是否正常工作
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_product_attachments_moved():
    """测试产品附件是否成功移动到产品信息页面"""
    
    print("=== 测试产品附件移动功能 ===\n")
    
    # 检查服务器是否运行
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        print("✅ 服务器运行正常")
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器未运行: {e}")
        return False
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        driver.get('http://localhost:5000')
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        print("1. 检查产品信息页面...")
        
        # 点击产品信息页面菜单
        product_menu = wait.until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-page="product"]'))
        )
        product_menu.click()
        
        # 等待产品信息页面显示
        time.sleep(1)
        
        # 检查产品附件区域是否存在
        try:
            attachments_section = driver.find_element(By.ID, 'product-attachments-section')
            print("✅ 产品附件区域已成功移动到产品信息页面")
            
            # 检查三个附件类型是否都存在
            attachment_types = [
                'explosion-diagram-input',
                'folding-diagram-input', 
                'assembly-diagram-input'
            ]
            
            for attachment_type in attachment_types:
                try:
                    element = driver.find_element(By.ID, attachment_type)
                    print(f"✅ {attachment_type} 元素存在")
                except:
                    print(f"❌ {attachment_type} 元素不存在")
                    
        except:
            print("❌ 产品附件区域未找到")
            return False
        
        print("\n2. 检查工艺页面...")
        
        # 点击工艺页面菜单
        process_menu = driver.find_element(By.CSS_SELECTOR, '[data-page="process"]')
        process_menu.click()
        
        # 等待工艺页面显示
        time.sleep(1)
        
        # 检查工艺页面是否还有产品附件区域（应该没有）
        try:
            # 尝试在工艺页面查找产品附件区域
            process_page = driver.find_element(By.ID, 'page-process')
            attachments_in_process = process_page.find_elements(By.ID, 'product-attachments-section')
            
            if len(attachments_in_process) == 0:
                print("✅ 工艺页面已成功移除产品附件区域")
            else:
                print("❌ 工艺页面仍然存在产品附件区域")
                return False
                
        except Exception as e:
            print(f"检查工艺页面时出错: {e}")
        
        print("\n3. 测试JavaScript功能...")
        
        # 回到产品信息页面
        product_menu = driver.find_element(By.CSS_SELECTOR, '[data-page="product"]')
        product_menu.click()
        time.sleep(1)
        
        # 检查JavaScript函数是否可用
        try:
            # 执行JavaScript检查函数是否存在
            js_check = driver.execute_script("""
                return {
                    handleFileUpload: typeof handleFileUpload !== 'undefined',
                    removeAttachment: typeof removeAttachment !== 'undefined',
                    getAllAttachments: typeof getAllAttachments !== 'undefined',
                    clearAllAttachments: typeof clearAllAttachments !== 'undefined'
                };
            """)
            
            for func_name, exists in js_check.items():
                if exists:
                    print(f"✅ JavaScript函数 {func_name} 可用")
                else:
                    print(f"❌ JavaScript函数 {func_name} 不可用")
        
        except Exception as e:
            print(f"JavaScript检查失败: {e}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
        
    finally:
        if 'driver' in locals():
            driver.quit()

def test_attachment_buttons():
    """测试附件按钮功能"""
    print("\n=== 测试附件按钮功能 ===")
    
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.get('http://localhost:5000')
        
        wait = WebDriverWait(driver, 10)
        
        # 切换到产品信息页面
        product_menu = wait.until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-page="product"]'))
        )
        product_menu.click()
        time.sleep(1)
        
        # 检查上传按钮
        upload_buttons = driver.find_elements(By.CLASS_NAME, 'upload-btn')
        print(f"✅ 找到 {len(upload_buttons)} 个上传按钮")
        
        # 检查管理按钮
        clear_btn = driver.find_elements(By.XPATH, "//button[contains(text(), '清空所有附件')]")
        export_btn = driver.find_elements(By.XPATH, "//button[contains(text(), '导出附件数据')]")
        
        if clear_btn:
            print("✅ 清空所有附件按钮存在")
        else:
            print("❌ 清空所有附件按钮不存在")
            
        if export_btn:
            print("✅ 导出附件数据按钮存在")
        else:
            print("❌ 导出附件数据按钮不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 按钮测试失败: {e}")
        return False
        
    finally:
        if 'driver' in locals():
            driver.quit()

if __name__ == "__main__":
    try:
        # 运行主要测试
        success1 = test_product_attachments_moved()
        
        # 运行按钮测试
        success2 = test_attachment_buttons()
        
        if success1 and success2:
            print("\n🎉 所有测试通过！产品附件功能已成功移动到产品信息页面。")
        else:
            print("\n⚠️ 部分测试失败，请检查相关功能。")
            
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        print("\n💡 提示:")
        print("1. 确保Chrome浏览器已安装")
        print("2. 确保chromedriver在PATH中")
        print("3. 确保Flask应用正在运行 (python app.py)")
