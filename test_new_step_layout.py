#!/usr/bin/env python3
"""
测试新的工艺步骤布局
验证"人or设备"移到步骤标题右边，三个标题各占一行
"""

import re
import requests

def test_new_step_layout():
    """测试新的步骤布局是否正确"""
    
    print("=== 测试新的工艺步骤布局 ===\n")
    
    try:
        # 读取JavaScript文件内容
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_generator.js文件")
        
        # 1. 检查步骤标题行的布局
        print("\n1. 检查步骤标题行布局...")
        
        # 检查是否有步骤标题和人or设备在同一行
        title_operator_pattern = r'步骤.*?人or设备.*?select'
        if re.search(title_operator_pattern, js_content, re.DOTALL):
            print("✅ 步骤标题和'人or设备'在同一行")
        else:
            print("❌ 步骤标题和'人or设备'未在同一行")
        
        # 检查步骤标题行的flex布局
        title_flex_pattern = r'display:\s*flex.*?justify-content:\s*space-between.*?步骤'
        if re.search(title_flex_pattern, js_content, re.DOTALL):
            print("✅ 步骤标题行使用了正确的flex布局")
        else:
            print("❌ 步骤标题行布局不正确")
        
        # 2. 检查三个标题是否各占一行
        print("\n2. 检查三个标题布局...")
        
        # 检查工艺过程描述是否单独一行
        process_desc_pattern = r'工艺过程描述:.*?textarea.*?margin-bottom'
        if re.search(process_desc_pattern, js_content, re.DOTALL):
            print("✅ '工艺过程描述'单独占一行")
        else:
            print("❌ '工艺过程描述'布局不正确")
        
        # 检查产品特性要求是否单独一行
        quality_pattern = r'产品特性要求:.*?textarea.*?margin-bottom'
        if re.search(quality_pattern, js_content, re.DOTALL):
            print("✅ '产品特性要求'单独占一行")
        else:
            print("❌ '产品特性要求'布局不正确")
        
        # 检查过程防错要求是否单独一行
        error_pattern = r'过程防错要求:.*?textarea'
        if re.search(error_pattern, js_content, re.DOTALL):
            print("✅ '过程防错要求'单独占一行")
        else:
            print("❌ '过程防错要求'布局不正确")
        
        # 3. 检查标签宽度一致性
        print("\n3. 检查标签宽度一致性...")
        
        min_width_count = len(re.findall(r'min-width:\s*90px', js_content))
        if min_width_count >= 3:
            print(f"✅ 找到{min_width_count}个标签设置了统一的最小宽度")
        else:
            print(f"❌ 只找到{min_width_count}个标签设置了最小宽度，应该至少有3个")
        
        # 4. 检查人or设备选择器的新位置
        print("\n4. 检查'人or设备'选择器...")
        
        # 检查选择器是否在步骤标题旁边
        operator_in_title_pattern = r'步骤.*?gap:\s*1rem.*?人or设备'
        if re.search(operator_in_title_pattern, js_content, re.DOTALL):
            print("✅ '人or设备'选择器正确放置在步骤标题旁边")
        else:
            print("❌ '人or设备'选择器位置不正确")
        
        # 检查选择器宽度
        operator_width_pattern = r'width:\s*80px.*?人or设备'
        if re.search(operator_width_pattern, js_content):
            print("✅ '人or设备'选择器宽度设置为80px")
        else:
            print("❌ '人or设备'选择器宽度设置不正确")
        
        # 5. 检查删除按钮位置
        print("\n5. 检查删除按钮位置...")
        
        delete_button_pattern = r'justify-content:\s*space-between.*?deleteProcessStep'
        if re.search(delete_button_pattern, js_content, re.DOTALL):
            print("✅ 删除按钮正确放置在右侧")
        else:
            print("❌ 删除按钮位置不正确")
        
        print("\n=== 布局测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def generate_new_layout_preview():
    """生成新布局预览HTML"""
    print("\n=== 生成新布局预览 ===")
    
    try:
        # 创建新的HTML预览文件
        preview_html = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新工艺步骤布局预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .step-preview { 
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white; 
        }
        .step-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem; 
        }
        .step-title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .step-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .operator-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        .content-row { 
            display: flex; 
            align-items: center; 
            gap: 0.5rem; 
            margin-bottom: 0.4rem; 
        }
        .content-row:last-child {
            margin-bottom: 0;
        }
        label { 
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap; 
            min-width: 90px;
        }
        .operator-label {
            min-width: auto;
        }
        textarea { 
            flex: 1; 
            height: 35px; 
            padding: 0.3rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            resize: none; 
            font-size: 0.8rem; 
            line-height: 1.3; 
        }
        select { 
            width: 80px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 28px; 
        }
        .delete-btn {
            background: #ff7875; 
            color: white; 
            border: none; 
            border-radius: 2px; 
            padding: 1px 4px; 
            cursor: pointer; 
            font-size: 0.65rem;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>新工艺步骤布局预览</h2>
        <p>新的布局设计："人or设备"移到步骤标题右边，三个标题各占一行</p>
        
        <div class="step-preview">
            <!-- 步骤标题行：步骤号 + 人or设备 + 删除按钮 -->
            <div class="step-header">
                <div class="step-title-group">
                    <h6 class="step-title">步骤 1</h6>
                    <div class="operator-group">
                        <label class="operator-label">人or设备:</label>
                        <select>
                            <option>人</option>
                        </select>
                    </div>
                </div>
                <button class="delete-btn">×</button>
            </div>
            
            <!-- 第一行：工艺过程描述 -->
            <div class="content-row">
                <label>工艺过程描述:</label>
                <textarea placeholder="请输入工艺过程描述">人工检查包装材料完整性</textarea>
            </div>
            
            <!-- 第二行：产品特性要求 -->
            <div class="content-row">
                <label>产品特性要求:</label>
                <textarea placeholder="请输入产品特性要求">包装材料无破损，标签清晰</textarea>
            </div>
            
            <!-- 第三行：过程防错要求 -->
            <div class="content-row">
                <label>过程防错要求:</label>
                <textarea placeholder="请输入过程防错要求">目视检查，发现问题立即停止</textarea>
            </div>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>新布局改进说明：</h3>
            <ul>
                <li>✅ "人or设备"移到步骤标题（步骤1/2/3...）右边</li>
                <li>✅ 步骤标题、人or设备选择器、删除按钮在同一行</li>
                <li>✅ 工艺过程描述单独占一行</li>
                <li>✅ 产品特性要求单独占一行</li>
                <li>✅ 过程防错要求单独占一行</li>
                <li>✅ 所有标签设置统一的最小宽度（90px）</li>
                <li>✅ 布局更加清晰，层次分明</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px;">
            <h3>布局结构：</h3>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 步骤 1    人or设备: [选择器]                      × │
├─────────────────────────────────────────────────────────┤
│ 工艺过程描述: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求: [──────────文本框──────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
    </div>
</body>
</html>
        """
        
        with open('new_layout_preview.html', 'w', encoding='utf-8') as f:
            f.write(preview_html)
        
        print("✅ 新布局预览文件已生成: new_layout_preview.html")
        print("💡 可以在浏览器中打开此文件查看新布局效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成预览失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试新的工艺步骤布局...\n")
    
    # 运行布局测试
    layout_ok = test_new_step_layout()
    
    # 生成布局预览
    preview_ok = generate_new_layout_preview()
    
    print(f"\n{'='*60}")
    if layout_ok:
        print("🎉 所有测试通过！")
        print("✅ '人or设备'已移到步骤标题右边")
        print("✅ 三个标题各自占据一行")
        print("✅ 布局结构清晰，层次分明")
        
        if preview_ok:
            print("✅ 新布局预览文件已生成")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    print(f"{'='*60}")
