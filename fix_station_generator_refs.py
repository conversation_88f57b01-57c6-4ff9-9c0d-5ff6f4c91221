#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复station_manager.js中的stationGenerator引用
"""

import re

def fix_station_generator_references():
    """修复stationGenerator引用"""
    print("🔧 修复stationGenerator引用...")
    
    try:
        # 读取文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 成功读取station_manager.js文件")
        
        # 替换模式：将 stationGenerator. 替换为 window.stationGenerator.
        # 但不替换已经是 window.stationGenerator 的情况
        pattern = r'(?<!window\.)stationGenerator\.'
        replacement = 'window.stationGenerator.'
        
        # 执行替换
        new_content = re.sub(pattern, replacement, content)
        
        # 计算替换次数
        original_matches = len(re.findall(r'stationGenerator\.', content))
        new_matches = len(re.findall(r'window\.stationGenerator\.', new_content))
        replaced_count = new_matches - len(re.findall(r'window\.stationGenerator\.', content))
        
        print(f"📊 替换统计:")
        print(f"   原始 stationGenerator. 引用: {original_matches}")
        print(f"   替换后 window.stationGenerator. 引用: {new_matches}")
        print(f"   实际替换次数: {replaced_count}")
        
        # 写回文件
        with open('static/js/station_manager.js', 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ 文件修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def fix_variable_references():
    """修复变量引用"""
    print("\n🔧 修复变量引用...")
    
    try:
        # 读取文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 需要替换的模式
        replacements = [
            # 修复条件检查中的stationGenerator引用
            (r'if \(typeof stationGenerator !== \'undefined\' && stationGenerator\)', 
             'if (typeof window.stationGenerator !== \'undefined\' && window.stationGenerator)'),
            
            (r'if \(stationGenerator && typeof stationGenerator\.', 
             'if (window.stationGenerator && typeof window.stationGenerator.'),
            
            # 修复赋值语句
            (r'stationGenerator = new StationGenerator\(\)', 
             'window.stationGenerator = new StationGenerator()'),
        ]
        
        for pattern, replacement in replacements:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                print(f"   ✅ 替换: {pattern[:50]}...")
        
        # 写回文件
        with open('static/js/station_manager.js', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 变量引用修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 变量引用修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复station_manager.js中的stationGenerator引用...")
    print("=" * 60)
    
    success1 = fix_station_generator_references()
    success2 = fix_variable_references()
    
    print("=" * 60)
    if success1 and success2:
        print("🎉 所有修复完成！")
        print("\n✅ 修复内容:")
        print("1. 将所有 stationGenerator. 替换为 window.stationGenerator.")
        print("2. 修复条件检查中的变量引用")
        print("3. 修复赋值语句中的变量引用")
        print("\n🔧 下一步:")
        print("1. 重启Flask应用")
        print("2. 访问 http://localhost:5000/js-test 测试")
        return True
    else:
        print("❌ 修复过程中出现错误")
        return False

if __name__ == "__main__":
    main()
