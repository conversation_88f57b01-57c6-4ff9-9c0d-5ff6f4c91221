"""
解析SAB夹具清单的真实数据结构
"""
import pandas as pd
import json

def parse_fixture_sheet(sheet_name):
    """解析单个夹具清单工作表"""
    
    df = pd.read_excel('Linespec-data-SAB-V0.xlsx', sheet_name=sheet_name)
    
    # 找到表头行（包含"工站"、"内容"等的行）
    header_row = None
    for i, row in df.iterrows():
        if pd.notna(row.iloc[0]) and '工站' in str(row.iloc[0]):
            header_row = i
            break
    
    if header_row is None:
        return None
    
    # 重新设置列名
    columns = []
    for j, col_value in enumerate(df.iloc[header_row]):
        if pd.notna(col_value):
            columns.append(str(col_value).strip())
        else:
            columns.append(f'Column_{j}')
    
    # 提取数据行
    data_rows = []
    current_station = None
    
    for i in range(header_row + 1, len(df)):
        row = df.iloc[i]
        
        # 检查是否是新的工站
        if pd.notna(row.iloc[0]) and str(row.iloc[0]).strip():
            station_text = str(row.iloc[0]).strip()
            if 'ST' in station_text or '工站' in station_text:
                current_station = station_text
        
        # 提取设备/夹具信息
        if pd.notna(row.iloc[2]):  # 设备/夹具/标准件列
            equipment_name = str(row.iloc[2]).strip()
            brand = str(row.iloc[3]).strip() if pd.notna(row.iloc[3]) else ''
            model = str(row.iloc[4]).strip() if pd.notna(row.iloc[4]) else ''
            quantity = str(row.iloc[5]).strip() if pd.notna(row.iloc[5]) else '1'
            content = str(row.iloc[1]).strip() if pd.notna(row.iloc[1]) else ''
            
            data_rows.append({
                'station': current_station or 'ST00',
                'content': content,
                'equipment_name': equipment_name,
                'brand': brand,
                'model': model,
                'quantity': quantity
            })
    
    return data_rows

def parse_all_fixture_data():
    """解析所有SAB类别的夹具数据"""
    
    print("=== 解析SAB夹具清单数据 ===\n")
    
    sab_types = ['A', 'B', 'C', 'D', 'E', 'F']
    all_fixture_data = {}
    
    for sab_type in sab_types:
        sheet_name = f'SAB-{sab_type}设备夹具清单'
        print(f"📋 解析 {sheet_name}")
        
        try:
            fixture_data = parse_fixture_sheet(sheet_name)
            
            if fixture_data:
                all_fixture_data[f'SAB-{sab_type}'] = fixture_data
                
                print(f"   ✅ 解析成功，共 {len(fixture_data)} 条记录")
                
                # 显示前几条数据
                print("   示例数据:")
                for i, item in enumerate(fixture_data[:3]):
                    print(f"     {i+1}. 工站: {item['station']}")
                    print(f"        设备: {item['equipment_name']}")
                    print(f"        品牌: {item['brand']}")
                    print(f"        型号: {item['model']}")
                    print(f"        数量: {item['quantity']}")
                    print()
                
                # 统计信息
                stations = set(item['station'] for item in fixture_data)
                brands = set(item['brand'] for item in fixture_data if item['brand'])
                
                print(f"   📊 统计: {len(stations)} 个工站, {len(brands)} 个品牌")
                print(f"   🏭 工站: {', '.join(sorted(stations))}")
                
            else:
                print(f"   ❌ 解析失败")
                
        except Exception as e:
            print(f"   ❌ 解析错误: {str(e)}")
        
        print()
    
    return all_fixture_data

def generate_fixture_summary(all_fixture_data):
    """生成夹具清单汇总信息"""
    
    print("=== 夹具清单汇总分析 ===\n")
    
    total_items = 0
    all_stations = set()
    all_brands = set()
    all_equipment_types = set()
    
    sab_summary = {}
    
    for sab_type, fixture_data in all_fixture_data.items():
        stations = set(item['station'] for item in fixture_data)
        brands = set(item['brand'] for item in fixture_data if item['brand'])
        equipment_types = set(item['equipment_name'] for item in fixture_data)
        
        sab_summary[sab_type] = {
            'total_items': len(fixture_data),
            'stations': list(stations),
            'brands': list(brands),
            'equipment_types': list(equipment_types)
        }
        
        total_items += len(fixture_data)
        all_stations.update(stations)
        all_brands.update(brands)
        all_equipment_types.update(equipment_types)
        
        print(f"📊 {sab_type}:")
        print(f"   设备数量: {len(fixture_data)}")
        print(f"   工站数量: {len(stations)}")
        print(f"   品牌数量: {len(brands)}")
        print(f"   主要设备: {', '.join(list(equipment_types)[:5])}")
        print()
    
    print(f"🎯 总体统计:")
    print(f"   总设备数量: {total_items}")
    print(f"   总工站数量: {len(all_stations)}")
    print(f"   总品牌数量: {len(all_brands)}")
    print(f"   设备类型数量: {len(all_equipment_types)}")
    print()
    
    print(f"🏭 所有工站: {', '.join(sorted(all_stations))}")
    print(f"🏢 主要品牌: {', '.join(sorted(list(all_brands)[:10]))}")
    
    return sab_summary

def generate_fixture_ui_components(all_fixture_data):
    """生成夹具清单UI组件"""
    
    print("\n=== 生成夹具清单UI组件 ===\n")
    
    # 生成CSS样式
    css_styles = '''
/* 夹具清单样式 */
.fixture-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.control-group select,
.control-group input {
    padding: 0.5rem;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    font-size: 0.9rem;
}

.fixture-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.summary-number {
    font-size: 2rem;
    font-weight: bold;
    color: #1a73e8;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: #666;
    font-size: 0.9rem;
}

.fixture-table-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.fixture-table {
    width: 100%;
    border-collapse: collapse;
}

.fixture-table th {
    background: #1a73e8;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 500;
}

.fixture-table td {
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.fixture-table tbody tr:hover {
    background: #f5f5f5;
}

.fixture-category-badge {
    display: inline-block;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.fixture-category-badge.sab-a { background: #ff6b6b; }
.fixture-category-badge.sab-b { background: #4ecdc4; }
.fixture-category-badge.sab-c { background: #45b7d1; }
.fixture-category-badge.sab-d { background: #96ceb4; }
.fixture-category-badge.sab-e { background: #feca57; }
.fixture-category-badge.sab-f { background: #ff9ff3; }

.fixture-details {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
'''
    
    # 生成JavaScript功能
    js_functions = '''
// 夹具清单管理功能
let fixtureData = ''' + json.dumps(all_fixture_data, ensure_ascii=False, indent=2) + ''';
let filteredData = fixtureData;

// 初始化夹具清单
function initFixtureList() {
    updateFixtureSummary();
    renderFixtureTable();
}

// 更新统计概览
function updateFixtureSummary() {
    let totalFixtures = 0;
    let totalStations = new Set();
    let activeCategories = 0;
    
    Object.keys(filteredData).forEach(sabType => {
        if (filteredData[sabType].length > 0) {
            activeCategories++;
            totalFixtures += filteredData[sabType].length;
            filteredData[sabType].forEach(item => {
                totalStations.add(item.station);
            });
        }
    });
    
    document.getElementById('total-fixtures').textContent = totalFixtures;
    document.getElementById('total-equipment').textContent = totalFixtures;
    document.getElementById('active-categories').textContent = activeCategories;
}

// 渲染夹具表格
function renderFixtureTable() {
    const tbody = document.getElementById('fixture-table-body');
    tbody.innerHTML = '';
    
    Object.keys(filteredData).forEach(sabType => {
        filteredData[sabType].forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><span class="fixture-category-badge ${sabType.toLowerCase().replace('-', '-')}">${sabType}</span></td>
                <td>${item.station}</td>
                <td>${item.equipment_name}</td>
                <td>${item.content}</td>
                <td>${item.brand}</td>
                <td>${item.model}</td>
                <td>${item.quantity}</td>
                <td>
                    <button onclick="viewFixtureDetails('${sabType}', '${item.equipment_name}')" class="btn-small">详情</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    });
}

// 筛选夹具
function filterFixtures() {
    const sabFilter = document.getElementById('sab-type-filter').value;
    
    if (sabFilter) {
        filteredData = { [sabFilter]: fixtureData[sabFilter] || [] };
    } else {
        filteredData = fixtureData;
    }
    
    updateFixtureSummary();
    renderFixtureTable();
}

// 搜索夹具
function searchFixtures() {
    const searchTerm = document.getElementById('fixture-search').value.toLowerCase();
    const sabFilter = document.getElementById('sab-type-filter').value;
    
    filteredData = {};
    
    Object.keys(fixtureData).forEach(sabType => {
        if (!sabFilter || sabType === sabFilter) {
            filteredData[sabType] = fixtureData[sabType].filter(item => 
                item.equipment_name.toLowerCase().includes(searchTerm) ||
                item.brand.toLowerCase().includes(searchTerm) ||
                item.model.toLowerCase().includes(searchTerm) ||
                item.station.toLowerCase().includes(searchTerm)
            );
        }
    });
    
    updateFixtureSummary();
    renderFixtureTable();
}

// 导出夹具清单
function exportFixtureList() {
    const data = {
        exportTime: new Date().toISOString(),
        fixtureData: filteredData
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `fixture_list_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
}

// 打印夹具清单
function printFixtureList() {
    window.print();
}

// 查看夹具详情
function viewFixtureDetails(sabType, equipmentName) {
    const item = fixtureData[sabType].find(item => item.equipment_name === equipmentName);
    if (item) {
        const detailsContent = `
            <h4>${item.equipment_name}</h4>
            <p><strong>SAB类别:</strong> ${sabType}</p>
            <p><strong>工站:</strong> ${item.station}</p>
            <p><strong>内容:</strong> ${item.content}</p>
            <p><strong>品牌:</strong> ${item.brand}</p>
            <p><strong>型号:</strong> ${item.model}</p>
            <p><strong>数量:</strong> ${item.quantity}</p>
        `;
        
        document.getElementById('fixture-details-content').innerHTML = detailsContent;
        document.getElementById('fixture-details').style.display = 'block';
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('fixture-table-body')) {
        initFixtureList();
    }
});
'''
    
    # 保存文件
    with open('fixture_styles.css', 'w', encoding='utf-8') as f:
        f.write(css_styles)
    
    with open('fixture_functions.js', 'w', encoding='utf-8') as f:
        f.write(js_functions)
    
    print("✅ CSS样式已保存到: fixture_styles.css")
    print("✅ JavaScript功能已保存到: fixture_functions.js")

def main():
    """主函数"""
    try:
        # 解析所有夹具数据
        all_fixture_data = parse_all_fixture_data()
        
        # 生成汇总信息
        sab_summary = generate_fixture_summary(all_fixture_data)
        
        # 生成UI组件
        generate_fixture_ui_components(all_fixture_data)
        
        # 保存完整数据
        with open('parsed_fixture_data.json', 'w', encoding='utf-8') as f:
            json.dump({
                'fixture_data': all_fixture_data,
                'summary': sab_summary
            }, f, ensure_ascii=False, indent=2)
        
        print("\n✅ 完整夹具数据已保存到: parsed_fixture_data.json")
        
    except Exception as e:
        print(f"❌ 解析过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
