#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的设备工站解析测试
"""

import re

def test_markdown_equipment_parsing():
    """测试Markdown格式的设备工站解析"""
    
    equipment_content = """
#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部
2. ST10工站配置支架压装、插线束高度检测以及发生器预装3小分站

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地
2. 3个小分站的气动需要分开控制，气压表需要设置在正对人侧

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

#### ST20 SAB-B-Folding
一、折叠设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
2. 设备前方需要2组安全光栅
"""
    
    print("=" * 60)
    print("测试Markdown格式设备工站解析")
    print("=" * 60)
    
    # 1. 查找工站
    markdown_pattern = r'####\s+ST(\d+)\s+([^\n]+)'
    markdown_matches = list(re.finditer(markdown_pattern, equipment_content))
    
    print(f"找到 {len(markdown_matches)} 个设备工站")
    
    for match in markdown_matches:
        station_number = match.group(1)
        station_name = match.group(2).strip()
        
        print(f"\n工站: ST{station_number} - {station_name}")
        
        # 2. 提取工站内容
        start_pos = match.start()
        
        # 查找下一个工站
        next_station_pattern = r"####\s+ST\d+"
        remaining_content = equipment_content[match.end():]
        
        end_pos = len(equipment_content)
        next_station_match = re.search(next_station_pattern, remaining_content)
        if next_station_match:
            end_pos = match.end() + next_station_match.start()
        
        station_content = equipment_content[start_pos:end_pos].strip()
        
        # 3. 解析详细信息
        details = extract_equipment_details(station_content)
        
        print(f"  机械要求: {details.get('mechanical_requirements', '未识别')[:80]}...")
        print(f"  电气要求: {details.get('electrical_requirements', '未识别')[:80]}...")
        print(f"  防错要求: {details.get('error_prevention_requirements', '未识别')[:80]}...")

def extract_equipment_details(equipment_content):
    """提取设备详细信息"""
    details = {
        'mechanical_requirements': '',
        'electrical_requirements': '',
        'error_prevention_requirements': ''
    }
    
    lines = equipment_content.split('\n')
    current_subsection = None
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 识别子部分
        if re.match(r'^（[一二三四五六七八九十]）', line):
            if '机械要求' in line:
                current_subsection = 'mechanical'
            elif '电气要求' in line:
                current_subsection = 'electrical'
            elif '防错及点检要求' in line:
                current_subsection = 'error_prevention'
            continue
        
        # 收集内容
        if current_subsection and line and not line.startswith(('####', '###', '一、', '二、')):
            if current_subsection == 'mechanical':
                if details['mechanical_requirements']:
                    details['mechanical_requirements'] += '\n' + line
                else:
                    details['mechanical_requirements'] = line
            elif current_subsection == 'electrical':
                if details['electrical_requirements']:
                    details['electrical_requirements'] += '\n' + line
                else:
                    details['electrical_requirements'] = line
            elif current_subsection == 'error_prevention':
                if details['error_prevention_requirements']:
                    details['error_prevention_requirements'] += '\n' + line
                else:
                    details['error_prevention_requirements'] = line
    
    return details

def test_content_splitting():
    """测试内容分割"""
    
    print("\n" + "=" * 60)
    print("测试内容分割")
    print("=" * 60)
    
    full_content = """
### 一、工艺部分

#### ST10 SAB-B-Pre-assembly
（一）
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
   - 人or设备: 人

### 二、设备部分

#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
"""
    
    # 查找工艺部分
    process_markers = ['### 一、工艺部分']
    process_end_markers = ['### 二、设备部分']
    
    process_content = extract_section(full_content, process_markers, process_end_markers)
    
    # 查找设备部分
    equipment_markers = ['### 二、设备部分']
    equipment_end_markers = ['### 三、', '## ']
    
    equipment_content = extract_section(full_content, equipment_markers, equipment_end_markers)
    
    print(f"工艺部分长度: {len(process_content)}")
    print(f"设备部分长度: {len(equipment_content)}")
    
    if process_content:
        print(f"工艺部分预览: {process_content[:100]}...")
    
    if equipment_content:
        print(f"设备部分预览: {equipment_content[:100]}...")

def extract_section(content, start_markers, end_markers):
    """提取内容部分"""
    for marker in start_markers:
        if marker in content:
            start_pos = content.find(marker)
            if start_pos != -1:
                start_pos += len(marker)
                remaining_content = content[start_pos:]
                
                end_pos = len(remaining_content)
                for end_marker in end_markers:
                    if end_marker in remaining_content:
                        end_pos = min(end_pos, remaining_content.find(end_marker))
                
                if end_pos > 0:
                    return remaining_content[:end_pos].strip()
    
    return ""

if __name__ == "__main__":
    test_markdown_equipment_parsing()
    test_content_splitting()
    
    print("\n" + "=" * 60)
    print("简化测试完成")
    print("=" * 60)
