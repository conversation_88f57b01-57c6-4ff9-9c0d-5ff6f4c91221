<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤插入菜单测试</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 步骤插入菜单测试页面</h1>
        
        <div class="test-section">
            <h2>测试说明</h2>
            <p>1. 每个步骤框右边只有一个"+"按钮</p>
            <p>2. 鼠标悬停在"+"按钮上时显示菜单</p>
            <p>3. 菜单包含"在此步骤前插入"和"在此步骤后插入"两个选项</p>
            <p>4. 保留标题行的"↑插入"和"+添加"按钮</p>
        </div>
        
        <div class="test-section">
            <div id="stations-list"></div>
        </div>
        
        <div class="test-section">
            <button onclick="initTestData()">初始化测试数据</button>
            <button onclick="testInsertMenu()">测试插入菜单</button>
        </div>
    </div>

    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>
    
    <script>
        function initTestData() {
            const testStation = {
                station_number: '10',
                station_name: '测试工站',
                content: '测试内容',
                process_steps: [
                    {
                        step_number: '1',
                        description: '第一个步骤',
                        operator: '人',
                        quality_requirements: '质量要求1',
                        error_prevention: '防错要求1'
                    },
                    {
                        step_number: '2', 
                        description: '第二个步骤',
                        operator: '设备',
                        quality_requirements: '质量要求2',
                        error_prevention: '防错要求2'
                    }
                ]
            };
            
            if (typeof StationGenerator !== 'undefined') {
                const generator = new StationGenerator();
                generator.generateProcessStations([testStation]);
                console.log('测试数据初始化完成');
            } else {
                console.error('StationGenerator未定义');
            }
        }
        
        function testInsertMenu() {
            console.log('测试插入菜单功能...');
            
            // 检查菜单元素是否存在
            const menus = document.querySelectorAll('.step-insert-menu');
            console.log(`找到 ${menus.length} 个插入菜单`);
            
            const dropdowns = document.querySelectorAll('.step-insert-dropdown');
            console.log(`找到 ${dropdowns.length} 个下拉菜单`);
            
            // 检查函数是否存在
            const functions = ['showStepInsertMenu', 'hideStepInsertMenu', 'hideAllStepInsertMenus'];
            functions.forEach(funcName => {
                console.log(`${funcName}: ${typeof window[funcName] === 'function' ? '存在' : '不存在'}`);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，可以开始测试');
        });
    </script>
</body>
</html>