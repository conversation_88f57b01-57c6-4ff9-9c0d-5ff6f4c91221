
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工站标题编辑预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .station-preview { 
            border: 1px solid #e0e0e0; 
            border-radius: 6px; 
            padding: 0.8rem; 
            margin-bottom: 1rem; 
            background: #fafbfc; 
        }
        .station-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.6rem; 
            padding-bottom: 0.4rem; 
            border-bottom: 1px solid #e0e0e0; 
        }
        .station-title-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .collapse-btn {
            background: none; 
            border: none; 
            color: #1a73e8; 
            cursor: pointer; 
            font-size: 0.8rem; 
            padding: 0; 
            width: 16px; 
            height: 16px; 
            display: flex; 
            align-items: center; 
            justify-content: center;
        }
        .station-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 1rem; 
            font-weight: 600; 
            cursor: pointer; 
            padding: 2px 4px; 
            border-radius: 3px; 
            transition: background-color 0.2s;
        }
        .station-title:hover {
            background-color: #f0f7ff;
        }
        .delete-btn {
            background: #ff4d4f; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            padding: 2px 6px; 
            cursor: pointer; 
            font-size: 0.7rem;
        }
        .edit-input {
            margin: 0;
            color: #1a73e8;
            font-size: 1rem;
            font-weight: 600;
            background: white;
            border: 2px solid #1a73e8;
            border-radius: 3px;
            padding: 2px 4px;
            width: 300px;
            font-family: inherit;
        }
        .demo-section {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 4px;
        }
        .instruction {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: 4px;
        }
    </style>
    <script>
        function simulateDoubleClick(element) {
            const currentText = element.textContent.trim();
            const match = currentText.match(/^ST(\d+)\s*-\s*(.+)$/);
            if (!match) return;
            
            const input = document.createElement('input');
            input.type = 'text';
            input.value = currentText;
            input.className = 'edit-input';
            
            element.style.display = 'none';
            element.parentNode.insertBefore(input, element);
            
            input.focus();
            input.select();
            
            const saveTitle = () => {
                const newValue = input.value.trim();
                const newMatch = newValue.match(/^ST(\d+)\s*-\s*(.+)$/);
                if (!newMatch) {
                    alert('请使用正确的格式：ST[数字] - [工站名称]');
                    input.focus();
                    return;
                }
                
                element.textContent = newValue;
                input.remove();
                element.style.display = '';
            };
            
            const cancelEdit = () => {
                input.remove();
                element.style.display = '';
            };
            
            input.addEventListener('blur', saveTitle);
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTitle();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });
        }
    </script>
</head>
<body>
    <div class="preview-container">
        <h2>工站标题编辑功能预览</h2>
        <p>新的设计：删除重复的工站号和工站名称输入框，标题支持双击编辑</p>
        
        <div class="station-preview">
            <div class="station-header">
                <div class="station-title-group">
                    <button class="collapse-btn" title="折叠/展开">
                        <span>▼</span>
                    </button>
                    <h4 class="station-title" 
                        ondblclick="simulateDoubleClick(this)"
                        title="双击编辑工站标题">
                        ST10 - Pre-assembly
                    </h4>
                </div>
                <button class="delete-btn">删除</button>
            </div>
            
            <div style="color: #666; font-size: 0.9rem; padding: 1rem; background: white; border-radius: 4px;">
                <p>📝 工艺步骤内容区域...</p>
                <p>（原来的工站号和工站名称输入框已被移除）</p>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>功能改进说明：</h3>
            <ul>
                <li>✅ 删除了重复的"工站号"和"工站名称"输入框</li>
                <li>✅ 工站标题支持双击编辑功能</li>
                <li>✅ 鼠标悬停时显示背景高亮效果</li>
                <li>✅ 编辑时显示蓝色边框的输入框</li>
                <li>✅ 支持Enter键保存，Escape键取消</li>
                <li>✅ 自动格式验证（ST[数字] - [名称]）</li>
                <li>✅ 失焦时自动保存</li>
            </ul>
        </div>
        
        <div class="instruction">
            <h3>使用说明：</h3>
            <ol>
                <li><strong>双击标题</strong>：双击"ST10 - Pre-assembly"等标题进行编辑</li>
                <li><strong>编辑格式</strong>：必须使用"ST[数字] - [工站名称]"的格式</li>
                <li><strong>保存方式</strong>：
                    <ul>
                        <li>按Enter键保存</li>
                        <li>点击其他地方（失焦）保存</li>
                    </ul>
                </li>
                <li><strong>取消编辑</strong>：按Escape键取消编辑</li>
                <li><strong>视觉反馈</strong>：
                    <ul>
                        <li>鼠标悬停时背景变浅蓝色</li>
                        <li>编辑时显示蓝色边框输入框</li>
                        <li>鼠标指针变为手型，提示可点击</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #fff7e6; border-radius: 4px;">
            <h3>布局对比：</h3>
            
            <h4>修改前（重复内容）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ ▼ ST10 - Pre-assembly                            删除 │
├─────────────────────────────────────────────────────────┤
│ 工站号: [ST10]     工站名称: [Pre-assembly]           │  ← 重复内容
├─────────────────────────────────────────────────────────┤
│ 工艺步骤内容...                                       │
└─────────────────────────────────────────────────────────┘
            </pre>
            
            <h4>修改后（简洁布局）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ ▼ ST10 - Pre-assembly (双击编辑)               删除 │  ← 可编辑标题
├─────────────────────────────────────────────────────────┤
│ 工艺步骤内容...                                       │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
    </div>
</body>
</html>
        