"""
分析真实的SAB数据结构
"""
import pandas as pd

def analyze_sab_data():
    """分析SAB数据文件的结构"""
    
    # 读取Excel文件
    xl = pd.ExcelFile('Linespec-data-SAB-V0.xlsx')
    
    print("=== SAB数据文件分析 ===\n")
    
    # 1. 分析list1 - 零件对应关系
    print("1. List1 - 零件对应关系:")
    df_list1 = pd.read_excel('Linespec-data-SAB-V0.xlsx', sheet_name='list1')
    
    # 获取所有零件列
    component_columns = [col for col in df_list1.columns if col not in ['Product family', 'Process type', 'CT']]
    print(f"   所有零件: {component_columns}")
    
    # 分析每个SAB类别的零件
    sab_categories = {}
    for i, row in df_list1.iterrows():
        process_type = row['Process type']
        components = []
        for col in component_columns:
            if pd.notna(row[col]) and row[col] != '':
                components.append(col)
        sab_categories[process_type] = components
        print(f"   SAB-{process_type}: {components}")
    
    print()
    
    # 2. 分析list2 - 工艺流程
    print("2. List2 - 工艺流程:")
    df_list2 = pd.read_excel('Linespec-data-SAB-V0.xlsx', sheet_name='list2')
    print(f"   列名: {list(df_list2.columns)}")
    
    # 按工艺类型分组
    for process_type in df_list2['Process type'].unique():
        process_data = df_list2[df_list2['Process type'] == process_type]
        print(f"   {process_type}:")
        for _, row in process_data.iterrows():
            print(f"     {row['station']}: {row['Process name']} (步骤{row['station step']}, CT:{row['CT/s']}s)")
    
    print()
    
    # 3. 分析各个SAB类别的详细工作表
    for sheet_name in xl.sheet_names:
        if sheet_name.startswith('SAB-') and not '设备夹具清单' in sheet_name:
            print(f"3. {sheet_name} - 详细工艺:")
            df = pd.read_excel('Linespec-data-SAB-V0.xlsx', sheet_name=sheet_name)
            print(f"   列名: {list(df.columns)}")
            
            # 显示前几行非空数据
            non_empty_rows = df.dropna(how='all').head(3)
            if not non_empty_rows.empty:
                print("   示例数据:")
                for i, row in non_empty_rows.iterrows():
                    if pd.notna(row['Process type']):
                        print(f"     工艺: {row['Process type']}")
                        print(f"     工站: {row['station']}")
                        print(f"     步骤: {row['step']}")
                        print(f"     描述: {row['process description']}")
                        print(f"     操作: {row['man/machine']}")
                        break
            print()
    
    return sab_categories, component_columns

def generate_frontend_config(sab_categories, component_columns):
    """生成前端配置"""
    
    print("=== 前端配置生成 ===\n")
    
    # 生成JavaScript配置
    js_config = "// SAB类别与零件的映射关系（基于真实数据）\n"
    js_config += "const SAB_CATEGORIES = {\n"
    
    for category, components in sab_categories.items():
        js_config += f"    '{category}': {components},\n"
    
    js_config += "};\n\n"
    js_config += f"// 所有零件列表\nconst ALL_COMPONENTS = {component_columns};\n"
    
    print("JavaScript配置:")
    print(js_config)
    
    # 生成HTML零件选择配置
    print("\nHTML零件选择配置:")
    
    # 按类别分组零件
    category_components = {}
    for category, components in sab_categories.items():
        category_components[category] = components
    
    # 生成HTML
    html_config = ""
    category_names = {
        'A': '预装配',
        'B': '线束装配', 
        'C': '扭矩检测',
        'D': '硬盖装配',
        'E': '外壳装配',
        'F': '3D热成型'
    }
    
    category_icons = {
        'A': '🔧',
        'B': '🔌', 
        'C': '⚙️',
        'D': '🛡️',
        'E': '🏠',
        'F': '🔥'
    }
    
    for category in ['A', 'B', 'C', 'D', 'E', 'F']:
        if category in sab_categories:
            components = sab_categories[category]
            icon = category_icons.get(category, '📦')
            name = category_names.get(category, f'类别{category}')
            
            html_config += f'''
                        <!-- SAB-{category} {name}零件 -->
                        <div class="component-category">
                            <h6>{icon} {name}零件 (SAB-{category})</h6>
'''
            
            for component in components:
                comp_id = component.lower().replace(' ', '-')
                html_config += f'''                            <div class="component-item">
                                <input type="checkbox" id="comp-{comp_id}" value="{component}">
                                <label for="comp-{comp_id}">{component}</label>
                            </div>
'''
            
            html_config += "                        </div>\n"
    
    print(html_config)

if __name__ == "__main__":
    sab_categories, component_columns = analyze_sab_data()
    generate_frontend_config(sab_categories, component_columns)
