/**
 * 工站管理器 - 处理工站的增删改查操作
 */

// 全局工站数据存储
let processStationsData = [];
let equipmentStationsData = [];

// 全局工站生成器实例（使用station_generator.js中创建的实例）
// let stationGenerator = null; // 注释掉，使用全局实例

// 初始化工站生成器
document.addEventListener('DOMContentLoaded', function() {
    initializeStationGenerator();
});

/**
 * 初始化工站生成器（检查全局实例是否可用）
 */
function initializeStationGenerator() {
    if (typeof stationGenerator !== 'undefined' && stationGenerator !== null) {
        console.log('[DEBUG] 使用全局StationGenerator实例');
    } else if (typeof StationGenerator !== 'undefined') {
        // 如果全局实例不存在，创建一个新的
        window.window.window.stationGenerator = new StationGenerator();
        console.log('[DEBUG] 创建新的StationGenerator实例');
    } else {
        console.error('[ERROR] StationGenerator类未定义');
        // 延迟重试
        setTimeout(() => {
            console.log('[DEBUG] 延迟重试初始化StationGenerator');
            initializeStationGenerator();
        }, 1000);
    }
}

/**
 * 确保工站生成器可用
 * @returns {boolean} 是否可用
 */
function ensureStationGenerator() {
    // 检查全局stationGenerator实例
    if (typeof window.stationGenerator !== 'undefined' &&
        window.stationGenerator !== null &&
        typeof window.stationGenerator.generateProcessStations === 'function') {
        return true;
    }

    console.log('[DEBUG] 工站生成器不可用，尝试重新初始化');
    if (typeof StationGenerator !== 'undefined') {
        window.stationGenerator = new StationGenerator();
        console.log('[DEBUG] 重新创建StationGenerator实例');
        return true;
    } else {
        console.error('[ERROR] StationGenerator类未定义');
        return false;
    }
}

/**
 * 更新工艺工站信息
 * @param {number} stationIndex - 工站索引
 * @param {string} field - 字段名
 * @param {string} value - 新值
 */
function updateProcessStationInfo(stationIndex, field, value) {
    if (processStationsData[stationIndex]) {
        processStationsData[stationIndex][field] = value;
        console.log(`更新工艺工站 ${stationIndex} 的 ${field}: ${value}`);
    }
}

/**
 * 更新工艺步骤信息
 * @param {number} stationIndex - 工站索引
 * @param {number} stepIndex - 步骤索引
 * @param {string} field - 字段名
 * @param {string} value - 新值
 */
function updateProcessStep(stationIndex, stepIndex, field, value) {
    if (processStationsData[stationIndex] && 
        processStationsData[stationIndex].process_steps && 
        processStationsData[stationIndex].process_steps[stepIndex]) {
        processStationsData[stationIndex].process_steps[stepIndex][field] = value;
        console.log(`更新工艺工站 ${stationIndex} 步骤 ${stepIndex} 的 ${field}: ${value}`);
    }
}

/**
 * 添加工艺步骤（在末尾添加）
 * @param {number} stationIndex - 工站索引
 */
function addProcessStep(stationIndex) {
    console.log(`[DEBUG] addProcessStep called: station=${stationIndex}`);
    if (!processStationsData[stationIndex]) {
        console.error(`[ERROR] Station ${stationIndex} not found`);
        return;
    }

    if (!processStationsData[stationIndex].process_steps) {
        processStationsData[stationIndex].process_steps = [];
    }

    const stepCount = processStationsData[stationIndex].process_steps.length;
    insertProcessStep(stationIndex, stepCount);
}

// 确保函数暴露到全局作用域
window.addProcessStep = addProcessStep;

/**
 * 在指定位置插入新的工艺步骤
 * @param {number} stationIndex - 工站索引
 * @param {number} insertIndex - 插入位置索引
 */
function insertProcessStep(stationIndex, insertIndex) {
    console.log(`[DEBUG] insertProcessStep called: station=${stationIndex}, insertIndex=${insertIndex}`);

    // 使用window.processStationsData或全局processStationsData
    const stationsData = window.processStationsData || processStationsData;
    console.log(`[DEBUG] Using stationsData, length: ${stationsData ? stationsData.length : 'undefined'}`);

    if (!stationsData[stationIndex]) {
        console.error(`[ERROR] Station ${stationIndex} not found in stationsData`);
        console.log(`[DEBUG] Available stations: ${stationsData ? stationsData.length : 'none'}`);
        return;
    }

    if (!stationsData[stationIndex].process_steps) {
        console.log(`[DEBUG] Creating process_steps array for station ${stationIndex}`);
        stationsData[stationIndex].process_steps = [];
    }

    const steps = stationsData[stationIndex].process_steps;
    console.log(`[DEBUG] Current steps count: ${steps.length}`);

    // 确保插入索引在有效范围内
    const originalIndex = insertIndex;
    insertIndex = Math.max(0, Math.min(insertIndex, steps.length));
    if (originalIndex !== insertIndex) {
        console.log(`[DEBUG] Insert index adjusted from ${originalIndex} to ${insertIndex}`);
    }

    const newStep = {
        step_number: (insertIndex + 1).toString(),
        description: '请输入工艺过程描述',
        operator: '',
        quality_requirements: '请输入产品特性要求',
        error_prevention: '请输入过程防错要求'
    };

    console.log(`[DEBUG] Creating new step:`, newStep);

    // 在指定位置插入新步骤
    steps.splice(insertIndex, 0, newStep);
    console.log(`[DEBUG] Step inserted, new steps count: ${steps.length}`);

    // 重新编号所有步骤
    steps.forEach((step, index) => {
        step.step_number = (index + 1).toString();
    });
    console.log(`[DEBUG] Steps renumbered`);

    // 同步数据到全局变量
    if (stationsData === window.processStationsData && typeof processStationsData !== 'undefined') {
        processStationsData.length = 0;
        processStationsData.push(...window.processStationsData);
        console.log(`[DEBUG] Synced data to global processStationsData`);
    } else if (stationsData === processStationsData && typeof window.processStationsData !== 'undefined') {
        window.processStationsData.length = 0;
        window.processStationsData.push(...processStationsData);
        console.log(`[DEBUG] Synced data to window.processStationsData`);
    }

    // 标记使用新工站系统
    window.usingNewStationSystem = true;

    // 重新生成该工站的HTML
    console.log(`[DEBUG] Regenerating station ${stationIndex}`);
    regenerateProcessStation(stationIndex);

    console.log(`[SUCCESS] 在工站 ${stationIndex} 的位置 ${insertIndex} 插入新步骤`);
}

// 确保函数暴露到全局作用域
window.insertProcessStep = insertProcessStep;

/**
 * 在指定步骤前插入新步骤
 * @param {number} stationIndex - 工站索引
 * @param {number} stepIndex - 目标步骤索引
 */
function insertProcessStepBefore(stationIndex, stepIndex) {
    console.log(`[DEBUG] insertProcessStepBefore called: station=${stationIndex}, step=${stepIndex}`);
    insertProcessStep(stationIndex, stepIndex);
}

/**
 * 在指定步骤后插入新步骤
 * @param {number} stationIndex - 工站索引
 * @param {number} stepIndex - 目标步骤索引
 */
function insertProcessStepAfter(stationIndex, stepIndex) {
    console.log(`[DEBUG] insertProcessStepAfter called: station=${stationIndex}, step=${stepIndex}`);
    insertProcessStep(stationIndex, stepIndex + 1);
}

// 确保函数暴露到全局作用域
window.insertProcessStepBefore = insertProcessStepBefore;
window.insertProcessStepAfter = insertProcessStepAfter;

/**
 * 删除工艺步骤
 * @param {number} stationIndex - 工站索引
 * @param {number} stepIndex - 步骤索引
 */
function deleteProcessStep(stationIndex, stepIndex) {
    console.log(`[DEBUG] deleteProcessStep called: station=${stationIndex}, step=${stepIndex}`);

    // 使用window.processStationsData或全局processStationsData
    const stationsData = window.processStationsData || processStationsData;
    console.log(`[DEBUG] Using stationsData for deletion, length: ${stationsData ? stationsData.length : 'undefined'}`);

    if (stationsData[stationIndex] &&
        stationsData[stationIndex].process_steps) {

        if (confirm('确定要删除这个工艺步骤吗？')) {
            console.log(`[DEBUG] User confirmed deletion of step ${stepIndex}`);
            stationsData[stationIndex].process_steps.splice(stepIndex, 1);

            // 重新编号步骤
            stationsData[stationIndex].process_steps.forEach((step, index) => {
                step.step_number = (index + 1).toString();
            });

            console.log(`[DEBUG] Steps renumbered, regenerating station ${stationIndex}`);

            // 同步数据到全局变量
            if (stationsData === window.processStationsData && typeof processStationsData !== 'undefined') {
                processStationsData.length = 0;
                processStationsData.push(...window.processStationsData);
                console.log(`[DEBUG] Synced data to global processStationsData after deletion`);
            } else if (stationsData === processStationsData && typeof window.processStationsData !== 'undefined') {
                window.processStationsData.length = 0;
                window.processStationsData.push(...processStationsData);
                console.log(`[DEBUG] Synced data to window.processStationsData after deletion`);
            }

            // 重新生成该工站的HTML
            regenerateProcessStation(stationIndex);
        } else {
            console.log(`[DEBUG] User cancelled deletion`);
        }
    } else {
        console.error(`[ERROR] Invalid station or steps data: station=${stationIndex}, step=${stepIndex}`);
    }
}

// 确保函数暴露到全局作用域
window.deleteProcessStep = deleteProcessStep;

/**
 * 删除工艺工站
 * @param {number} stationIndex - 工站索引
 */
function deleteProcessStation(stationIndex) {
    if (confirm('确定要删除这个工艺工站吗？')) {
        console.log(`[DEBUG] deleteProcessStation: 删除工站 ${stationIndex}`);

        // 从数据数组中删除指定工站
        processStationsData.splice(stationIndex, 1);

        // 同步到window.processStationsData
        if (typeof window.processStationsData !== 'undefined') {
            window.processStationsData.splice(stationIndex, 1);
        }

        // 删除对应的DOM元素
        const stationElement = document.querySelector(`[data-station-index="${stationIndex}"]`);
        if (stationElement) {
            // 找到包含插入按钮的完整工站块
            let stationBlock = stationElement;
            while (stationBlock && !stationBlock.classList.contains('station-block')) {
                stationBlock = stationBlock.parentElement;
            }

            // 删除工站前的插入按钮（如果存在）
            const prevInsertBtn = stationBlock?.previousElementSibling;
            if (prevInsertBtn && prevInsertBtn.textContent.includes('在此前插入工站')) {
                prevInsertBtn.remove();
            }

            // 删除工站块
            if (stationBlock) {
                stationBlock.remove();
            }
        }

        // 重新索引剩余的工站
        reindexProcessStations();

        console.log(`[DEBUG] deleteProcessStation: 工站 ${stationIndex} 删除完成`);
    }
}

/**
 * 重新生成指定的工艺工站
 * @param {number} stationIndex - 工站索引
 */
function regenerateProcessStation(stationIndex) {
    console.log(`[DEBUG] regenerateProcessStation called: station=${stationIndex}`);

    // 使用window.processStationsData或全局processStationsData
    const stationsData = window.processStationsData || processStationsData;
    console.log(`[DEBUG] Using stationsData for regeneration, length: ${stationsData ? stationsData.length : 'undefined'}`);

    const stationBlock = document.querySelector(`[data-station-index="${stationIndex}"]`);
    if (stationBlock && stationsData[stationIndex]) {
        if (ensureStationGenerator()) {
            // 只更新工艺步骤容器，不替换整个工站块
            const stepsContainer = stationBlock.querySelector('.process-steps-container');
            if (stepsContainer) {
                const station = stationsData[stationIndex];
                const stepsHtml = station.process_steps ?
                    station.process_steps.map((step, stepIndex) =>
                        window.stationGenerator.createProcessStepHtml(step, stationIndex, stepIndex, station.process_steps.length)
                    ).join('') :
                    '';

                // 只更新步骤内容，保留步骤容器的头部
                const stepsContentDiv = stepsContainer.querySelector('div:last-child') || stepsContainer;
                if (stepsContentDiv !== stepsContainer) {
                    // 如果找到了步骤内容区域，只更新步骤
                    stepsContentDiv.innerHTML = stepsHtml;
                } else {
                    // 如果没有找到，重新生成整个步骤容器内容
                    stepsContainer.innerHTML = `
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <h5 style="margin: 0; color: #333; font-size: 0.9rem; font-weight: 500;">工艺步骤:</h5>
                            <div style="display: flex; gap: 0.3rem;">
                                <button onclick="insertProcessStep(${stationIndex}, 0)"
                                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.75rem;"
                                        title="在开头插入步骤">
                                    ↑插入
                                </button>
                                <button onclick="addProcessStep(${stationIndex})"
                                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.75rem;"
                                        title="在末尾添加步骤">
                                    +添加
                                </button>
                            </div>
                        </div>
                        ${stepsHtml}
                    `;
                }
                console.log(`[DEBUG] Station ${stationIndex} steps regenerated`);

                // 强制应用紧凑样式
                setTimeout(() => {
                    if (typeof window.forceCompactStepStyles === 'function') {
                        window.forceCompactStepStyles();
                    }
                }, 50);
            } else {
                console.error(`[ERROR] Steps container not found for station ${stationIndex}`);
            }
        } else {
            console.error('[ERROR] 无法初始化工站生成器');
        }
    } else {
        console.error(`[ERROR] Station block or data not found for index ${stationIndex}`);
        console.log(`[DEBUG] stationBlock exists: ${!!stationBlock}, stationsData[${stationIndex}] exists: ${!!stationsData[stationIndex]}`);
    }
}

// 确保函数暴露到全局作用域
window.regenerateProcessStation = regenerateProcessStation;

/**
 * 更新设备工站信息
 * @param {number} stationIndex - 工站索引
 * @param {string} field - 字段名
 * @param {string} value - 新值
 */
function updateEquipmentStationInfo(stationIndex, field, value) {
    if (equipmentStationsData[stationIndex]) {
        if (!equipmentStationsData[stationIndex].equipment_details) {
            equipmentStationsData[stationIndex].equipment_details = {};
        }
        equipmentStationsData[stationIndex].equipment_details[field] = value;

        // 同步新旧字段映射和Equipment/Fixture字段合并
        const details = equipmentStationsData[stationIndex].equipment_details;

        // 处理Equipment和Fixture字段更新时的合并逻辑
        if (field.startsWith('equipment_') || field.startsWith('fixture_')) {
            updateCombinedFields(details);
        }

        // 传统字段映射
        if (field === 'mechanical_requirements') {
            details['technical_requirements'] = value;
        } else if (field === 'electrical_requirements') {
            details['equipment_parameters'] = value;
        } else if (field === 'error_prevention_requirements') {
            details['safety_requirements'] = value;
        } else if (field === 'technical_requirements') {
            details['mechanical_requirements'] = value;
        } else if (field === 'equipment_parameters') {
            details['electrical_requirements'] = value;
        } else if (field === 'safety_requirements') {
            details['error_prevention_requirements'] = value;
        }

        console.log(`更新设备工站 ${stationIndex} 的 ${field}: ${value}`);
    }
}

/**
 * 初始化设备工站的图片和参数数据
 * @param {number} stationIndex - 工站索引
 */
function initializeEquipmentStationImageAndParameters(stationIndex) {
    // 从设备工站数据中恢复图片和参数
    const station = equipmentStationsData[stationIndex];
    if (station && station.equipment_details) {
        const details = station.equipment_details;

        // 恢复图片数据
        if (details.equipment_image_data && details.equipment_image_filename) {
            const imageData = {
                imageData: details.equipment_image_data,
                fileName: details.equipment_image_filename
            };
            if (typeof setEquipmentImageAndParameterData === 'function') {
                setEquipmentImageAndParameterData(stationIndex, { image: imageData });
            }
        }

        // 恢复参数数据
        const parameters = {};
        if (details.equipment_length) parameters.length = details.equipment_length;
        if (details.equipment_width) parameters.width = details.equipment_width;
        if (details.equipment_height) parameters.height = details.equipment_height;
        if (details.equipment_cycle_time) parameters.cycle_time = details.equipment_cycle_time;
        if (details.equipment_changeover_time) parameters.changeover_time = details.equipment_changeover_time;

        if (Object.keys(parameters).length > 0) {
            if (typeof setEquipmentImageAndParameterData === 'function') {
                setEquipmentImageAndParameterData(stationIndex, { parameters });
            }
        }

        // 恢复夹具文件数据（支持多夹具）
        if (details.fixture_files_data) {
            if (!window.fixtureFilesData) {
                window.fixtureFilesData = {};
            }

            // 检查数据格式：新格式（多夹具）或旧格式（单夹具）
            if (Array.isArray(details.fixture_files_data)) {
                // 旧格式：转换为新格式
                window.fixtureFilesData[stationIndex] = {
                    0: details.fixture_files_data
                };
            } else if (typeof details.fixture_files_data === 'object') {
                // 新格式：直接使用
                window.fixtureFilesData[stationIndex] = details.fixture_files_data;
            }

            // 延迟更新预览，确保DOM元素已创建
            setTimeout(() => {
                if (typeof updateFixtureFilesPreview === 'function') {
                    // 为每个夹具更新预览
                    Object.keys(window.fixtureFilesData[stationIndex]).forEach(fixtureIndex => {
                        const fixtureFiles = window.fixtureFilesData[stationIndex][fixtureIndex];
                        if (Array.isArray(fixtureFiles) && fixtureFiles.length > 0) {
                            updateFixtureFilesPreview(stationIndex, parseInt(fixtureIndex));

                            // 显示清空按钮
                            const clearBtn = document.getElementById(`clear-fixture-files-btn-${stationIndex}-${fixtureIndex}`);
                            if (clearBtn) {
                                clearBtn.style.display = 'inline-block';
                            }
                        }
                    });
                }
            }, 100);
        }
    }
}

/**
 * 保存设备工站的图片和参数数据到设备工站数据中
 * @param {number} stationIndex - 工站索引
 */
function saveEquipmentStationImageAndParameters(stationIndex) {
    if (equipmentStationsData[stationIndex]) {
        if (!equipmentStationsData[stationIndex].equipment_details) {
            equipmentStationsData[stationIndex].equipment_details = {};
        }

        const details = equipmentStationsData[stationIndex].equipment_details;

        // 保存图片数据
        const imageData = window.equipmentImagesData?.[stationIndex];
        if (imageData) {
            details.equipment_image_data = imageData.imageData;
            details.equipment_image_filename = imageData.fileName;
        }

        // 保存参数数据
        const parameters = window.equipmentParametersData?.[stationIndex];
        if (parameters) {
            details.equipment_length = parameters.length || '';
            details.equipment_width = parameters.width || '';
            details.equipment_height = parameters.height || '';
            details.equipment_cycle_time = parameters.cycle_time || '';
            details.equipment_changeover_time = parameters.changeover_time || '';
        }

        // 保存夹具文件数据（支持多夹具）
        const fixtureFilesData = window.fixtureFilesData?.[stationIndex];
        if (fixtureFilesData && Object.keys(fixtureFilesData).length > 0) {
            details.fixture_files_data = fixtureFilesData;

            // 计算总文件数量
            let totalFiles = 0;
            Object.values(fixtureFilesData).forEach(fixtureFiles => {
                if (Array.isArray(fixtureFiles)) {
                    totalFiles += fixtureFiles.length;
                }
            });
            details.fixture_files_count = totalFiles;
        } else {
            // 清除夹具文件数据
            delete details.fixture_files_data;
            delete details.fixture_files_count;
        }
    }
}

/**
 * 更新合并字段（将Equipment和Fixture内容合并）
 */
function updateCombinedFields(details) {
    // 合并机械要求
    details['mechanical_requirements'] = combineEquipmentFixtureContent(
        details['equipment_mechanical_requirements'] || '',
        details['fixture_mechanical_requirements'] || ''
    );

    // 合并电气要求
    details['electrical_requirements'] = combineEquipmentFixtureContent(
        details['equipment_electrical_requirements'] || '',
        details['fixture_electrical_requirements'] || ''
    );

    // 合并防错要求
    details['error_prevention_requirements'] = combineEquipmentFixtureContent(
        details['equipment_error_prevention_requirements'] || '',
        details['fixture_error_prevention_requirements'] || ''
    );

    // 同步到传统字段
    details['technical_requirements'] = details['mechanical_requirements'];
    details['equipment_parameters'] = details['electrical_requirements'];
    details['safety_requirements'] = details['error_prevention_requirements'];
}

/**
 * 合并Equipment和Fixture内容
 */
function combineEquipmentFixtureContent(equipmentContent, fixtureContent) {
    const combined = [];

    if (equipmentContent && equipmentContent.trim()) {
        combined.push(`【设备要求】\n${equipmentContent.trim()}`);
    }

    if (fixtureContent && fixtureContent.trim()) {
        combined.push(`【夹具要求】\n${fixtureContent.trim()}`);
    }

    return combined.join('\n\n');
}

/**
 * 删除设备工站
 * @param {number} stationIndex - 工站索引
 */
function deleteEquipmentStation(stationIndex) {
    if (confirm('确定要删除这个设备工站吗？')) {
        console.log(`[DEBUG] deleteEquipmentStation: 删除设备工站 ${stationIndex}`);

        // 从数据数组中删除指定工站
        equipmentStationsData.splice(stationIndex, 1);

        // 同步到window.equipmentStationsData
        if (typeof window.equipmentStationsData !== 'undefined') {
            window.equipmentStationsData.splice(stationIndex, 1);
        }

        // 删除对应的DOM元素
        const stationElement = document.querySelector(`[data-station-index="${stationIndex}"]`);
        if (stationElement) {
            // 找到包含插入按钮的完整设备工站块
            let stationBlock = stationElement;
            while (stationBlock && !stationBlock.classList.contains('equipment-station-block')) {
                stationBlock = stationBlock.parentElement;
            }

            // 删除工站前的插入按钮（如果存在）
            const prevInsertBtn = stationBlock?.previousElementSibling;
            if (prevInsertBtn && prevInsertBtn.textContent.includes('在此前插入设备工站')) {
                prevInsertBtn.remove();
            }

            // 删除工站块
            if (stationBlock) {
                stationBlock.remove();
            }
        }

        // 重新索引剩余的设备工站
        reindexEquipmentStations();

        console.log(`[DEBUG] deleteEquipmentStation: 设备工站 ${stationIndex} 删除完成`);
    }
}

/**
 * 重新索引工艺工站
 */
function reindexProcessStations() {
    console.log(`[DEBUG] reindexProcessStations: 开始重新索引工艺工站`);

    const container = document.getElementById('process-stations-list');
    if (!container) return;

    const stationBlocks = container.querySelectorAll('.station-block[data-station-index]');
    stationBlocks.forEach((block, newIndex) => {
        // 更新data-station-index属性
        block.setAttribute('data-station-index', newIndex);

        // 更新所有相关的onclick事件和ID
        updateStationBlockIds(block, newIndex, 'process');
    });

    console.log(`[DEBUG] reindexProcessStations: 重新索引完成，共 ${stationBlocks.length} 个工站`);
}

/**
 * 重新索引设备工站
 */
function reindexEquipmentStations() {
    console.log(`[DEBUG] reindexEquipmentStations: 开始重新索引设备工站`);

    const container = document.getElementById('equipment-stations-list');
    if (!container) return;

    const stationBlocks = container.querySelectorAll('.equipment-station-block[data-station-index]');
    stationBlocks.forEach((block, newIndex) => {
        // 更新data-station-index属性
        block.setAttribute('data-station-index', newIndex);

        // 更新所有相关的onclick事件和ID
        updateStationBlockIds(block, newIndex, 'equipment');
    });

    console.log(`[DEBUG] reindexEquipmentStations: 重新索引完成，共 ${stationBlocks.length} 个设备工站`);
}

/**
 * 更新工站块中的所有ID和事件处理器
 * @param {Element} block - 工站块元素
 * @param {number} newIndex - 新的索引
 * @param {string} type - 工站类型 ('process' 或 'equipment')
 */
function updateStationBlockIds(block, newIndex, type) {
    // 更新所有包含旧索引的onclick属性
    const clickableElements = block.querySelectorAll('[onclick]');
    clickableElements.forEach(element => {
        let onclick = element.getAttribute('onclick');
        if (onclick) {
            // 替换函数调用中的索引参数
            if (type === 'process') {
                onclick = onclick.replace(/deleteProcessStation\(\d+\)/g, `deleteProcessStation(${newIndex})`);
                onclick = onclick.replace(/toggleStationCollapse\(\d+\)/g, `toggleStationCollapse(${newIndex})`);
                onclick = onclick.replace(/makeStationTitleEditable\(\d+/g, `makeStationTitleEditable(${newIndex}`);
                onclick = onclick.replace(/insertProcessStepBefore\(\d+/g, `insertProcessStepBefore(${newIndex}`);
                onclick = onclick.replace(/insertProcessStepAfter\(\d+/g, `insertProcessStepAfter(${newIndex}`);
                onclick = onclick.replace(/deleteProcessStep\(\d+/g, `deleteProcessStep(${newIndex}`);
            } else if (type === 'equipment') {
                onclick = onclick.replace(/deleteEquipmentStation\(\d+\)/g, `deleteEquipmentStation(${newIndex})`);
                onclick = onclick.replace(/toggleEquipmentStationCollapse\(\d+\)/g, `toggleEquipmentStationCollapse(${newIndex})`);
                onclick = onclick.replace(/makeEquipmentStationTitleEditable\(\d+/g, `makeEquipmentStationTitleEditable(${newIndex}`);
            }
            element.setAttribute('onclick', onclick);
        }
    });

    // 更新所有包含索引的ID属性
    const elementsWithIds = block.querySelectorAll('[id*="-"]');
    elementsWithIds.forEach(element => {
        let id = element.getAttribute('id');
        if (id) {
            // 替换ID中的索引部分
            id = id.replace(/-\d+(-|$)/g, `-${newIndex}$1`);
            element.setAttribute('id', id);
        }
    });
}

/**
 * 在指定位置插入新的工艺工站
 * @param {number} insertIndex - 插入位置索引
 */
function insertProcessStation(insertIndex) {
    // 确保插入索引在有效范围内
    insertIndex = Math.max(0, Math.min(insertIndex, processStationsData.length));

    // 计算新工站编号，基于插入位置
    let newStationNumber;
    if (insertIndex === 0) {
        // 插入到开头
        newStationNumber = processStationsData.length > 0 ?
            Math.max(5, parseInt(processStationsData[0].station_number) - 5) : 10;
    } else if (insertIndex >= processStationsData.length) {
        // 插入到末尾
        newStationNumber = processStationsData.length > 0 ?
            parseInt(processStationsData[processStationsData.length - 1].station_number) + 5 : 10;
    } else {
        // 插入到中间
        const prevNumber = parseInt(processStationsData[insertIndex - 1].station_number);
        const nextNumber = parseInt(processStationsData[insertIndex].station_number);
        newStationNumber = Math.floor((prevNumber + nextNumber) / 2);

        // 如果中间值已经被占用或太接近，使用递增逻辑
        if (newStationNumber <= prevNumber || newStationNumber >= nextNumber) {
            newStationNumber = prevNumber + 1;
        }
    }

    const newStation = {
        station_number: newStationNumber.toString(),
        station_name: `新工站${newStationNumber}`,
        content: '',
        process_steps: [{
            step_number: '1',
            description: '请输入工艺过程描述',
            operator: '',
            quality_requirements: '请输入产品特性要求',
            error_prevention: '请输入过程防错要求'
        }]
    };

    // 在指定位置插入新工站
    processStationsData.splice(insertIndex, 0, newStation);

    // 同步到window.processStationsData
    if (typeof window.processStationsData !== 'undefined') {
        window.processStationsData.splice(insertIndex, 0, newStation);
    } else {
        window.processStationsData = [...processStationsData];
    }

    // 标记使用新工站系统
    window.usingNewStationSystem = true;

    // 使用新的单个插入方法，保留现有内容
    if (ensureStationGenerator()) {
        window.stationGenerator.insertSingleProcessStation(newStation, insertIndex);
    } else {
        console.error('[ERROR] 无法初始化工站生成器');
    }

    console.log(`在位置 ${insertIndex} 插入新工站 ST${newStationNumber}`);
}

/**
 * 在指定工站前插入新工站
 * @param {number} stationIndex - 目标工站索引
 */
function insertProcessStationBefore(stationIndex) {
    insertProcessStation(stationIndex);
}

/**
 * 在指定工站后插入新工站
 * @param {number} stationIndex - 目标工站索引
 */
function insertProcessStationAfter(stationIndex) {
    insertProcessStation(stationIndex + 1);
}

/**
 * 在指定位置前插入新的设备工站
 * @param {number} insertIndex - 插入位置索引
 */
function insertEquipmentStationBefore(insertIndex) {
    insertEquipmentStation(insertIndex);
}

/**
 * 在指定位置后插入新的设备工站
 * @param {number} insertIndex - 插入位置索引
 */
function insertEquipmentStationAfter(insertIndex) {
    insertEquipmentStation(insertIndex + 1);
}

/**
 * 在指定位置插入新的设备工站
 * @param {number} insertIndex - 插入位置索引
 */
function insertEquipmentStation(insertIndex) {
    // 确保插入索引在有效范围内
    insertIndex = Math.max(0, Math.min(insertIndex, equipmentStationsData.length));

    // 计算新工站号
    let newStationNumber = 10; // 默认从ST10开始

    if (equipmentStationsData.length > 0) {
        const prevNumber = insertIndex > 0 ? parseInt(equipmentStationsData[insertIndex - 1].station_number) : 0;
        const nextNumber = insertIndex < equipmentStationsData.length ? parseInt(equipmentStationsData[insertIndex].station_number) : 999;

        // 计算中间值
        newStationNumber = Math.floor((prevNumber + nextNumber) / 2);

        // 如果中间值已经被占用或太接近，使用递增逻辑
        if (newStationNumber <= prevNumber || newStationNumber >= nextNumber) {
            newStationNumber = prevNumber + 5; // 设备工站通常以5为间隔
        }
    }

    const newStation = {
        station_number: newStationNumber.toString(),
        station_name: `新设备工站${newStationNumber}`,
        content: '',
        equipment_details: {
            equipment_type: '',
            technical_requirements: '',
            equipment_parameters: '',
            safety_requirements: '',

            // 合并字段（向后兼容）- 设置默认值以触发Markdown格式
            mechanical_requirements: '【设备要求】\n\n【夹具要求】\n',
            electrical_requirements: '【设备要求】\n\n【夹具要求】\n',
            error_prevention_requirements: '【设备要求】\n\n【夹具要求】\n',

            // Equipment部分字段 - 设置默认占位符以触发Equipment和Fixture分离格式
            equipment_mechanical_requirements: '请输入设备机械要求',
            equipment_electrical_requirements: '请输入设备电气要求',
            equipment_error_prevention_requirements: '请输入设备防错及点检要求',

            // Fixture部分字段 - 设置默认占位符以触发Equipment和Fixture分离格式
            fixture_mechanical_requirements: '请输入夹具机械要求',
            fixture_electrical_requirements: '请输入夹具电气要求',
            fixture_error_prevention_requirements: '请输入夹具防错及点检要求'
        }
    };

    // 在指定位置插入新工站
    equipmentStationsData.splice(insertIndex, 0, newStation);

    // 使用新的单个插入方法，保留现有内容
    if (ensureStationGenerator()) {
        window.stationGenerator.insertSingleEquipmentStation(newStation, insertIndex);
    } else {
        console.error('[ERROR] 无法初始化工站生成器');
    }

    console.log(`在位置 ${insertIndex} 插入新设备工站 ST${newStationNumber}`);
}

/**
 * 折叠/展开设备工站
 * @param {number} stationIndex - 工站索引
 */
function toggleEquipmentStationCollapse(stationIndex) {
    const stationBlock = document.querySelector(`[data-station-index="${stationIndex}"].equipment-station-block`);
    if (!stationBlock) return;

    const content = stationBlock.querySelector('.station-content');
    const icon = stationBlock.querySelector('.collapse-icon');

    if (!content || !icon) return;

    const isCollapsed = content.style.display === 'none';

    if (isCollapsed) {
        content.style.display = 'block';
        icon.textContent = '▼';
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

/**
 * 使设备工站标题可编辑
 * @param {number} stationIndex - 工站索引
 * @param {HTMLElement} titleElement - 标题元素
 */
function makeEquipmentStationTitleEditable(stationIndex, titleElement) {
    // 获取当前标题文本
    const currentText = titleElement.textContent.trim();

    // 解析当前的工站号和工站名称
    const match = currentText.match(/^ST(\d+)\s*-\s*(.+)$/);
    if (!match) {
        console.error('无法解析设备工站标题格式');
        return;
    }

    const stationNumber = match[1];
    const stationName = match[2];

    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.value = `ST${stationNumber} - ${stationName}`;
    input.style.cssText = `
        margin: 0;
        color: #52c41a;
        font-size: 1.1rem;
        font-weight: 600;
        background: white;
        border: 2px solid #52c41a;
        border-radius: 3px;
        padding: 2px 4px;
        width: 300px;
        font-family: inherit;
    `;

    // 替换标题元素
    titleElement.style.display = 'none';
    titleElement.parentNode.insertBefore(input, titleElement);

    // 选中输入框内容
    input.focus();
    input.select();

    // 处理保存
    const saveTitle = () => {
        const newText = input.value.trim();
        const newMatch = newText.match(/^ST(\d+)\s*-\s*(.+)$/);

        if (newMatch) {
            const newStationNumber = newMatch[1];
            const newStationName = newMatch[2];

            // 更新数据
            if (equipmentStationsData[stationIndex]) {
                equipmentStationsData[stationIndex].station_number = newStationNumber;
                equipmentStationsData[stationIndex].station_name = newStationName;
            }

            // 更新显示
            titleElement.textContent = `ST${newStationNumber} - ${newStationName}`;
        }

        // 恢复标题显示
        titleElement.style.display = '';
        input.remove();
    };

    // 绑定事件
    input.addEventListener('blur', saveTitle);
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            saveTitle();
        } else if (e.key === 'Escape') {
            titleElement.style.display = '';
            input.remove();
        }
    });
}

/**
 * 处理AI生成内容的工站信息
 * @param {Object} data - AI返回的数据
 */
function handleAIGeneratedStations(data) {
    console.log('[DEBUG] handleAIGeneratedStations被调用，数据:', data);

    // 处理工艺工站
    if (data.process_stations && data.process_stations.length > 0) {
        console.log('[DEBUG] 开始处理工艺工站，数量:', data.process_stations.length);
        processStationsData = data.process_stations;

        // 检查stationGenerator是否存在
        if (typeof window.stationGenerator !== 'undefined' && window.stationGenerator) {
            console.log('[DEBUG] 调用window.stationGenerator.generateProcessStations');
            window.stationGenerator.generateProcessStations(processStationsData, false);
        } else {
            console.error('[ERROR] stationGenerator未定义或为空');
            // 尝试创建新的StationGenerator实例
            if (typeof StationGenerator !== 'undefined') {
                console.log('[DEBUG] 创建新的StationGenerator实例');
                window.window.stationGenerator = new StationGenerator();
                window.stationGenerator.generateProcessStations(processStationsData, false);
            } else {
                console.error('[ERROR] StationGenerator类未定义');
            }
        }

        // 显示成功消息
        showNotification(`成功识别到 ${data.process_stations.length} 个工艺工站`, 'success');
    } else {
        console.log('[DEBUG] 无工艺工站数据或数据为空');
    }

    // 处理设备工站
    if (data.equipment_stations && data.equipment_stations.length > 0) {
        console.log('[DEBUG] 开始处理设备工站，数量:', data.equipment_stations.length);
        equipmentStationsData = data.equipment_stations;

        // 检查stationGenerator是否存在
        if (typeof window.stationGenerator !== 'undefined' && window.stationGenerator) {
            console.log('[DEBUG] 调用window.stationGenerator.generateEquipmentStations');
            window.stationGenerator.generateEquipmentStations(equipmentStationsData);
        } else {
            console.error('[ERROR] stationGenerator未定义或为空');
            // 尝试创建新的StationGenerator实例
            if (typeof StationGenerator !== 'undefined') {
                console.log('[DEBUG] 创建新的StationGenerator实例');
                window.window.stationGenerator = new StationGenerator();
                window.stationGenerator.generateEquipmentStations(equipmentStationsData);
            } else {
                console.error('[ERROR] StationGenerator类未定义');
            }
        }

        // 显示成功消息
        showNotification(`成功识别到 ${data.equipment_stations.length} 个设备工站`, 'success');
    } else {
        console.log('[DEBUG] 无设备工站数据或数据为空');
    }
}

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('success', 'error', 'warning', 'info')
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 10000;
        max-width: 300px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
    `;

    // 根据类型设置背景色
    const colors = {
        success: '#52c41a',
        error: '#ff4d4f',
        warning: '#faad14',
        info: '#1890ff'
    };
    notification.style.backgroundColor = colors[type] || colors.info;

    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * 导出工站数据
 * @returns {Object} 包含所有工站数据的对象
 */
function exportStationsData() {
    return {
        processStations: processStationsData,
        equipmentStations: equipmentStationsData,
        exportTime: new Date().toISOString()
    };
}

/**
 * 导入工站数据
 * @param {Object} data - 工站数据
 */
function importStationsData(data) {
    if (data.processStations) {
        processStationsData = data.processStations;
        if (window.stationGenerator && typeof window.stationGenerator.generateProcessStations === 'function') {
            window.stationGenerator.generateProcessStations(processStationsData, false);
        } else {
            console.error('[ERROR] stationGenerator未定义或不可用');
        }
    }

    if (data.equipmentStations) {
        equipmentStationsData = data.equipmentStations;
        if (window.stationGenerator && typeof window.stationGenerator.generateEquipmentStations === 'function') {
            window.stationGenerator.generateEquipmentStations(equipmentStationsData);
        } else {
            console.error('[ERROR] stationGenerator未定义或不可用');
        }
    }

    showNotification('工站数据导入成功', 'success');
}

/**
 * 清空所有工站数据
 */
function clearAllStations() {
    if (confirm('确定要清空所有工站数据吗？此操作不可撤销。')) {
        processStationsData = [];
        equipmentStationsData = [];

        // 清空容器
        const processContainer = document.getElementById('stations-list');
        const equipmentContainer = document.getElementById('equipment-stations-list');

        if (processContainer) processContainer.innerHTML = '';
        if (equipmentContainer) equipmentContainer.innerHTML = '';

        showNotification('所有工站数据已清空', 'warning');
    }
}

/**
 * 切换工站折叠状态
 * @param {number} stationIndex - 工站索引
 */
function toggleStationCollapse(stationIndex) {
    const stationBlock = document.querySelector(`[data-station-index="${stationIndex}"]`);
    if (!stationBlock) return;

    const content = stationBlock.querySelector('.station-content');
    const icon = stationBlock.querySelector('.collapse-icon');

    if (!content || !icon) return;

    const isCollapsed = content.style.display === 'none';

    if (isCollapsed) {
        // 展开
        content.style.display = 'block';
        icon.textContent = '▼';
        stationBlock.style.marginBottom = '1rem';
    } else {
        // 折叠
        content.style.display = 'none';
        icon.textContent = '▶';
        stationBlock.style.marginBottom = '0.5rem';
    }
}

/**
 * 批量折叠/展开所有工站
 * @param {boolean} collapse - true为折叠，false为展开
 */
function toggleAllStations(collapse) {
    // 处理工艺工站
    const processStationBlocks = document.querySelectorAll('.station-block');
    // 处理设备工站
    const equipmentStationBlocks = document.querySelectorAll('.equipment-station-block');

    // 合并所有工站
    const allStationBlocks = [...processStationBlocks, ...equipmentStationBlocks];

    allStationBlocks.forEach((block, index) => {
        const content = block.querySelector('.station-content');
        const icon = block.querySelector('.collapse-icon');

        if (content && icon) {
            if (collapse) {
                content.style.display = 'none';
                icon.textContent = '▶';
                block.style.marginBottom = '0.5rem';
            } else {
                content.style.display = 'block';
                icon.textContent = '▼';
                block.style.marginBottom = '1rem';
            }
        }
    });

    showNotification(collapse ? '已折叠所有工站' : '已展开所有工站', 'info');
}

// 确保所有关键函数都暴露到全局作用域
(function() {
    console.log('[DEBUG] 检查函数暴露状态...');

    const requiredFunctions = [
        'addProcessStep',
        'insertProcessStep',
        'insertProcessStepBefore',
        'insertProcessStepAfter',
        'deleteProcessStep',
        'regenerateProcessStation',
        // 设备工站相关函数
        'insertEquipmentStationBefore',
        'insertEquipmentStationAfter',
        'insertEquipmentStation',
        'deleteEquipmentStation',
        'toggleEquipmentStationCollapse',
        'makeEquipmentStationTitleEditable',
        'initializeEquipmentStationImageAndParameters',
        'saveEquipmentStationImageAndParameters'
    ];

    requiredFunctions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`[DEBUG] ✅ ${funcName} 已暴露到全局作用域`);
        } else {
            console.error(`[ERROR] ❌ ${funcName} 未暴露到全局作用域`);
            // 尝试从当前作用域暴露
            if (typeof eval(funcName) === 'function') {
                window[funcName] = eval(funcName);
                console.log(`[DEBUG] 🔧 ${funcName} 已修复暴露`);
            }
        }
    });

    console.log('[DEBUG] 函数暴露检查完成');
})();

// 直接暴露关键函数到全局作用域
window.deleteEquipmentStation = deleteEquipmentStation;
window.addProcessStep = addProcessStep;
window.insertProcessStep = insertProcessStep;
window.insertProcessStepBefore = insertProcessStepBefore;
window.insertProcessStepAfter = insertProcessStepAfter;
window.deleteProcessStep = deleteProcessStep;

/**
 * 强制应用紧凑样式到所有步骤元素
 */
function forceCompactStepStyles() {
    // 首先强制步骤容器使用正确的布局
    const stepsContainers = document.querySelectorAll('.process-steps-container');
    stepsContainers.forEach(container => {
        container.style.setProperty('display', 'flex', 'important');
        container.style.setProperty('flex-direction', 'column', 'important');
        container.style.setProperty('width', '100%', 'important');
    });

    // 强制工站容器使用正确的布局
    const stationBlocks = document.querySelectorAll('.station-block');
    stationBlocks.forEach(block => {
        block.style.setProperty('display', 'block', 'important');
        block.style.setProperty('width', '100%', 'important');
    });

    const allSteps = document.querySelectorAll('.process-step');
    console.log(`[DEBUG] 强制应用紧凑样式到 ${allSteps.length} 个步骤`);

    allSteps.forEach((step, index) => {
        // 确保有compact类
        if (!step.classList.contains('compact')) {
            step.classList.add('compact');
            console.log(`[DEBUG] 为步骤 ${index} 添加compact类`);
        }

        // 强制应用内联样式
        step.style.setProperty('border', '1px solid #e8e8e8', 'important');
        step.style.setProperty('border-radius', '4px', 'important');
        step.style.setProperty('padding', '0.5rem', 'important');
        step.style.setProperty('margin-bottom', '0.5rem', 'important');
        step.style.setProperty('background', 'white', 'important');

        // 强制布局样式 - 确保垂直排列
        step.style.setProperty('display', 'block', 'important');
        step.style.setProperty('width', '100%', 'important');
        step.style.setProperty('box-sizing', 'border-box', 'important');
        step.style.setProperty('float', 'none', 'important');
        step.style.setProperty('clear', 'both', 'important');
        step.style.setProperty('flex', 'none', 'important');

        // 修复内部元素
        const innerDivs = step.querySelectorAll('div');
        innerDivs.forEach((div, divIndex) => {
            if (divIndex < innerDivs.length - 1) {
                div.style.setProperty('margin-bottom', '0.4rem', 'important');
            } else {
                div.style.setProperty('margin-bottom', '0', 'important');
            }
        });

        // 修复标题
        const h6Elements = step.querySelectorAll('h6');
        h6Elements.forEach(h6 => {
            h6.style.setProperty('font-size', '0.85rem', 'important');
            h6.style.setProperty('margin', '0', 'important');
            h6.style.setProperty('color', '#1a73e8', 'important');
            h6.style.setProperty('font-weight', '500', 'important');
        });

        // 修复标签
        const labels = step.querySelectorAll('label');
        labels.forEach(label => {
            label.style.setProperty('font-size', '0.8rem', 'important');
            label.style.setProperty('font-weight', '500', 'important');
            label.style.setProperty('color', '#555', 'important');
            label.style.setProperty('min-width', '90px', 'important');
        });

        // 修复文本框
        const textareas = step.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            textarea.style.setProperty('height', '35px', 'important');
            textarea.style.setProperty('font-size', '0.8rem', 'important');
            textarea.style.setProperty('padding', '0.3rem', 'important');
            textarea.style.setProperty('border', '1px solid #ddd', 'important');
            textarea.style.setProperty('border-radius', '3px', 'important');
            textarea.style.setProperty('resize', 'none', 'important');
            textarea.style.setProperty('line-height', '1.3', 'important');
        });

        // 修复选择框
        const selects = step.querySelectorAll('select');
        selects.forEach(select => {
            select.style.setProperty('width', '80px', 'important');
            select.style.setProperty('padding', '0.2rem', 'important');
            select.style.setProperty('border', '1px solid #ddd', 'important');
            select.style.setProperty('border-radius', '3px', 'important');
            select.style.setProperty('font-size', '0.75rem', 'important');
            select.style.setProperty('height', '28px', 'important');
        });
    });
}

// 暴露函数到全局作用域
window.forceCompactStepStyles = forceCompactStepStyles;

// 在DOM变化时自动应用紧凑样式
if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
        let shouldApplyStyles = false;
        mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1 && (
                        node.classList?.contains('process-step') ||
                        node.querySelector?.('.process-step')
                    )) {
                        shouldApplyStyles = true;
                    }
                });
            }
        });

        if (shouldApplyStyles) {
            setTimeout(() => {
                forceCompactStepStyles();
            }, 100);
        }
    });

    // 观察整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('[DEBUG] 已启动步骤样式监听器');
}
