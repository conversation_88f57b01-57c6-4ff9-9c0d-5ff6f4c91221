# 设备页面识别框统一修改说明

## 修改目标
让设备页面的识别框除了标题内容等，其他底层格式和逻辑与工艺页面的保持一致。

## 主要修改内容

### 1. 设备工站HTML结构统一 (`static/js/station_generator.js`)

#### 1.1 传统格式设备工站 (`createTraditionalEquipmentStationHtml`)
- ✅ 添加工站前插入按钮（仅在第一个工站前显示）
- ✅ 添加折叠/展开按钮和图标
- ✅ 添加双击编辑标题功能
- ✅ 添加插入设备工站按钮
- ✅ 统一颜色主题（使用绿色 #52c41a 替代蓝色）
- ✅ 添加工站后插入按钮（仅在最后一个工站显示）
- ✅ 添加 `.station-content` 容器包装内容

#### 1.2 Equipment和Fixture分离格式 (`createEquipmentFixtureStationHtml`)
- ✅ 添加工站前插入按钮
- ✅ 添加折叠/展开功能
- ✅ 添加双击编辑标题功能
- ✅ 添加插入设备工站按钮
- ✅ 统一颜色主题
- ✅ 添加工站后插入按钮
- ✅ 添加 `.station-content` 容器

#### 1.3 简化Markdown格式 (`createSimpleMarkdownStationHtml`)
- ✅ 添加工站前插入按钮
- ✅ 添加折叠/展开功能
- ✅ 添加双击编辑标题功能
- ✅ 添加插入设备工站按钮
- ✅ 统一颜色主题
- ✅ 添加工站后插入按钮
- ✅ 添加 `.station-content` 容器

### 2. 设备工站功能函数 (`static/js/station_manager.js`)

#### 2.1 新增插入工站功能
- ✅ `insertEquipmentStationBefore(index)` - 在指定工站前插入
- ✅ `insertEquipmentStationAfter(index)` - 在指定工站后插入
- ✅ `insertEquipmentStation(index)` - 在指定位置插入
- ✅ 智能工站号计算（以5为间隔：ST10, ST15, ST20...）

#### 2.2 新增折叠展开功能
- ✅ `toggleEquipmentStationCollapse(index)` - 切换单个设备工站折叠状态
- ✅ 修改 `toggleAllStations(collapse)` - 支持同时处理工艺和设备工站

#### 2.3 新增标题编辑功能
- ✅ `makeEquipmentStationTitleEditable(index, element)` - 双击编辑设备工站标题
- ✅ 支持Enter保存、Escape取消
- ✅ 实时更新数据和显示

#### 2.4 全局函数暴露
- ✅ 将新增的设备工站函数添加到全局函数暴露检查列表中

### 3. 统一的交互体验

#### 3.1 颜色主题统一
- 工艺页面：蓝色主题 (#1a73e8)
- 设备页面：绿色主题 (#52c41a)
- 保持各自特色的同时统一交互逻辑

#### 3.2 功能对等
| 功能 | 工艺页面 | 设备页面 | 状态 |
|------|----------|----------|------|
| 折叠/展开工站 | ✅ | ✅ | 已统一 |
| 双击编辑标题 | ✅ | ✅ | 已统一 |
| 插入工站 | ✅ | ✅ | 已统一 |
| 删除工站 | ✅ | ✅ | 已统一 |
| 批量折叠/展开 | ✅ | ✅ | 已统一 |

#### 3.3 HTML结构统一
- 统一使用 `.station-content` 容器
- 统一的折叠图标和按钮布局
- 统一的插入按钮位置和样式
- 统一的标题编辑交互

## 技术实现细节

### 1. 工站插入逻辑
```javascript
// 智能计算新工站号
const prevNumber = insertIndex > 0 ? parseInt(equipmentStationsData[insertIndex - 1].station_number) : 0;
const nextNumber = insertIndex < equipmentStationsData.length ? parseInt(equipmentStationsData[insertIndex].station_number) : 999;
newStationNumber = Math.floor((prevNumber + nextNumber) / 2);

// 设备工站以5为间隔
if (newStationNumber <= prevNumber || newStationNumber >= nextNumber) {
    newStationNumber = prevNumber + 5;
}
```

### 2. 折叠状态管理
```javascript
// 统一的折叠逻辑
const isCollapsed = content.style.display === 'none';
if (isCollapsed) {
    content.style.display = 'block';
    icon.textContent = '▼';
} else {
    content.style.display = 'none';
    icon.textContent = '▶';
}
```

### 3. 标题编辑实现
```javascript
// 双击触发编辑模式
ondblclick="makeEquipmentStationTitleEditable(${index}, this)"

// 支持Enter保存、Escape取消
input.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') saveTitle();
    else if (e.key === 'Escape') cancelEdit();
});
```

## 测试验证

### 验证项目
1. ✅ 设备工站可以正常折叠/展开
2. ✅ 设备工站标题可以双击编辑
3. ✅ 可以在设备工站前后插入新工站
4. ✅ 批量折叠/展开同时作用于工艺和设备工站
5. ✅ 所有新功能函数正确暴露到全局作用域
6. ✅ 颜色主题保持一致性（绿色）
7. ✅ HTML结构与工艺页面保持一致

## 总结

通过本次修改，设备页面的识别框已经与工艺页面保持了一致的底层格式和逻辑：

1. **结构统一**：都使用相同的HTML结构和CSS类名
2. **功能对等**：折叠、编辑、插入等功能完全一致
3. **交互统一**：相同的操作方式和反馈机制
4. **主题区分**：保持各自的颜色主题特色

设备页面现在具备了与工艺页面相同的用户体验，同时保持了自己的视觉特色。
