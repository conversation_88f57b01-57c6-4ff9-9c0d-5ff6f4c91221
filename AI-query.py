import requests
import urllib3
from requests.exceptions import RequestException

# 调试打印
print("程序开始执行")

# 禁用SSL警告（仅测试环境使用）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def interactive_chat():
    """
    交互式问答函数
    """
    # 调试打印
    print("进入interactive_chat函数")
    
    # API配置
    api_url = 'https://ach-sso01.ap.autoliv.int:18443/api/v1/chat-messages'
    headers = {
        'Authorization': 'Bearer Ey0H37iNjuDk/t6pDNiKnjA+QKoCPNYXIskz6MZijYat6ow31IsDdVBgniQtcVQCJjxFDYVYlMGjlvhuj6ij9TWLHXJpRFC0xcia+xb/N/T8u5Nzp1stCwUXkUyZS7qqU71+1eqUzpMiwp7ZD6uWpya3qX973KlE/fAmrDU4WufXHroTP8HCWg==',
        'X-API-Key': 'b2c4eec9-be01-46f6-a501-f313a348d979'
    }

    print("""
    ======================
      AI 问答系统（输入 exit 退出）
    ======================
    """)

    while True:
        try:
            # 获取用户输入
            question = input("\n您的问题：").strip()
            
            if question.lower() in ['exit', 'quit']:
                print("\n再见！")
                break
                
            if not question:
                print("问题不能为空，请重新输入")
                continue

            # 发送请求
            print(f"正在发送请求：{question[:30]}...")
            response = requests.post(
                api_url,
                headers=headers,
                json={
                    'query': question,
                    'chatId': 9194,
                    'stream': False
                },
                verify=False,
                timeout=90
            )

            # 处理响应
            if response.status_code == 200:
                print("\n[调试] 完整响应内容：")
                print(response.json())  # 添加调试打印
                answer = response.json().get('answer', '')
                print("\nAI 回答：")
                print("-" * 50)
                # 将answer中的\n替换为实际的换行符
                formatted_answer = answer.replace('\\n', '\n')
                print(formatted_answer)
                print("-" * 50)
            else:
                print(f"\n[错误] API响应异常（状态码：{response.status_code}）")
                print(f"详细信息：{response.text[:200]}")

        except RequestException as e:
            print(f"\n[网络错误] 请求失败：{str(e)}")
        except KeyboardInterrupt:
            print("\n用户中断操作，退出程序")
            break
        except Exception as e:
            print(f"\n[系统错误] 发生未知异常：{str(e)}")
            import traceback
            print(traceback.format_exc())

print("准备调用interactive_chat函数")

if __name__ == "__main__":
    print("__main__块被执行")
    interactive_chat()
    print("程序执行完毕")
