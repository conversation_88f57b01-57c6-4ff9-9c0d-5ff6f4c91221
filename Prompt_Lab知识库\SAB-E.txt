# SAB-E类知识库

## 概述
SAB-E类产品的工艺和设备要求规范。

**产品族**: SAB
**工艺类型**: E
**标准节拍时间**: 40秒
**应用场景**: 适用于包含6个主要零件的SAB产品装配


## 零件清单
1. **Deflector** - 导流片，用于气流导向
2. **inflator** - 发生器，气袋充气装置
3. **cushion** - 气袋，主要缓冲组件
4. **Bracket** - 支架，结构支撑组件
5. **Nuts** - 螺母，紧固件
6. **housing** - 外壳，保护性外罩


## 标准工艺流程

### 工艺部分

#### ST10 Pre-assembly
（一）
1. 工艺过程描述: 将发生器放在夹具上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器
   - 过程防错要求: 发生器校验位比对

3. 工艺过程描述: 发生器称重检测
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器重量
   - 过程防错要求: nan

4. 工艺过程描述: 预装支架/发生器/气袋/卡环
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: 二级零件扫描

5. 工艺过程描述: 扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: FIFO

6. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: 气袋校验位比对

7. 工艺过程描述: 将预装好的组件放入夹具
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 锁定支架位置

8. 工艺过程描述: 发生器角度定位
   - 人or设备: 设备
   - 产品特性要求: 
   1.发生器,支架,气袋,卡环的相对位置正确
   2.正确的发生器方向
   3.正确的发生器角度（发生器角度上下活动量在图纸范围内）
   - 过程防错要求: nan

9. 工艺过程描述: 夹紧组件
   - 人or设备: 设备
   - 产品特性要求: 
   1.气袋&支架位置正确
   2.发生器正确插入气袋(上下Taco）
   3.发生器支架位置锁定
   4.发生器前后位置锁定
   5.卡环位置正确
   - 过程防错要求: nan

10. 工艺过程描述: 锁紧2个卡环
   - 人or设备: 设备
   - 产品特性要求: 
   1.打点深度2.2-2.4 mm
   2.buckle打点位置0-5°
   3.Buckle 位置/方向符合图纸要求
   4.卡环位置符合图纸要求
   - 过程防错要求: 
   1.Bandit 过程中卡环不可有干涉
   2.Bandit 过程中枪体接触产品
   3.符合MPS067相关要求

11. 工艺过程描述: 产品组件取至下一工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST20 Folding & cover closure
（一）
1. 工艺过程描述: 组件与罩盖预装
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 固定组件
   - 人or设备: 设备
   - 产品特性要求: 发生器支架安装正确（卡入卡扣卡槽）
   - 过程防错要求: nan

3. 工艺过程描述: 气袋+1折叠尺插入气袋
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 气袋+1折叠尺插入位置正确

4. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 先进先出

5. 工艺过程描述: 上气袋插入夹子,整理气袋
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 正确的气袋夹持位置

6. 工艺过程描述: 自动折叠
   - 人or设备: 设备
   - 产品特性要求: 
   1.正确的折叠方向
   2.正确的折叠尺
   3.起始折叠位置正确
   4.折叠符合PID 图纸
   5.折叠无松散
   - 过程防错要求: nan

7. 工艺过程描述: 罩盖闭合
   - 人or设备: 设备
   - 产品特性要求: 
   1.卡扣正确压入卡槽并完全露出
   2.无夹气袋
   - 过程防错要求: nan

8. 工艺过程描述: 自动扫描罩盖条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 校验位比对

9. 工艺过程描述: 释放组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST30 Package
（一）
1. 工艺过程描述: 将罩盖放入夹具，并固定
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 扫描罩盖条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 移动E-check 探头并锁住发生器
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 设备检测需求满足MPS128

4. 工艺过程描述: 按按钮设备自动检测（电阻&厚度）
   - 人or设备: 设备
   - 产品特性要求: 正确的模块厚度和电阻
   - 过程防错要求: nan

5. 工艺过程描述: 打印客户标签
   - 人or设备: 设备
   - 产品特性要求: 正确的客户标签内容，方向，位置
   - 过程防错要求: nan

6. 工艺过程描述: 黏贴客户标签
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 扫描客户标签条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 客户标签角度检测

8. 工艺过程描述: 释放组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

### 设备部分

#### ST10 Pre-assembly
一、SAB-发生器预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器扫描OK/NG，气袋扫描OK/NG，影像OK/NG 等指示灯
6.发生器夹持治具下方台面配置黑色背景板
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
10.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.导流片安装方向正确、导流片无错装、漏装
导流片通过上方IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.无扫A做B
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、发生器、气袋预装治具-Fixture 
（一）机械要求:
1. 1.发生器平放插入治具内，以发生器塑料端面为基准，治具内带有传感器检测放置到位。
2.治具夹持不能对发生器端面造成磕碰损伤
3.治具上配置发生器螺柱导向功能，原位伸出，发生器夹持后缩回
4.治具松开，发生器不会掉落
5.治具夹紧发生器后不晃动、松动，治具夹持后间隙需小于发生器直径，夹持长度至少要15mm
6.发生器夹持治具整体设计哈丁成快换形式，到位后气缸自锁。
7.治具于产品接触部分无锐边，毛刺，表面需倒角
8.治具无夹手风险
9.治具带有防护罩，防止螺丝掉落

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 Folding & cover closure
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照Hard Cover folding标机制作
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备整体框架有如下4个部分组成；
  3.1 左、右气袋夹紧机构；
  3.2 左、右气袋折叠机构；
  3.3 左、右加一折尺机构；工装底座及周围附件；
  3.4 快换工装、折尺、加一折尺、卡扣外导向、卡扣压尺、气袋整理、防气袋抽出；
4.电气箱分上、下部分，下电气箱旁放置气源处理件及阀岛；
5.脚踏开关安装到设备下方，外缘露出型材2公分；
6.左、右两侧X向伺服模组形成不小于500mm；
7.左、右两侧X向伺服行程不能一样，需保证单个夹头能移动到中间位置；
8.模组选型尽量选用封闭式模组，最大程度上减少A级螺母；
9.整个机构需优化设计要考虑减少螺母使用量；
10.折尺快换机构采用Autoliv标准快换结构；
11.左、右两侧X向伺服行程不小于260mm，Z向行程不小于450mm；
12.工装底座角度调节范围不小于±10°；
13.工装底座前后伺服调节范围不小于±50mm；
14.夹具合模伺服输出扭矩不小于150NM；
15.配置1个设备状态指示灯盒
16.设备上方增设相机，检测气袋折叠状态
16.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关
17.设备顶上带有蜂鸣器的红黄绿三色灯
18.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
19.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.正确的加一折尺位置：
通过设备相机影像，检测气袋位置及气袋与加一折尺位置
5.气袋不歪斜：
通过设备下方的相机影像来确保气袋夹持不歪斜
方案1：[具体方案]

二、折尺、罩盖压合治具-Fixture 
（一）机械要求:
1. 1.罩盖压装夹具要求方便拆卸，可以快速换型，换型时扫码防错；
2.折尺尺寸符合图纸要求，可快速换型，换型时扫码防错；
3.+1折叠尺位置正确，+1折叠尺可以快速换型，换型时扫码防错；
4.传感器检查罩盖安装到位，吸盘能有效吸附罩盖；
5.折叠完气袋固定机构能有效固定气袋，固定机构可以快速换型，换型时扫码防错；
6.螺柱夹持机构能有效夹持螺柱，不损坏螺柱，传感器能够检测螺柱到位；
7.每一个卡脚处需要一个卡脚导向机构，导向机构可以快速换型，换型时扫码防错；
8.气袋折叠后，须有辅助整理气袋机构，防止夹气袋，整理机构可以快速换型，换型时扫码防错；
9.传感器检查每个卡脚卡扣到位；
10.传感器安装位置需合理，能够检测罩盖安装到位，安装紧固不易松动；
11.吸盘的数量和位置需结合罩盖形状进行设计，要求能够有效吸附罩盖，起到固定壳体作用（需提供备件），吸盘具有负压检测功能；
12.发生器螺柱夹持机构需能有效夹持住螺柱，保证螺柱不松脱，不能损伤螺柱的螺牙（需提供备件）；
13.螺柱检测到位要求螺柱高度在≥13.5mm时检测到信号，检测机构高度位置调整好以后要求打定位销固定；
14.要求罩壳及卡扣有稳定、可靠的定位和夹紧，保证合模后卡扣准确、完好的卡入卡槽内；
15.罩盖翻转侧的夹具上需有定位柱定位罩盖，罩盖卡扣内侧和外侧都需要有定位柱，定位柱数量和形状按照卡扣数量和形状设计，不能损伤罩盖定位孔和撕裂线，定位柱不易松脱，掉落，光滑，不尖锐，不能损伤气袋，定位柱能够伸缩；
16.安装发生器侧的罩盖定位柱要求能够伸缩，定位柱须在罩盖内测，定位柱数量和形状按照罩盖开孔数量和形状设计，不能损伤罩盖定位孔和撕裂线，定位柱不易松脱，掉落，光滑，不尖锐，不能损伤气袋；（保留设计，现场调试确有需求再加上）
17.罩盖上、下模设计需考虑减重，在不影响定位和整体强度条件下开减重孔；
18.翻转侧的夹具要求设计成可以左右调节，旋转链接轴要求耐磨损；
19.加一折尺位置尺寸需满足折叠图纸要求；
20.+1折尺表面光滑，无毛刺，+1折尺方便快换，切换产品时通过扫描折尺上二维码进行防错；
21.折叠时须有视觉检测+1折叠尺位置正确；
22.罩盖上每一个卡脚处都需要有外导向块，导向块要求能够前后调节，气缸控制导向块伸出，缩回，导向块方便快换，换型时通过扫描导向块上二维码进行防错；
23.压板压住折叠完的气袋，保证气袋不松散；
24.压板表面光滑，无毛刺，不能损伤气袋；
25.压板要求能够前后调节，不易变形，断裂；
26.气袋整理机构需安装在罩盖小头处，将外漏的气袋推回罩盖内，防止罩盖扣合时气袋背带被夹；
27.整理机构上推板表面光滑，无毛刺，不能损伤气袋，能够前后调节，不易变形，断裂；
28.压板和整理机构方便快换，换型时通过扫描压板和整理机构上二维码进行防错；
29.传感器检测每个罩盖卡脚安装状态，能够识别卡脚安装异常，导杆头部形状需完全覆盖卡脚端面，保证检测可靠；
30.检测头上下伸缩要求防转，上下伸缩顺滑不卡顿；
31.要求每个卡扣到位单独检测；

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
2.折尺、加一折尺二维码防错
方案1：[具体方案]

#### ST30 Package
一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
4.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
5.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
6.设备顶上带有蜂鸣器的红黄绿三色灯
7.设备设计合理，尺寸紧凑，空间无浪费
8.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
方案1：[具体方案]

二、1.产品定位治具
2.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
2.治具托板为平板
3.治具设计合理，无安全风险
4.治具设计轻便，整体重量≤7.5Kg
5.传感器放大器安装位置便于维修调试
6.传感器检测发生器螺柱，无需夹爪夹持
7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
9.两侧安装把手，方便拿取
10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
3. 厚度GT检测机构：
1.厚度检测机构按照最新的设备标机设计
2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
3.GT头下压过程中顺畅，不卡顿
4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
5.厚度检测能覆盖20mm-50mm之间的产品
6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
7.测厚压板左右考虑共用，下压时避开产品发生器区域
8.压板需设计成快换，并带有防错
9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
10.压板机构自重掉落过程中顺畅、不卡顿
11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
4. 归零、标准点检块：
1.归零块：对产品厚度检测机构GT进行归零
2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

