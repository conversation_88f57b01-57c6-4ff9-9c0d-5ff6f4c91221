#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误修复验证脚本
验证"Cannot set properties of null (setting 'innerHTML')"错误的修复
"""

import os
import re
import sys

def test_error_handling():
    """测试错误处理机制"""
    print("🔧 测试错误处理机制...")
    
    try:
        # 读取station_manager.js文件
        with open('static/js/station_manager.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_manager.js文件")
        
        # 1. 检查ensureStationGenerator函数是否存在
        print("\n1. 检查ensureStationGenerator函数...")
        
        if 'function ensureStationGenerator()' in js_content:
            print("✅ ensureStationGenerator函数存在")
        else:
            print("❌ ensureStationGenerator函数不存在")
            return False
        
        # 2. 检查所有调用是否使用了错误处理
        print("\n2. 检查错误处理调用...")
        
        error_handled_calls = [
            'if (ensureStationGenerator())',
            'stationGenerator.generateProcessStations(processStationsData)'
        ]
        
        for call in error_handled_calls:
            if call in js_content:
                print(f"✅ 找到错误处理调用: {call}")
            else:
                print(f"❌ 未找到错误处理调用: {call}")
                return False
        
        # 3. 检查是否移除了直接调用
        print("\n3. 检查直接调用移除...")
        
        # 检查是否还有未保护的直接调用
        unsafe_patterns = [
            r'stationGenerator\.generateProcessStations\((?!.*ensureStationGenerator)',
            r'stationGenerator\.createProcessStationHtml\((?!.*ensureStationGenerator)'
        ]
        
        unsafe_found = False
        for pattern in unsafe_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                print(f"⚠️  发现可能不安全的调用: {matches}")
                # 这不一定是错误，因为可能在条件检查后调用
            else:
                print(f"✅ 未发现不安全的调用模式: {pattern}")
        
        # 4. 检查初始化逻辑
        print("\n4. 检查初始化逻辑...")
        
        if 'function initializeStationGenerator()' in js_content:
            print("✅ initializeStationGenerator函数存在")
        else:
            print("❌ initializeStationGenerator函数不存在")
            return False
        
        if 'setTimeout' in js_content and '延迟重试初始化StationGenerator' in js_content:
            print("✅ 延迟重试机制存在")
        else:
            print("❌ 延迟重试机制不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dom_element_checks():
    """测试DOM元素检查"""
    print("\n🔧 测试DOM元素检查...")
    
    try:
        # 读取station_generator.js文件
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_generator.js文件")
        
        # 检查DOM元素检查
        dom_checks = [
            "const container = document.getElementById('stations-list')",
            "if (!container) {",
            "console.error('未找到工站容器')",
            "return;"
        ]
        
        for check in dom_checks:
            if check in js_content:
                print(f"✅ DOM检查存在: {check}")
            else:
                print(f"❌ DOM检查不存在: {check}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_html_integration():
    """测试HTML集成"""
    print("\n🔧 测试HTML集成...")
    
    try:
        # 读取index.html文件
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print("✅ 成功读取index.html文件")
        
        # 检查必要的DOM元素是否存在
        required_elements = [
            'id="stations-list"',
            'id="equipment-stations-list"'
        ]
        
        for element in required_elements:
            if element in html_content:
                print(f"✅ 必要DOM元素存在: {element}")
            else:
                print(f"❌ 必要DOM元素不存在: {element}")
                return False
        
        # 检查JS文件引用
        js_files = [
            'station_manager.js',
            'station_generator.js'
        ]
        
        for js_file in js_files:
            if js_file in html_content:
                print(f"✅ JS文件已引用: {js_file}")
            else:
                print(f"❌ JS文件未引用: {js_file}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_debug_info():
    """生成调试信息"""
    print("\n📋 生成调试信息...")
    
    debug_info = """
# 错误修复调试指南

## 常见错误原因
1. **StationGenerator未初始化**: 页面加载时StationGenerator类未定义
2. **DOM元素不存在**: stations-list或equipment-stations-list元素不存在
3. **时序问题**: JavaScript执行顺序导致的初始化问题

## 调试步骤
1. 打开浏览器开发者工具 (F12)
2. 查看Console标签页的错误信息
3. 检查以下调试信息:
   - `[DEBUG] StationGenerator初始化成功`
   - `[DEBUG] 工站生成器不可用，尝试重新初始化`
   - `[ERROR] 未找到工站容器`

## 修复措施
1. **确保JS文件加载顺序正确**:
   - station_generator.js 应在 station_manager.js 之前加载
   
2. **确保DOM元素存在**:
   - 检查HTML中是否有 id="stations-list" 的元素
   - 检查HTML中是否有 id="equipment-stations-list" 的元素

3. **检查初始化时机**:
   - 确保在DOMContentLoaded事件后调用插入函数
   - 避免在页面完全加载前调用工站操作

## 错误处理机制
- 自动重试初始化
- 延迟重试机制
- 详细的错误日志
- 安全的函数调用检查
"""
    
    with open('debug_info.md', 'w', encoding='utf-8') as f:
        f.write(debug_info)
    
    print("✅ 调试信息已生成到 debug_info.md")

def main():
    """主测试函数"""
    print("🚀 开始验证错误修复...")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_error_handling,
        test_dom_element_checks,
        test_html_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    # 生成调试信息
    generate_debug_info()
    
    # 输出测试结果
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 错误修复验证通过！")
        print("\n✅ 修复内容:")
        print("1. 添加了ensureStationGenerator()函数确保生成器可用")
        print("2. 添加了initializeStationGenerator()函数处理初始化")
        print("3. 添加了延迟重试机制处理时序问题")
        print("4. 所有工站操作都使用了安全的错误处理")
        print("5. 保留了原有的DOM元素检查机制")
        
        print("\n🔧 如果仍然出现错误，请:")
        print("1. 检查浏览器控制台的详细错误信息")
        print("2. 确认JS文件加载顺序正确")
        print("3. 确认DOM元素存在")
        print("4. 查看debug_info.md获取详细调试指南")
        
        return True
    else:
        print("❌ 部分验证失败，请检查修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
