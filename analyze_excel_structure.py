#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Excel文件结构脚本
用于了解Linespec-data-SAB-V0.xlsx的数据结构
"""

import pandas as pd
import os

def analyze_excel_structure(excel_file_path):
    """分析Excel文件结构"""
    
    if not os.path.exists(excel_file_path):
        print(f"❌ 错误: Excel文件 '{excel_file_path}' 不存在")
        return False
    
    try:
        # 读取Excel文件
        excel_file = pd.ExcelFile(excel_file_path)
        
        print("=" * 80)
        print(f"📊 Excel文件分析: {excel_file_path}")
        print("=" * 80)
        
        print(f"\n📋 工作表总数: {len(excel_file.sheet_names)}")
        print(f"📋 工作表列表: {excel_file.sheet_names}")
        
        # 分析每个工作表
        for i, sheet_name in enumerate(excel_file.sheet_names, 1):
            print(f"\n{'-' * 60}")
            print(f"📄 工作表 {i}: {sheet_name}")
            print(f"{'-' * 60}")
            
            try:
                # 读取工作表数据
                df = pd.read_excel(excel_file, sheet_name=sheet_name)
                
                print(f"📏 数据维度: {df.shape[0]} 行 x {df.shape[1]} 列")
                
                if not df.empty:
                    print(f"📝 列名:")
                    for j, col in enumerate(df.columns, 1):
                        print(f"   {j}. {col}")
                    
                    print(f"\n📋 前5行数据预览:")
                    print(df.head().to_string())
                    
                    # 检查是否包含SAB类别信息
                    sheet_content = sheet_name.upper() + " " + " ".join(df.columns.astype(str)).upper()
                    sab_categories = []
                    for cat in ['A', 'B', 'C', 'D', 'E', 'F']:
                        if f'SAB-{cat}' in sheet_content or f'SAB{cat}' in sheet_content or f'{cat}类' in sheet_content:
                            sab_categories.append(cat)
                    
                    if sab_categories:
                        print(f"🏷️  检测到SAB类别: {', '.join(sab_categories)}")
                    
                    # 检查数据类型
                    data_types = []
                    if any(keyword in sheet_name.lower() for keyword in ['part', '零件', 'component']):
                        data_types.append("零件数据")
                    if any(keyword in sheet_name.lower() for keyword in ['process', '工艺', 'procedure']):
                        data_types.append("工艺数据")
                    if any(keyword in sheet_name.lower() for keyword in ['equipment', '设备', 'machine']):
                        data_types.append("设备数据")
                    if any(keyword in sheet_name.lower() for keyword in ['fixture', '夹具', 'jig']):
                        data_types.append("夹具数据")
                    if any(keyword in sheet_name.lower() for keyword in ['list', '清单', 'mapping']):
                        data_types.append("映射清单")
                    
                    if data_types:
                        print(f"📊 数据类型: {', '.join(data_types)}")
                    
                else:
                    print("⚠️  工作表为空")
                    
            except Exception as e:
                print(f"❌ 读取工作表 '{sheet_name}' 时出错: {e}")
        
        print(f"\n{'=' * 80}")
        print("📋 分析总结")
        print(f"{'=' * 80}")
        
        # 生成分析总结
        all_sheets = excel_file.sheet_names
        sab_related_sheets = [sheet for sheet in all_sheets if any(cat in sheet.upper() for cat in ['A', 'B', 'C', 'D', 'E', 'F'])]
        
        print(f"📊 总工作表数: {len(all_sheets)}")
        print(f"🏷️  SAB相关工作表: {len(sab_related_sheets)}")
        
        if sab_related_sheets:
            print(f"📋 SAB相关工作表列表:")
            for sheet in sab_related_sheets:
                print(f"   - {sheet}")
        
        # 建议数据处理策略
        print(f"\n💡 数据处理建议:")
        print(f"1. 重点关注包含SAB类别标识的工作表")
        print(f"2. 根据工作表名称和内容识别数据类型（零件/工艺/设备/夹具）")
        print(f"3. 提取每个SAB类别的完整数据集")
        print(f"4. 建立零件到SAB类别的映射关系")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析Excel文件时出错: {e}")
        return False

def main():
    """主函数"""
    excel_file = "Linespec-data-SAB-V0.xlsx"
    
    print("🔍 开始分析Excel文件结构...")
    
    if analyze_excel_structure(excel_file):
        print("\n✅ 分析完成！")
        print("\n📝 下一步操作建议:")
        print("1. 根据分析结果调整转换脚本")
        print("2. 运行 excel_to_markdown_converter.py 进行数据转换")
        print("3. 检查生成的Markdown文件")
        print("4. 上传到知识库系统")
    else:
        print("\n❌ 分析失败，请检查文件路径和格式")

if __name__ == "__main__":
    main()
