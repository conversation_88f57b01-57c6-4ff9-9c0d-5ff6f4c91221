/**
 * 产品附件分类多文件上传管理
 */

// 存储上传的附件数据（按分类组织，每个分类支持多个附件）
let categoryAttachments = {
    'explosion-diagram': [],
    'folding-diagram': [],
    'assembly-diagram': []
};

// 附件计数器，用于生成唯一ID
let attachmentCounter = 0;

// 附件类型配置
const ATTACHMENT_CATEGORIES = {
    'explosion-diagram': {
        name: '产品爆炸图',
        icon: '🔧',
        description: '展示产品各部件分解状态的技术图纸'
    },
    'folding-diagram': {
        name: '产品折叠图',
        icon: '📐',
        description: '展示产品折叠或收纳状态的技术图纸'
    },
    'assembly-diagram': {
        name: '产品总成图',
        icon: '🏗️',
        description: '展示产品完整装配状态的技术图纸'
    }
};

/**
 * 处理分类文件上传
 * @param {string} category - 附件分类
 * @param {Event} event - 文件上传事件
 */
function handleCategoryFileUpload(category, event) {
    const files = Array.from(event.target.files);
    if (!files.length) return;

    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf',
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    const validFiles = [];
    const invalidFiles = [];

    files.forEach(file => {
        if (!allowedTypes.includes(file.type)) {
            invalidFiles.push(`${file.name}: 不支持的文件格式`);
        } else if (file.size > maxSize) {
            invalidFiles.push(`${file.name}: 文件大小超过10MB`);
        } else {
            validFiles.push(file);
        }
    });

    if (invalidFiles.length > 0) {
        showToast(`以下文件无法上传：\n${invalidFiles.join('\n')}`);
    }

    if (validFiles.length === 0) {
        event.target.value = '';
        return;
    }

    // 显示加载状态
    showLoadingOverlay(`正在上传 ${validFiles.length} 个${ATTACHMENT_CATEGORIES[category].name}...`);

    let processedCount = 0;
    const totalFiles = validFiles.length;

    validFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const attachmentData = {
                id: `${category}_${++attachmentCounter}`,
                name: file.name,
                type: file.type,
                size: file.size,
                data: e.target.result,
                uploadTime: new Date().toISOString(),
                notes: ''
            };

            // 添加到对应分类的附件数组
            categoryAttachments[category].push(attachmentData);

            processedCount++;

            // 所有文件处理完成后更新界面
            if (processedCount === totalFiles) {
                updateCategoryAttachmentsList(category);
                hideLoadingOverlay();
                showToast(`成功上传 ${totalFiles} 个${ATTACHMENT_CATEGORIES[category].name}！`);

                // 清空文件输入
                event.target.value = '';

                console.log(`${category} 上传完成，当前数量: ${categoryAttachments[category].length}`);
            }
        };

        reader.onerror = function() {
            processedCount++;
            if (processedCount === totalFiles) {
                updateCategoryAttachmentsList(category);
                hideLoadingOverlay();
                showToast('部分文件上传失败，请重试');
                event.target.value = '';
            }
        };

        reader.readAsDataURL(file);
    });
}

/**
 * 更新分类附件列表显示
 * @param {string} category - 附件分类
 */
function updateCategoryAttachmentsList(category) {
    const contentContainer = document.getElementById(`${category}-content`);
    if (!contentContainer) return;

    const attachments = categoryAttachments[category];

    if (attachments.length === 0) {
        // 显示空状态
        contentContainer.innerHTML = `
            <div class="upload-placeholder">
                <div class="placeholder-icon">📋</div>
                <p>点击上传${ATTACHMENT_CATEGORIES[category].name}</p>
                <small>支持 JPG, PNG, PDF, DOC, DOCX 格式</small>
            </div>
        `;

        // 隐藏清空按钮
        const clearBtn = contentContainer.closest('.attachment-category').querySelector('.clear-btn');
        if (clearBtn) {
            clearBtn.style.display = 'none';
        }
        return;
    }

    // 显示清空按钮
    const clearBtn = contentContainer.closest('.attachment-category').querySelector('.clear-btn');
    if (clearBtn) {
        clearBtn.style.display = 'inline-block';
    }

    // 生成附件列表HTML
    let attachmentsHtml = '';
    attachments.forEach((attachment, index) => {
        attachmentsHtml += createCategoryAttachmentItemHtml(attachment, category, index);
    });

    contentContainer.innerHTML = attachmentsHtml;

    console.log(`${category} 附件列表已更新，共 ${attachments.length} 个附件`);
}

/**
 * 创建分类附件项的HTML
 * @param {Object} attachment - 附件数据
 * @param {string} category - 附件分类
 * @param {number} index - 附件在分类中的索引
 * @returns {string} HTML字符串
 */
function createCategoryAttachmentItemHtml(attachment, category, index) {
    const fileIcon = getCategoryAttachmentIcon(attachment.type);
    const fileSize = formatFileSize(attachment.size);
    const uploadDate = new Date(attachment.uploadTime).toLocaleString();

    return `
        <div class="category-attachment-item" data-attachment-id="${attachment.id}" data-category="${category}" data-index="${index}">
            <div class="category-attachment-header">
                <div class="category-attachment-info">
                    <div class="category-attachment-icon">${fileIcon}</div>
                    <div class="category-attachment-details">
                        <h4 class="category-attachment-name" ondblclick="editCategoryAttachmentName('${category}', ${index}, this)" title="双击编辑文件名">${attachment.name}</h4>
                        <p class="category-attachment-meta">${fileSize} • ${uploadDate}</p>
                    </div>
                </div>
                <div class="category-attachment-controls">
                    <button class="category-attachment-btn view" onclick="toggleCategoryAttachmentPreview('${category}', ${index})" title="查看预览">
                        👁️
                    </button>
                    <button class="category-attachment-btn edit" onclick="toggleCategoryAttachmentEdit('${category}', ${index})" title="编辑备注">
                        ✏️
                    </button>
                    <button class="category-attachment-btn download" onclick="downloadCategoryAttachment('${category}', ${index})" title="下载文件">
                        ⬇️
                    </button>
                    <button class="category-attachment-btn delete" onclick="deleteCategoryAttachment('${category}', ${index})" title="删除附件">
                        🗑️
                    </button>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="category-attachment-preview" id="preview-${attachment.id}">
                ${createCategoryAttachmentPreviewHtml(attachment)}
            </div>

            <!-- 备注编辑区域 -->
            <div class="category-attachment-notes" id="notes-${attachment.id}" style="display: none;">
                <label>备注说明：</label>
                <textarea placeholder="请输入附件的相关说明..." onchange="updateCategoryAttachmentNotes('${category}', ${index}, this.value)">${attachment.notes || ''}</textarea>
            </div>
        </div>
    `;
}

/**
 * 获取分类附件文件类型对应的图标
 * @param {string} fileType - 文件MIME类型
 * @returns {string} 图标字符
 */
function getCategoryAttachmentIcon(fileType) {
    if (fileType.startsWith('image/')) {
        return '🖼️';
    } else if (fileType === 'application/pdf') {
        return '📄';
    } else if (fileType.includes('word') || fileType.includes('document')) {
        return '📝';
    } else {
        return '📎';
    }
}

/**
 * 创建分类附件预览HTML
 * @param {Object} attachment - 附件数据
 * @returns {string} 预览HTML
 */
function createCategoryAttachmentPreviewHtml(attachment) {
    if (attachment.type.startsWith('image/')) {
        return `<img src="${attachment.data}" alt="${attachment.name}" />`;
    } else if (attachment.type === 'application/pdf') {
        return `<div class="pdf-preview">📄 PDF文档<br><small>点击下载查看完整内容</small></div>`;
    } else {
        return `<div class="pdf-preview">📎 ${attachment.name}<br><small>点击下载查看文件内容</small></div>`;
    }
}

/**
 * 切换分类附件预览显示
 * @param {string} category - 附件分类
 * @param {number} index - 附件索引
 */
function toggleCategoryAttachmentPreview(category, index) {
    const attachment = categoryAttachments[category][index];
    if (!attachment) return;

    const previewElement = document.getElementById(`preview-${attachment.id}`);
    if (!previewElement) return;

    const isActive = previewElement.classList.contains('active');

    // 关闭所有其他预览
    document.querySelectorAll('.category-attachment-preview.active').forEach(el => {
        el.classList.remove('active');
    });

    if (!isActive) {
        previewElement.classList.add('active');
    }
}

/**
 * 切换分类附件编辑模式
 * @param {string} category - 附件分类
 * @param {number} index - 附件索引
 */
function toggleCategoryAttachmentEdit(category, index) {
    const attachment = categoryAttachments[category][index];
    if (!attachment) return;

    const notesElement = document.getElementById(`notes-${attachment.id}`);
    const itemElement = document.querySelector(`[data-attachment-id="${attachment.id}"]`);

    if (!notesElement || !itemElement) return;

    const isEditing = notesElement.style.display !== 'none';

    if (isEditing) {
        // 退出编辑模式
        notesElement.style.display = 'none';
        itemElement.classList.remove('category-attachment-edit-mode');
    } else {
        // 进入编辑模式
        notesElement.style.display = 'block';
        itemElement.classList.add('category-attachment-edit-mode');

        // 关闭其他编辑模式
        document.querySelectorAll('.category-attachment-edit-mode').forEach(el => {
            if (el !== itemElement) {
                el.classList.remove('category-attachment-edit-mode');
                const otherNotesElement = el.querySelector('.category-attachment-notes');
                if (otherNotesElement) {
                    otherNotesElement.style.display = 'none';
                }
            }
        });
    }
}

/**
 * 下载分类附件
 * @param {string} category - 附件分类
 * @param {number} index - 附件索引
 */
function downloadCategoryAttachment(category, index) {
    const attachment = categoryAttachments[category][index];
    if (!attachment) return;

    const link = document.createElement('a');
    link.href = attachment.data;
    link.download = attachment.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast(`正在下载 ${attachment.name}`);
}

/**
 * 删除分类附件
 * @param {string} category - 附件分类
 * @param {number} index - 附件索引
 */
function deleteCategoryAttachment(category, index) {
    const attachment = categoryAttachments[category][index];
    if (!attachment) return;

    if (confirm(`确定要删除附件 "${attachment.name}" 吗？`)) {
        // 从数组中删除
        categoryAttachments[category].splice(index, 1);

        // 更新列表显示
        updateCategoryAttachmentsList(category);

        showToast(`附件 "${attachment.name}" 已删除`);
        console.log(`删除 ${category} 附件: ${attachment.name}，剩余 ${categoryAttachments[category].length} 个附件`);
    }
}

/**
 * 编辑分类附件名称
 * @param {string} category - 附件分类
 * @param {number} index - 附件索引
 * @param {Element} element - 名称元素
 */
function editCategoryAttachmentName(category, index, element) {
    const attachment = categoryAttachments[category][index];
    if (!attachment) return;

    const currentName = attachment.name;
    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentName;
    input.style.cssText = element.style.cssText;
    input.style.border = '1px solid #40a9ff';
    input.style.background = '#fff';
    input.style.width = '100%';
    input.style.fontSize = '0.9rem';
    input.style.fontWeight = '600';

    // 替换元素
    element.parentNode.replaceChild(input, element);
    input.focus();
    input.select();

    // 保存函数
    const saveEdit = () => {
        const newName = input.value.trim() || currentName;
        attachment.name = newName;

        const newElement = document.createElement('h4');
        newElement.className = 'category-attachment-name';
        newElement.textContent = newName;
        newElement.setAttribute('ondblclick', `editCategoryAttachmentName('${category}', ${index}, this)`);
        newElement.setAttribute('title', '双击编辑文件名');

        input.parentNode.replaceChild(newElement, input);

        showToast(`文件名已更新为: ${newName}`);
        console.log(`${category} 附件 ${index} 名称已更新: ${newName}`);
    };

    // 事件监听
    input.addEventListener('blur', saveEdit);
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            saveEdit();
        } else if (e.key === 'Escape') {
            // 取消编辑
            input.parentNode.replaceChild(element, input);
        }
    });
}

/**
 * 更新分类附件备注
 * @param {string} category - 附件分类
 * @param {number} index - 附件索引
 * @param {string} notes - 备注内容
 */
function updateCategoryAttachmentNotes(category, index, notes) {
    const attachment = categoryAttachments[category][index];
    if (!attachment) return;

    attachment.notes = notes;
    console.log(`${category} 附件 ${index} 备注已更新:`, notes);
}

/**
 * 清空分类附件
 * @param {string} category - 附件分类
 */
function clearCategoryAttachments(category) {
    const attachments = categoryAttachments[category];
    if (!attachments || attachments.length === 0) {
        showToast(`${ATTACHMENT_CATEGORIES[category].name}没有附件需要清空`);
        return;
    }

    if (confirm(`确定要清空所有 ${attachments.length} 个${ATTACHMENT_CATEGORIES[category].name}吗？`)) {
        categoryAttachments[category] = [];
        updateCategoryAttachmentsList(category);
        showToast(`${ATTACHMENT_CATEGORIES[category].name}已清空`);
        console.log(`${category} 附件已清空`);
    }
}

/**
 * 清空所有分类附件
 */
function clearAllCategoryAttachments() {
    const totalCount = Object.values(categoryAttachments).reduce((sum, arr) => sum + arr.length, 0);

    if (totalCount === 0) {
        showToast('没有附件需要清空');
        return;
    }

    if (confirm(`确定要清空所有 ${totalCount} 个产品附件吗？`)) {
        Object.keys(categoryAttachments).forEach(category => {
            categoryAttachments[category] = [];
            updateCategoryAttachmentsList(category);
        });
        attachmentCounter = 0;
        showToast('所有产品附件已清空');
        console.log('所有分类附件已清空');
    }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取所有分类附件数据
 * @returns {Object} 分类附件数据对象
 */
function getAllCategoryAttachments() {
    return Object.keys(categoryAttachments).reduce((result, category) => {
        result[category] = categoryAttachments[category].map(attachment => ({ ...attachment }));
        return result;
    }, {});
}

/**
 * 设置分类附件数据（用于加载保存的数据）
 * @param {Object} attachments - 分类附件数据对象
 */
function setAllCategoryAttachments(attachments) {
    if (!attachments || typeof attachments !== 'object') {
        console.error('附件数据格式错误，应为对象');
        return;
    }

    // 重置数据
    Object.keys(categoryAttachments).forEach(category => {
        categoryAttachments[category] = [];
    });

    // 加载数据
    Object.keys(attachments).forEach(category => {
        if (categoryAttachments.hasOwnProperty(category) && Array.isArray(attachments[category])) {
            categoryAttachments[category] = attachments[category].map((attachment, index) => ({
                ...attachment,
                id: attachment.id || `${category}_${++attachmentCounter}`
            }));
        }
    });

    // 更新计数器
    let maxId = 0;
    Object.values(categoryAttachments).forEach(attachments => {
        attachments.forEach(attachment => {
            const match = attachment.id.match(/_(\d+)$/);
            if (match) {
                maxId = Math.max(maxId, parseInt(match[1]));
            }
        });
    });
    attachmentCounter = Math.max(attachmentCounter, maxId);

    // 更新所有分类的显示
    Object.keys(categoryAttachments).forEach(category => {
        updateCategoryAttachmentsList(category);
    });

    const totalCount = Object.values(categoryAttachments).reduce((sum, arr) => sum + arr.length, 0);
    showToast(`已加载 ${totalCount} 个附件`);
}

/**
 * 导出所有分类附件数据
 * @returns {string} JSON格式的附件数据
 */
function exportAllCategoryAttachments() {
    const attachments = getAllCategoryAttachments();
    const jsonData = JSON.stringify(attachments, null, 2);

    // 创建下载链接
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `product_category_attachments_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    const totalCount = Object.values(categoryAttachments).reduce((sum, arr) => sum + arr.length, 0);
    showToast(`附件数据已导出 (${totalCount} 个附件)`);
    return jsonData;
}

/**
 * 导入分类附件数据
 * @param {string} jsonData - JSON格式的附件数据
 */
function importCategoryAttachmentsData(jsonData) {
    try {
        const attachments = JSON.parse(jsonData);
        setAllCategoryAttachments(attachments);
        showToast('附件数据加载成功');
    } catch (error) {
        console.error('附件数据导入失败:', error);
        showToast('附件数据格式错误');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('产品附件分类多文件管理模块已加载');

    // 初始化所有分类的附件列表显示
    Object.keys(categoryAttachments).forEach(category => {
        updateCategoryAttachmentsList(category);
    });

    // 添加拖拽上传功能到各个分类
    Object.keys(ATTACHMENT_CATEGORIES).forEach(category => {
        const categoryElement = document.getElementById(`${category}-content`).closest('.attachment-category');
        if (categoryElement) {
            categoryElement.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                categoryElement.style.backgroundColor = '#f0f9ff';
                categoryElement.style.borderColor = '#40a9ff';
            });

            categoryElement.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                categoryElement.style.backgroundColor = '';
                categoryElement.style.borderColor = '';
            });

            categoryElement.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                categoryElement.style.backgroundColor = '';
                categoryElement.style.borderColor = '';

                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    // 模拟文件输入事件
                    const fileInput = document.getElementById(`${category}-input`);
                    if (fileInput) {
                        // 创建一个新的文件列表
                        const dt = new DataTransfer();
                        files.forEach(file => dt.items.add(file));
                        fileInput.files = dt.files;

                        // 触发上传处理
                        handleCategoryFileUpload(category, { target: fileInput });
                    }
                }
            });
        }
    });
});

// 暴露全局函数
window.handleCategoryFileUpload = handleCategoryFileUpload;
window.toggleCategoryAttachmentPreview = toggleCategoryAttachmentPreview;
window.toggleCategoryAttachmentEdit = toggleCategoryAttachmentEdit;
window.downloadCategoryAttachment = downloadCategoryAttachment;
window.deleteCategoryAttachment = deleteCategoryAttachment;
window.editCategoryAttachmentName = editCategoryAttachmentName;
window.updateCategoryAttachmentNotes = updateCategoryAttachmentNotes;
window.clearCategoryAttachments = clearCategoryAttachments;
window.clearAllCategoryAttachments = clearAllCategoryAttachments;
window.exportAllCategoryAttachments = exportAllCategoryAttachments;
window.importCategoryAttachmentsData = importCategoryAttachmentsData;
window.getAllCategoryAttachments = getAllCategoryAttachments;
window.setAllCategoryAttachments = setAllCategoryAttachments;

console.log('[DEBUG] 产品附件分类多文件管理功能已初始化');
