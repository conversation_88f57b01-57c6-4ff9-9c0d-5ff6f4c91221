/**
 * 产品附件多文件上传管理
 */

// 存储上传的附件数据（支持多个附件）
let productAttachments = [];

// 附件计数器，用于生成唯一ID
let attachmentCounter = 0;

/**
 * 处理多文件上传
 * @param {Event} event - 文件上传事件
 */
function handleMultipleFileUpload(event) {
    const files = Array.from(event.target.files);
    if (!files.length) return;

    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf',
                         'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    const validFiles = [];
    const invalidFiles = [];

    files.forEach(file => {
        if (!allowedTypes.includes(file.type)) {
            invalidFiles.push(`${file.name}: 不支持的文件格式`);
        } else if (file.size > maxSize) {
            invalidFiles.push(`${file.name}: 文件大小超过10MB`);
        } else {
            validFiles.push(file);
        }
    });

    if (invalidFiles.length > 0) {
        showToast(`以下文件无法上传：\n${invalidFiles.join('\n')}`);
    }

    if (validFiles.length === 0) {
        event.target.value = '';
        return;
    }

    // 显示加载状态
    showLoadingOverlay(`正在上传 ${validFiles.length} 个文件...`);

    let processedCount = 0;
    const totalFiles = validFiles.length;

    validFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const attachmentData = {
                id: `attachment_${++attachmentCounter}`,
                name: file.name,
                type: file.type,
                size: file.size,
                data: e.target.result,
                uploadTime: new Date().toISOString(),
                notes: ''
            };

            // 添加到附件数组
            productAttachments.push(attachmentData);

            processedCount++;

            // 所有文件处理完成后更新界面
            if (processedCount === totalFiles) {
                updateProductAttachmentsList();
                hideLoadingOverlay();
                showToast(`成功上传 ${totalFiles} 个附件！`);

                // 清空文件输入
                event.target.value = '';

                console.log(`上传完成，当前附件数量: ${productAttachments.length}`);
            }
        };

        reader.onerror = function() {
            processedCount++;
            if (processedCount === totalFiles) {
                updateProductAttachmentsList();
                hideLoadingOverlay();
                showToast('部分文件上传失败，请重试');
                event.target.value = '';
            }
        };

        reader.readAsDataURL(file);
    });
}

/**
 * 更新产品附件列表显示
 */
function updateProductAttachmentsList() {
    const listContainer = document.getElementById('product-attachments-list');
    const emptyState = document.getElementById('attachments-empty-state');

    if (!listContainer) return;

    if (productAttachments.length === 0) {
        // 显示空状态
        if (emptyState) {
            emptyState.style.display = 'block';
        }
        // 清空列表内容（保留空状态）
        const existingItems = listContainer.querySelectorAll('.product-attachment-item');
        existingItems.forEach(item => item.remove());
        return;
    }

    // 隐藏空状态
    if (emptyState) {
        emptyState.style.display = 'none';
    }

    // 清空现有内容
    const existingItems = listContainer.querySelectorAll('.product-attachment-item');
    existingItems.forEach(item => item.remove());

    // 生成附件列表
    productAttachments.forEach((attachment, index) => {
        const attachmentHtml = createProductAttachmentItemHtml(attachment, index);
        listContainer.insertAdjacentHTML('beforeend', attachmentHtml);
    });

    console.log(`附件列表已更新，共 ${productAttachments.length} 个附件`);
}

/**
 * 创建单个附件项的HTML
 * @param {Object} attachment - 附件数据
 * @param {number} index - 附件索引
 * @returns {string} HTML字符串
 */
function createProductAttachmentItemHtml(attachment, index) {
    const fileIcon = getProductAttachmentIcon(attachment.type);
    const fileSize = formatFileSize(attachment.size);
    const uploadDate = new Date(attachment.uploadTime).toLocaleString();

    return `
        <div class="product-attachment-item" data-attachment-id="${attachment.id}" data-index="${index}">
            <div class="product-attachment-header">
                <div class="product-attachment-info">
                    <div class="product-attachment-icon">${fileIcon}</div>
                    <div class="product-attachment-details">
                        <h4 class="product-attachment-name" ondblclick="editProductAttachmentName(${index}, this)" title="双击编辑文件名">${attachment.name}</h4>
                        <p class="product-attachment-meta">${fileSize} • ${uploadDate}</p>
                    </div>
                </div>
                <div class="product-attachment-controls">
                    <button class="product-attachment-btn view" onclick="toggleProductAttachmentPreview(${index})" title="查看预览">
                        👁️ 查看
                    </button>
                    <button class="product-attachment-btn edit" onclick="toggleProductAttachmentEdit(${index})" title="编辑备注">
                        ✏️ 编辑
                    </button>
                    <button class="product-attachment-btn download" onclick="downloadProductAttachment(${index})" title="下载文件">
                        ⬇️ 下载
                    </button>
                    <button class="product-attachment-btn delete" onclick="deleteProductAttachment(${index})" title="删除附件">
                        🗑️ 删除
                    </button>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="product-attachment-preview" id="preview-${attachment.id}">
                ${createProductAttachmentPreviewHtml(attachment)}
            </div>

            <!-- 备注编辑区域 -->
            <div class="product-attachment-notes" id="notes-${attachment.id}" style="display: none;">
                <label>备注说明：</label>
                <textarea placeholder="请输入附件的相关说明..." onchange="updateProductAttachmentNotes(${index}, this.value)">${attachment.notes || ''}</textarea>
            </div>
        </div>
    `;
}

/**
 * 获取文件类型对应的图标
 * @param {string} fileType - 文件MIME类型
 * @returns {string} 图标字符
 */
function getProductAttachmentIcon(fileType) {
    if (fileType.startsWith('image/')) {
        return '🖼️';
    } else if (fileType === 'application/pdf') {
        return '📄';
    } else if (fileType.includes('word') || fileType.includes('document')) {
        return '📝';
    } else {
        return '📎';
    }
}

/**
 * 创建附件预览HTML
 * @param {Object} attachment - 附件数据
 * @returns {string} 预览HTML
 */
function createProductAttachmentPreviewHtml(attachment) {
    if (attachment.type.startsWith('image/')) {
        return `<img src="${attachment.data}" alt="${attachment.name}" />`;
    } else if (attachment.type === 'application/pdf') {
        return `<div class="pdf-preview">📄 PDF文档<br><small>点击下载查看完整内容</small></div>`;
    } else {
        return `<div class="pdf-preview">📎 ${attachment.name}<br><small>点击下载查看文件内容</small></div>`;
    }
}

/**
 * 切换附件预览显示
 * @param {number} index - 附件索引
 */
function toggleProductAttachmentPreview(index) {
    const attachment = productAttachments[index];
    if (!attachment) return;

    const previewElement = document.getElementById(`preview-${attachment.id}`);
    if (!previewElement) return;

    const isActive = previewElement.classList.contains('active');

    // 关闭所有其他预览
    document.querySelectorAll('.product-attachment-preview.active').forEach(el => {
        el.classList.remove('active');
    });

    if (!isActive) {
        previewElement.classList.add('active');
    }
}

/**
 * 切换附件编辑模式
 * @param {number} index - 附件索引
 */
function toggleProductAttachmentEdit(index) {
    const attachment = productAttachments[index];
    if (!attachment) return;

    const notesElement = document.getElementById(`notes-${attachment.id}`);
    const itemElement = document.querySelector(`[data-attachment-id="${attachment.id}"]`);

    if (!notesElement || !itemElement) return;

    const isEditing = notesElement.style.display !== 'none';

    if (isEditing) {
        // 退出编辑模式
        notesElement.style.display = 'none';
        itemElement.classList.remove('product-attachment-edit-mode');
    } else {
        // 进入编辑模式
        notesElement.style.display = 'block';
        itemElement.classList.add('product-attachment-edit-mode');

        // 关闭其他编辑模式
        document.querySelectorAll('.product-attachment-edit-mode').forEach(el => {
            if (el !== itemElement) {
                el.classList.remove('product-attachment-edit-mode');
                const otherNotesId = el.querySelector('.product-attachment-notes').id;
                document.getElementById(otherNotesId).style.display = 'none';
            }
        });
    }
}

/**
 * 下载附件
 * @param {number} index - 附件索引
 */
function downloadProductAttachment(index) {
    const attachment = productAttachments[index];
    if (!attachment) return;

    const link = document.createElement('a');
    link.href = attachment.data;
    link.download = attachment.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast(`正在下载 ${attachment.name}`);
}

/**
 * 删除附件
 * @param {number} index - 附件索引
 */
function deleteProductAttachment(index) {
    const attachment = productAttachments[index];
    if (!attachment) return;

    if (confirm(`确定要删除附件 "${attachment.name}" 吗？`)) {
        // 从数组中删除
        productAttachments.splice(index, 1);

        // 更新列表显示
        updateProductAttachmentsList();

        showToast(`附件 "${attachment.name}" 已删除`);
        console.log(`删除附件: ${attachment.name}，剩余 ${productAttachments.length} 个附件`);
    }
}

/**
 * 编辑附件名称
 * @param {number} index - 附件索引
 * @param {Element} element - 名称元素
 */
function editProductAttachmentName(index, element) {
    const attachment = productAttachments[index];
    if (!attachment) return;

    const currentName = attachment.name;
    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentName;
    input.style.cssText = element.style.cssText;
    input.style.border = '1px solid #40a9ff';
    input.style.background = '#fff';
    input.style.width = '100%';
    input.style.fontSize = '1rem';
    input.style.fontWeight = '600';

    // 替换元素
    element.parentNode.replaceChild(input, element);
    input.focus();
    input.select();

    // 保存函数
    const saveEdit = () => {
        const newName = input.value.trim() || currentName;
        attachment.name = newName;

        const newElement = document.createElement('h4');
        newElement.className = 'product-attachment-name';
        newElement.textContent = newName;
        newElement.setAttribute('ondblclick', `editProductAttachmentName(${index}, this)`);
        newElement.setAttribute('title', '双击编辑文件名');

        input.parentNode.replaceChild(newElement, input);

        showToast(`文件名已更新为: ${newName}`);
        console.log(`附件 ${index} 名称已更新: ${newName}`);
    };

    // 事件监听
    input.addEventListener('blur', saveEdit);
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            saveEdit();
        } else if (e.key === 'Escape') {
            // 取消编辑
            input.parentNode.replaceChild(element, input);
        }
    });
}

/**
 * 更新附件备注
 * @param {number} index - 附件索引
 * @param {string} notes - 备注内容
 */
function updateProductAttachmentNotes(index, notes) {
    const attachment = productAttachments[index];
    if (!attachment) return;

    attachment.notes = notes;
    console.log(`附件 ${index} 备注已更新:`, notes);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取所有产品附件数据
 * @returns {Array} 附件数据数组
 */
function getAllProductAttachments() {
    return productAttachments.map(attachment => ({
        ...attachment
    }));
}

/**
 * 设置产品附件数据（用于加载保存的数据）
 * @param {Array} attachments - 附件数据数组
 */
function setAllProductAttachments(attachments) {
    if (!Array.isArray(attachments)) {
        console.error('附件数据格式错误，应为数组');
        return;
    }

    productAttachments = attachments.map((attachment, index) => ({
        ...attachment,
        id: attachment.id || `attachment_${++attachmentCounter}`
    }));

    // 更新计数器
    if (productAttachments.length > 0) {
        const maxId = Math.max(...productAttachments.map(a => {
            const match = a.id.match(/attachment_(\d+)/);
            return match ? parseInt(match[1]) : 0;
        }));
        attachmentCounter = Math.max(attachmentCounter, maxId);
    }

    updateProductAttachmentsList();
    showToast(`已加载 ${productAttachments.length} 个附件`);
}

/**
 * 清空所有产品附件
 */
function clearAllProductAttachments() {
    if (productAttachments.length === 0) {
        showToast('没有附件需要清空');
        return;
    }

    if (confirm(`确定要清空所有 ${productAttachments.length} 个产品附件吗？`)) {
        productAttachments = [];
        attachmentCounter = 0;
        updateProductAttachmentsList();
        showToast('所有附件已清空');
        console.log('所有产品附件已清空');
    }
}

/**
 * 导出产品附件数据（用于保存）
 * @returns {string} JSON格式的附件数据
 */
function exportProductAttachmentsData() {
    const attachments = getAllProductAttachments();
    const jsonData = JSON.stringify(attachments, null, 2);

    // 创建下载链接
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `product_attachments_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showToast('附件数据已导出');
    return jsonData;
}

/**
 * 导入产品附件数据（用于加载）
 * @param {string} jsonData - JSON格式的附件数据
 */
function importProductAttachmentsData(jsonData) {
    try {
        const attachments = JSON.parse(jsonData);
        setAllProductAttachments(attachments);
        showToast('附件数据加载成功');
    } catch (error) {
        console.error('附件数据导入失败:', error);
        showToast('附件数据格式错误');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('产品附件管理模块已加载');
    
    // 为备注文本框添加自动保存功能
    Object.keys(ATTACHMENT_TYPES).forEach(type => {
        const notesElement = document.getElementById(`${type}-notes`);
        if (notesElement) {
            notesElement.addEventListener('blur', function() {
                // 可以在这里添加自动保存逻辑
                console.log(`${type} 备注已更新:`, this.value);
            });
        }
    });
});
