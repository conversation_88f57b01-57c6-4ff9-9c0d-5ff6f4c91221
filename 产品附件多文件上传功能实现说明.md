# 产品附件多文件上传功能实现说明

## 功能概述

重新设计了产品页的附件上传功能，从原来的固定类型单文件上传改为支持多个附件的自由上传、查看、编辑和删除系统。

## 主要特性

### 1. 多文件上传
- ✅ **批量上传**：支持一次选择多个文件进行上传
- ✅ **拖拽上传**：支持拖拽文件到上传区域
- ✅ **格式支持**：JPG、PNG、PDF、DOC、DOCX格式
- ✅ **大小限制**：单个文件最大10MB
- ✅ **智能验证**：自动验证文件格式和大小

### 2. 附件管理
- ✅ **查看预览**：图片直接预览，PDF/DOC显示文件信息
- ✅ **编辑功能**：双击编辑文件名，编辑备注说明
- ✅ **下载功能**：一键下载任意附件
- ✅ **删除功能**：单独删除或批量清空
- ✅ **数据导出**：导出附件数据为JSON格式

### 3. 用户体验
- ✅ **直观界面**：清晰的文件列表和操作按钮
- ✅ **实时反馈**：上传进度、操作结果提示
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **键盘支持**：支持Enter/Escape快捷键操作

## 技术实现

### 1. HTML结构重构 (`templates/index.html`)

#### 1.1 新的上传界面
```html
<!-- 产品附件上传区域 -->
<div class="preview-section" id="product-attachments-section">
    <h3>📎 产品附件</h3>
    
    <!-- 附件上传区域 -->
    <div style="margin-bottom: 1.5rem; padding: 1rem; border: 2px dashed #d9d9d9; border-radius: 8px; background: #fafafa; text-align: center;">
        <div style="margin-bottom: 1rem;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📎</div>
            <h4>上传产品附件</h4>
            <p>支持 JPG、PNG、PDF、DOC、DOCX 格式，单个文件最大 10MB</p>
        </div>
        <div style="display: flex; gap: 0.5rem; justify-content: center; flex-wrap: wrap;">
            <input type="file" id="product-attachments-input" accept="image/*,.pdf,.doc,.docx" multiple style="display: none;" onchange="handleMultipleFileUpload(event)">
            <button onclick="document.getElementById('product-attachments-input').click()" class="btn primary">
                📁 选择文件
            </button>
            <button onclick="clearAllProductAttachments()" class="btn" style="background: #ff7875; color: white;">
                🗑️ 清空所有
            </button>
            <button onclick="exportProductAttachmentsData()" class="btn" style="background: #722ed1; color: white;">
                💾 导出数据
            </button>
        </div>
    </div>

    <!-- 附件列表区域 -->
    <div id="product-attachments-list" style="min-height: 100px;">
        <div id="attachments-empty-state" style="text-align: center; padding: 2rem; color: #999;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📋</div>
            <p>暂无附件</p>
            <small>点击上方"选择文件"按钮上传产品附件</small>
        </div>
    </div>
</div>
```

#### 1.2 单个附件项结构
```html
<div class="product-attachment-item" data-attachment-id="${attachment.id}">
    <div class="product-attachment-header">
        <div class="product-attachment-info">
            <div class="product-attachment-icon">${fileIcon}</div>
            <div class="product-attachment-details">
                <h4 class="product-attachment-name" ondblclick="editProductAttachmentName(${index}, this)">
                    ${attachment.name}
                </h4>
                <p class="product-attachment-meta">${fileSize} • ${uploadDate}</p>
            </div>
        </div>
        <div class="product-attachment-controls">
            <button class="product-attachment-btn view" onclick="toggleProductAttachmentPreview(${index})">
                👁️ 查看
            </button>
            <button class="product-attachment-btn edit" onclick="toggleProductAttachmentEdit(${index})">
                ✏️ 编辑
            </button>
            <button class="product-attachment-btn download" onclick="downloadProductAttachment(${index})">
                ⬇️ 下载
            </button>
            <button class="product-attachment-btn delete" onclick="deleteProductAttachment(${index})">
                🗑️ 删除
            </button>
        </div>
    </div>
    
    <!-- 预览区域 -->
    <div class="product-attachment-preview" id="preview-${attachment.id}">
        ${createProductAttachmentPreviewHtml(attachment)}
    </div>
    
    <!-- 备注编辑区域 -->
    <div class="product-attachment-notes" id="notes-${attachment.id}" style="display: none;">
        <label>备注说明：</label>
        <textarea placeholder="请输入附件的相关说明..." onchange="updateProductAttachmentNotes(${index}, this.value)">
            ${attachment.notes || ''}
        </textarea>
    </div>
</div>
```

### 2. CSS样式设计 (`templates/index.html`)

#### 2.1 附件项样式
```css
.product-attachment-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
    transition: box-shadow 0.2s, border-color 0.2s;
}

.product-attachment-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #40a9ff;
}
```

#### 2.2 操作按钮样式
```css
.product-attachment-btn {
    padding: 0.3rem 0.6rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 0.2rem;
}

.product-attachment-btn.view { background: #52c41a; color: white; }
.product-attachment-btn.edit { background: #faad14; color: white; }
.product-attachment-btn.delete { background: #ff4d4f; color: white; }
.product-attachment-btn.download { background: #1890ff; color: white; }
```

#### 2.3 编辑模式样式
```css
.product-attachment-edit-mode {
    border-color: #faad14 !important;
    background: #fffbf0 !important;
}

.product-attachment-edit-mode .product-attachment-icon {
    background: #fff7e6;
    color: #faad14;
}
```

### 3. JavaScript功能实现 (`static/js/product_attachments.js`)

#### 3.1 数据结构
```javascript
// 存储上传的附件数据（支持多个附件）
let productAttachments = [];

// 附件计数器，用于生成唯一ID
let attachmentCounter = 0;

// 单个附件数据结构
{
    id: 'attachment_1',
    name: '产品爆炸图.jpg',
    type: 'image/jpeg',
    size: 1024000,
    data: 'data:image/jpeg;base64,...',
    uploadTime: '2024-01-01T12:00:00.000Z',
    notes: '产品各部件分解图'
}
```

#### 3.2 核心功能函数
```javascript
// 多文件上传处理
function handleMultipleFileUpload(event) {
    const files = Array.from(event.target.files);
    // 验证文件格式和大小
    // 批量读取文件数据
    // 更新界面显示
}

// 更新附件列表显示
function updateProductAttachmentsList() {
    // 检查是否有附件
    // 生成附件项HTML
    // 更新DOM
}

// 切换预览显示
function toggleProductAttachmentPreview(index) {
    // 显示/隐藏预览区域
    // 关闭其他预览
}

// 切换编辑模式
function toggleProductAttachmentEdit(index) {
    // 显示/隐藏编辑区域
    // 切换编辑样式
}

// 下载附件
function downloadProductAttachment(index) {
    // 创建下载链接
    // 触发下载
}

// 删除附件
function deleteProductAttachment(index) {
    // 确认删除
    // 从数组中移除
    // 更新界面
}
```

#### 3.3 编辑功能
```javascript
// 编辑文件名（双击触发）
function editProductAttachmentName(index, element) {
    // 创建输入框
    // 替换显示元素
    // 处理保存/取消
}

// 更新备注
function updateProductAttachmentNotes(index, notes) {
    // 保存备注到数据
}
```

#### 3.4 数据管理
```javascript
// 获取所有附件数据
function getAllProductAttachments() {
    return productAttachments.map(attachment => ({ ...attachment }));
}

// 设置附件数据（用于加载）
function setAllProductAttachments(attachments) {
    productAttachments = attachments.map((attachment, index) => ({
        ...attachment,
        id: attachment.id || `attachment_${++attachmentCounter}`
    }));
    updateProductAttachmentsList();
}

// 导出附件数据
function exportProductAttachmentsData() {
    const jsonData = JSON.stringify(getAllProductAttachments(), null, 2);
    // 创建下载链接
    // 触发下载
}
```

### 4. 拖拽上传功能

#### 4.1 拖拽事件处理
```javascript
// 拖拽悬停
uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '#f0f9ff';
    uploadArea.style.borderColor = '#40a9ff';
});

// 拖拽离开
uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    uploadArea.style.backgroundColor = '';
    uploadArea.style.borderColor = '';
});

// 文件放置
uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    // 处理拖拽的文件
});
```

## 用户操作流程

### 1. 上传附件
```
选择文件/拖拽文件 → 验证格式和大小 → 读取文件数据 → 添加到列表 → 显示在界面
```

### 2. 查看附件
```
点击"查看"按钮 → 切换预览显示 → 图片直接显示/其他文件显示信息
```

### 3. 编辑附件
```
点击"编辑"按钮 → 进入编辑模式 → 编辑备注 → 自动保存
双击文件名 → 内联编辑 → Enter保存/Escape取消
```

### 4. 下载附件
```
点击"下载"按钮 → 创建下载链接 → 触发浏览器下载
```

### 5. 删除附件
```
点击"删除"按钮 → 确认对话框 → 从数组中移除 → 更新界面显示
```

## 数据持久化

### 1. 数据格式
```json
[
    {
        "id": "attachment_1",
        "name": "产品爆炸图.jpg",
        "type": "image/jpeg",
        "size": 1024000,
        "data": "data:image/jpeg;base64,...",
        "uploadTime": "2024-01-01T12:00:00.000Z",
        "notes": "产品各部件分解图"
    },
    {
        "id": "attachment_2",
        "name": "技术规格.pdf",
        "type": "application/pdf",
        "size": 2048000,
        "data": "data:application/pdf;base64,...",
        "uploadTime": "2024-01-01T12:05:00.000Z",
        "notes": "详细技术参数说明"
    }
]
```

### 2. 保存和加载
```javascript
// 保存到本地存储或服务器
const attachmentsData = getAllProductAttachments();
localStorage.setItem('productAttachments', JSON.stringify(attachmentsData));

// 从本地存储或服务器加载
const savedData = localStorage.getItem('productAttachments');
if (savedData) {
    setAllProductAttachments(JSON.parse(savedData));
}
```

## 兼容性和性能

### 1. 浏览器兼容性
- ✅ **现代浏览器**：Chrome、Firefox、Safari、Edge
- ✅ **文件API**：FileReader、DataTransfer、Blob
- ✅ **拖拽API**：HTML5 Drag and Drop

### 2. 性能优化
- ✅ **文件大小限制**：单个文件最大10MB
- ✅ **格式验证**：上传前验证，避免无效文件
- ✅ **内存管理**：及时清理不需要的数据
- ✅ **DOM优化**：批量更新，减少重绘

### 3. 错误处理
- ✅ **文件验证**：格式、大小检查
- ✅ **读取错误**：FileReader错误处理
- ✅ **用户提示**：清晰的错误信息和操作反馈

## 总结

成功实现了产品附件的多文件上传管理系统：

1. **功能完整**：上传、查看、编辑、下载、删除一应俱全
2. **用户友好**：直观的界面设计和流畅的交互体验
3. **技术先进**：支持拖拽上传、实时预览、内联编辑
4. **数据安全**：完整的验证机制和错误处理
5. **扩展性强**：模块化设计，易于维护和扩展

现在用户可以方便地管理产品的各种附件文档，大大提升了工作效率！🎉
