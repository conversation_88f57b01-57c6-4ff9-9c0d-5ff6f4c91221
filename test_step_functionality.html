<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤功能完整测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.danger { background: #dc3545; color: white; }
        .btn.warning { background: #ffc107; color: black; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .function-test {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
        }
        
        .step-counter {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 步骤功能完整测试</h1>
        
        <div class="test-panel">
            <h2>📋 测试控制面板</h2>
            <button class="btn primary" onclick="initializeTest()">🚀 初始化测试环境</button>
            <button class="btn success" onclick="runAllTests()">🔄 运行所有测试</button>
            <button class="btn warning" onclick="resetTest()">🔄 重置测试</button>
            <button class="btn danger" onclick="clearLogs()">🗑️ 清空日志</button>
            
            <div id="main-status" class="status info">
                ℹ️ 请先点击"初始化测试环境"开始测试
            </div>
        </div>

        <div class="test-grid">
            <div class="function-test">
                <h3>➕ 步骤添加功能测试</h3>
                <div class="step-counter" id="add-counter">当前步骤数: 0</div>
                
                <button class="btn success" onclick="testAddAtBeginning()">在开头添加</button>
                <button class="btn success" onclick="testAddAtEnd()">在末尾添加</button>
                <button class="btn success" onclick="testAddInMiddle()">在中间添加</button>
                <button class="btn success" onclick="testAddBefore()">在指定步骤前添加</button>
                <button class="btn success" onclick="testAddAfter()">在指定步骤后添加</button>
                
                <div id="add-status" class="status info">等待测试...</div>
            </div>

            <div class="function-test">
                <h3>❌ 步骤删除功能测试</h3>
                <div class="step-counter" id="delete-counter">当前步骤数: 0</div>
                
                <button class="btn danger" onclick="testDeleteFirst()">删除第一个步骤</button>
                <button class="btn danger" onclick="testDeleteLast()">删除最后一个步骤</button>
                <button class="btn danger" onclick="testDeleteMiddle()">删除中间步骤</button>
                <button class="btn danger" onclick="testDeleteSpecific()">删除指定步骤</button>
                
                <div id="delete-status" class="status info">等待测试...</div>
            </div>
        </div>

        <div class="test-panel">
            <h3>🏭 工站显示区域</h3>
            <div id="stations-list" style="min-height: 200px; border: 2px dashed #ccc; padding: 20px; border-radius: 6px;">
                工站将在这里显示...
            </div>
        </div>

        <div class="test-panel">
            <h3>📝 详细测试日志</h3>
            <div id="test-log" class="log-container">
                测试日志将在这里显示...
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>

    <script>
        // 全局测试变量
        let testStationIndex = 0;
        let testResults = {
            add: { passed: 0, total: 0 },
            delete: { passed: 0, total: 0 }
        };

        // 日志功能
        function log(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('test-log').textContent = '';
            log('日志已清空');
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const icons = { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️' };
            element.className = `status ${type}`;
            element.textContent = `${icons[type]} ${message}`;
        }

        function updateStepCounter() {
            if (processStationsData && processStationsData[0] && processStationsData[0].process_steps) {
                const count = processStationsData[0].process_steps.length;
                document.getElementById('add-counter').textContent = `当前步骤数: ${count}`;
                document.getElementById('delete-counter').textContent = `当前步骤数: ${count}`;
                return count;
            }
            return 0;
        }

        // 初始化测试环境
        function initializeTest() {
            log('开始初始化测试环境...');
            
            try {
                // 检查必要的类和函数
                if (typeof StationGenerator === 'undefined') {
                    throw new Error('StationGenerator类未定义');
                }

                // 创建测试工站数据
                const testStation = {
                    station_number: '10',
                    station_name: '测试工站',
                    content: '这是用于测试步骤功能的工站',
                    process_steps: [
                        {
                            step_number: '1',
                            description: '初始步骤1',
                            operator: '人',
                            quality_requirements: '质量要求1',
                            error_prevention: '防错要求1'
                        },
                        {
                            step_number: '2',
                            description: '初始步骤2',
                            operator: '设备',
                            quality_requirements: '质量要求2',
                            error_prevention: '防错要求2'
                        }
                    ]
                };

                // 设置全局数据
                window.processStationsData = [testStation];
                
                // 创建工站生成器
                if (!window.stationGenerator) {
                    window.stationGenerator = new StationGenerator();
                }
                
                // 生成工站显示
                stationGenerator.generateProcessStations([testStation]);
                
                updateStepCounter();
                updateStatus('main-status', '测试环境初始化成功，可以开始测试', 'success');
                log('测试环境初始化完成');
                
                // 检查关键函数
                const functions = [
                    'insertProcessStepBefore',
                    'insertProcessStepAfter', 
                    'deleteProcessStep',
                    'addProcessStep',
                    'insertProcessStep'
                ];
                
                functions.forEach(funcName => {
                    const exists = typeof window[funcName] === 'function';
                    log(`函数检查 ${funcName}: ${exists ? '存在' : '不存在'}`, exists ? 'info' : 'error');
                });

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                updateStatus('main-status', `初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试添加功能
        function testAddAtBeginning() {
            log('测试在开头添加步骤...');
            try {
                const beforeCount = updateStepCounter();
                insertProcessStep(testStationIndex, 0);
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount + 1) {
                        log('在开头添加步骤 - 成功', 'success');
                        updateStatus('add-status', '在开头添加步骤测试通过', 'success');
                        testResults.add.passed++;
                    } else {
                        log('在开头添加步骤 - 失败', 'error');
                        updateStatus('add-status', '在开头添加步骤测试失败', 'error');
                    }
                    testResults.add.total++;
                }, 100);
            } catch (error) {
                log(`在开头添加步骤失败: ${error.message}`, 'error');
                updateStatus('add-status', '在开头添加步骤测试失败', 'error');
                testResults.add.total++;
            }
        }

        function testAddAtEnd() {
            log('测试在末尾添加步骤...');
            try {
                const beforeCount = updateStepCounter();
                addProcessStep(testStationIndex);
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount + 1) {
                        log('在末尾添加步骤 - 成功', 'success');
                        updateStatus('add-status', '在末尾添加步骤测试通过', 'success');
                        testResults.add.passed++;
                    } else {
                        log('在末尾添加步骤 - 失败', 'error');
                        updateStatus('add-status', '在末尾添加步骤测试失败', 'error');
                    }
                    testResults.add.total++;
                }, 100);
            } catch (error) {
                log(`在末尾添加步骤失败: ${error.message}`, 'error');
                updateStatus('add-status', '在末尾添加步骤测试失败', 'error');
                testResults.add.total++;
            }
        }

        function testAddInMiddle() {
            log('测试在中间添加步骤...');
            try {
                const beforeCount = updateStepCounter();
                if (beforeCount < 2) {
                    log('步骤数量不足，无法测试中间插入', 'warning');
                    return;
                }
                const middleIndex = Math.floor(beforeCount / 2);
                insertProcessStep(testStationIndex, middleIndex);
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount + 1) {
                        log('在中间添加步骤 - 成功', 'success');
                        updateStatus('add-status', '在中间添加步骤测试通过', 'success');
                        testResults.add.passed++;
                    } else {
                        log('在中间添加步骤 - 失败', 'error');
                        updateStatus('add-status', '在中间添加步骤测试失败', 'error');
                    }
                    testResults.add.total++;
                }, 100);
            } catch (error) {
                log(`在中间添加步骤失败: ${error.message}`, 'error');
                updateStatus('add-status', '在中间添加步骤测试失败', 'error');
                testResults.add.total++;
            }
        }

        function testAddBefore() {
            log('测试在指定步骤前添加...');
            try {
                const beforeCount = updateStepCounter();
                insertProcessStepBefore(testStationIndex, 1);
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount + 1) {
                        log('在指定步骤前添加 - 成功', 'success');
                        updateStatus('add-status', '在指定步骤前添加测试通过', 'success');
                        testResults.add.passed++;
                    } else {
                        log('在指定步骤前添加 - 失败', 'error');
                        updateStatus('add-status', '在指定步骤前添加测试失败', 'error');
                    }
                    testResults.add.total++;
                }, 100);
            } catch (error) {
                log(`在指定步骤前添加失败: ${error.message}`, 'error');
                updateStatus('add-status', '在指定步骤前添加测试失败', 'error');
                testResults.add.total++;
            }
        }

        function testAddAfter() {
            log('测试在指定步骤后添加...');
            try {
                const beforeCount = updateStepCounter();
                insertProcessStepAfter(testStationIndex, 0);
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount + 1) {
                        log('在指定步骤后添加 - 成功', 'success');
                        updateStatus('add-status', '在指定步骤后添加测试通过', 'success');
                        testResults.add.passed++;
                    } else {
                        log('在指定步骤后添加 - 失败', 'error');
                        updateStatus('add-status', '在指定步骤后添加测试失败', 'error');
                    }
                    testResults.add.total++;
                }, 100);
            } catch (error) {
                log(`在指定步骤后添加失败: ${error.message}`, 'error');
                updateStatus('add-status', '在指定步骤后添加测试失败', 'error');
                testResults.add.total++;
            }
        }

        // 测试删除功能（模拟用户确认）
        function testDeleteFirst() {
            log('测试删除第一个步骤...');
            try {
                const beforeCount = updateStepCounter();
                if (beforeCount === 0) {
                    log('没有步骤可删除', 'warning');
                    return;
                }
                
                // 模拟用户确认
                const originalConfirm = window.confirm;
                window.confirm = () => true;
                
                deleteProcessStep(testStationIndex, 0);
                
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount - 1) {
                        log('删除第一个步骤 - 成功', 'success');
                        updateStatus('delete-status', '删除第一个步骤测试通过', 'success');
                        testResults.delete.passed++;
                    } else {
                        log('删除第一个步骤 - 失败', 'error');
                        updateStatus('delete-status', '删除第一个步骤测试失败', 'error');
                    }
                    testResults.delete.total++;
                    window.confirm = originalConfirm;
                }, 100);
            } catch (error) {
                log(`删除第一个步骤失败: ${error.message}`, 'error');
                updateStatus('delete-status', '删除第一个步骤测试失败', 'error');
                testResults.delete.total++;
            }
        }

        function testDeleteLast() {
            log('测试删除最后一个步骤...');
            try {
                const beforeCount = updateStepCounter();
                if (beforeCount === 0) {
                    log('没有步骤可删除', 'warning');
                    return;
                }
                
                // 模拟用户确认
                const originalConfirm = window.confirm;
                window.confirm = () => true;
                
                deleteProcessStep(testStationIndex, beforeCount - 1);
                
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount - 1) {
                        log('删除最后一个步骤 - 成功', 'success');
                        updateStatus('delete-status', '删除最后一个步骤测试通过', 'success');
                        testResults.delete.passed++;
                    } else {
                        log('删除最后一个步骤 - 失败', 'error');
                        updateStatus('delete-status', '删除最后一个步骤测试失败', 'error');
                    }
                    testResults.delete.total++;
                    window.confirm = originalConfirm;
                }, 100);
            } catch (error) {
                log(`删除最后一个步骤失败: ${error.message}`, 'error');
                updateStatus('delete-status', '删除最后一个步骤测试失败', 'error');
                testResults.delete.total++;
            }
        }

        function testDeleteMiddle() {
            log('测试删除中间步骤...');
            try {
                const beforeCount = updateStepCounter();
                if (beforeCount < 3) {
                    log('步骤数量不足，无法测试中间删除', 'warning');
                    return;
                }
                
                // 模拟用户确认
                const originalConfirm = window.confirm;
                window.confirm = () => true;
                
                const middleIndex = Math.floor(beforeCount / 2);
                deleteProcessStep(testStationIndex, middleIndex);
                
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount - 1) {
                        log('删除中间步骤 - 成功', 'success');
                        updateStatus('delete-status', '删除中间步骤测试通过', 'success');
                        testResults.delete.passed++;
                    } else {
                        log('删除中间步骤 - 失败', 'error');
                        updateStatus('delete-status', '删除中间步骤测试失败', 'error');
                    }
                    testResults.delete.total++;
                    window.confirm = originalConfirm;
                }, 100);
            } catch (error) {
                log(`删除中间步骤失败: ${error.message}`, 'error');
                updateStatus('delete-status', '删除中间步骤测试失败', 'error');
                testResults.delete.total++;
            }
        }

        function testDeleteSpecific() {
            log('测试删除指定步骤...');
            try {
                const beforeCount = updateStepCounter();
                if (beforeCount === 0) {
                    log('没有步骤可删除', 'warning');
                    return;
                }
                
                // 模拟用户确认
                const originalConfirm = window.confirm;
                window.confirm = () => true;
                
                deleteProcessStep(testStationIndex, 0);
                
                setTimeout(() => {
                    const afterCount = updateStepCounter();
                    if (afterCount === beforeCount - 1) {
                        log('删除指定步骤 - 成功', 'success');
                        updateStatus('delete-status', '删除指定步骤测试通过', 'success');
                        testResults.delete.passed++;
                    } else {
                        log('删除指定步骤 - 失败', 'error');
                        updateStatus('delete-status', '删除指定步骤测试失败', 'error');
                    }
                    testResults.delete.total++;
                    window.confirm = originalConfirm;
                }, 100);
            } catch (error) {
                log(`删除指定步骤失败: ${error.message}`, 'error');
                updateStatus('delete-status', '删除指定步骤测试失败', 'error');
                testResults.delete.total++;
            }
        }

        // 运行所有测试
        function runAllTests() {
            log('开始运行所有测试...');
            testResults = { add: { passed: 0, total: 0 }, delete: { passed: 0, total: 0 } };
            
            // 重新初始化
            initializeTest();
            
            setTimeout(() => {
                // 测试添加功能
                testAddAtEnd();
                setTimeout(() => testAddAtBeginning(), 200);
                setTimeout(() => testAddInMiddle(), 400);
                setTimeout(() => testAddBefore(), 600);
                setTimeout(() => testAddAfter(), 800);
                
                // 测试删除功能
                setTimeout(() => testDeleteLast(), 1000);
                setTimeout(() => testDeleteFirst(), 1200);
                setTimeout(() => testDeleteMiddle(), 1400);
                setTimeout(() => testDeleteSpecific(), 1600);
                
                // 显示最终结果
                setTimeout(() => {
                    const addResult = `${testResults.add.passed}/${testResults.add.total}`;
                    const deleteResult = `${testResults.delete.passed}/${testResults.delete.total}`;
                    log(`测试完成 - 添加功能: ${addResult}, 删除功能: ${deleteResult}`, 'success');
                    updateStatus('main-status', `测试完成 - 添加: ${addResult}, 删除: ${deleteResult}`, 'success');
                }, 2000);
            }, 500);
        }

        function resetTest() {
            log('重置测试环境...');
            initializeTest();
            testResults = { add: { passed: 0, total: 0 }, delete: { passed: 0, total: 0 } };
            updateStatus('add-status', '等待测试...', 'info');
            updateStatus('delete-status', '等待测试...', 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，准备测试环境');
            log(`StationGenerator: ${typeof StationGenerator !== 'undefined' ? '已定义' : '未定义'}`);
            log(`processStationsData: ${typeof processStationsData !== 'undefined' ? '已定义' : '未定义'}`);
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error.message}`, 'error');
            updateStatus('main-status', `发生错误: ${event.error.message}`, 'error');
        });
    </script>
</body>
</html>
