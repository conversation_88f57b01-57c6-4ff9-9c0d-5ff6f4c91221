SAB产品族完整知识库

=== 概述 ===
本知识库包含SAB产品族A-F所有类别的完整工艺流程和设备配置信息。
根据输入的零件组合，可以准确识别SAB类别并生成相应的工艺和设备要求。

=== SAB类别识别规则 ===
根据零件组合识别SAB类别：
- SAB-A类: Deflector, inflator, cushion, soft cover (4个零件)
- SAB-B类: Deflector, inflator, cushion, Harness, soft cover (5个零件)
- SAB-C类: Deflector, inflator, cushion, Harness, Bracket, Nuts, soft cover (7个零件)
- SAB-D类: Deflector, inflator, cushion, Harness, hard cover (5个零件)
- SAB-E类: Deflector, inflator, cushion, Bracket, Nuts, housing (6个零件)
- SAB-F类: Deflector, inflator, cushion, hard cover, housing, 3D heat (6个零件)

=== 输出格式要求 ===
必须严格按照以下Markdown格式输出，确保工艺部分和设备部分完全对应：

### 一、工艺部分

#### ST10 工站名称
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

### 二、设备部分

#### ST10 工站名称
一、设备名称-Equipment 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture 
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]


==================================================
SAB-A类详细信息
==================================================

零件组成:
- **Deflector** - 导流片，用于气流导向
- **inflator** - 发生器，气袋充气装置
- **cushion** - 气袋，主要缓冲组件
- **soft cover** - 软包布，柔性覆盖材料

工艺流程:
#### ST10 ST10
（一）
1. 工艺过程描述: 员工拿取发生器
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 将发生器放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 夹具自动夹紧
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 员工拿取一个导流片
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 设备自动影像检测导流片安装正确
   - 人or设备: 设备
   - 产品特性要求: 导流片安装方向正确
   - 过程防错要求: 无漏装，无错装

8. 工艺过程描述: 设备自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 正确的发生器

9. 工艺过程描述: 员工拿取一个气袋
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 将气袋套到发生器上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 设备自动影像检测气袋正确套入
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 无扫A做B

13. 工艺过程描述: 设备自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 正确的气袋

14. 工艺过程描述: 设备自动松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST20 ST20
（一）
1. 工艺过程描述: 员工将组件放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 夹具自动夹紧螺柱
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 员工将气袋拉直放入上夹头中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 上夹头自动夹紧
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 员工手持线束头，扫描线束条码
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 员工退出安全光栅
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 自动扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: nan

8. 工艺过程描述: 设备自动折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠
   - 过程防错要求: nan

9. 工艺过程描述: 员工将气袋套入包布中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 踩脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 员工将包布包好
   - 人or设备: nan
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST30 ST30
（一）
1. 工艺过程描述: 员工将组件放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 设备自动夹紧螺柱
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 员工将线束头插入电检夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 手拍触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 安全门关闭
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 设备自动拍打模块
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 设备自动检测模块厚度
   - 人or设备: 设备
   - 产品特性要求: 正确的模块厚度
   - 过程防错要求: nan

8. 工艺过程描述: 设备自动检测模块电性能
   - 人or设备: 设备
   - 产品特性要求: 正确的模块电性能
   - 过程防错要求: nan

9. 工艺过程描述: 安全门打开
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 自动打印标签
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 员工将标签黏贴在模块上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 手拍触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

13. 工艺过程描述: 设备自动扫描总成标签
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

14. 工艺过程描述: 设备自动松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

15. 工艺过程描述: 下线，包装
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

设备配置:
#### ST10 SAB-A-ST10
一、SAB-发生器预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器扫描OK/NG，气袋扫描OK/NG，影像OK/NG 等指示灯
6.发生器夹持治具下方台面配置黑色背景板
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
10.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.导流片安装方向正确、导流片无错装、漏装
导流片通过上方IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.无扫A做B
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、发生器、气袋预装治具-Fixture 
（一）机械要求:
1. 1.发生器平放插入治具内，以发生器塑料端面为基准，治具内带有传感器检测放置到位。
2.治具夹持不能对发生器端面造成磕碰损伤
3.治具上配置发生器螺柱导向功能，原位伸出，发生器夹持后缩回
4.治具松开，发生器不会掉落
5.治具夹紧发生器后不晃动、松动，治具夹持后间隙需小于发生器直径，夹持长度至少要15mm
6.发生器夹持治具整体设计哈丁成快换形式，到位后气缸自锁。
7.治具于产品接触部分无锐边，毛刺，表面需倒角
8.治具无夹手风险
9.治具带有防护罩，防止螺丝掉落

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 SAB-A-ST20
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断气袋是否歪斜
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备前方需要2组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。自动转折机构部分外部使用亚克力板进行物理防护
5.设备卷折需能实现inboard和outboard折叠的需求；折叠首折尺寸和折尺尺寸需要满足图纸的要求;
6.设备增设扫描枪，扫描气袋条码，增加滑轨，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代
12.设备由气袋卷折模组、上气袋夹持模组及产品定位机构3大部分组成
13.气袋卷折模组可实现上下，左右移动，上下移动由伺服实现，伺服行程450mm，极限位置都须有限位开关、机械硬限位及独立零位传感器；横向左右利用气缸完成移动，带有硬限位及缓冲装置；伺服控制旋转气缸从而满足折尺的正反转。
14.上部气袋夹持机构有传感器检测气袋放置到位；内部小气缸夹持气袋，开口大小≤4mm，无夹手风险
15.夹持机构整体上下可通过伺服移动。左右滑轨，可分别单独通过拉拔销进行移动。下方有托板防止异物掉落。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.伺服驱动器需要有STO功能
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.气袋不歪斜：
通过设备上方的相机影像来确保气袋夹持不歪斜
5.正确的折叠
通过预设的折叠参数保证气袋折叠。折叠参数上传追溯控制
方案1：[具体方案]

二、1.产品定位治具
2.折叠尺-Fixture 
（一）机械要求:
1. 产品定位治具：
1.左右共用一副工装，螺柱夹爪材质铜，增加螺纹。发生器螺柱夹持不松脱
2.治具带有发生器放反防错
3.治具设计轻便，不干涉操作
4.治具设计成哈丁接口快换
5.治具与产品接触部分无锐边，毛刺，表面需倒角
6.治具无夹手风险
7.传感器放大器安装位置便于维修调试
8.两侧安装把手，方便拿取
9.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
折叠尺：
1.折尺尺寸根据实际产品图纸制作，表面光滑，无毛刺。折尺滑槽座，滑槽按ALV统一标准设计，由二维码防错，折尺上开凹槽粘贴二维码，避免剐蹭
2.折尺固定滑槽尺寸需与折尺滑槽座尺寸适宜，公差合理，折尺插入后，前端不晃动、歪斜

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST30 SAB-A-ST30
一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
4.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
5.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
6.设备顶上带有蜂鸣器的红黄绿三色灯
7.设备设计合理，尺寸紧凑，空间无浪费
8.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
方案1：[具体方案]

二、1.产品定位治具
2.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
2.治具托板为平板
3.治具设计合理，无安全风险
4.治具设计轻便，整体重量≤7.5Kg
5.传感器放大器安装位置便于维修调试
6.传感器检测发生器螺柱，无需夹爪夹持
7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
9.两侧安装把手，方便拿取
10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
厚度GT检测机构：
1.厚度检测机构按照最新的设备标机设计
2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
3.GT头下压过程中顺畅，不卡顿
4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
5.厚度检测能覆盖20mm-50mm之间的产品
6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
7.测厚压板左右考虑共用，下压时避开产品发生器区域
8.压板需设计成快换，并带有防错
9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
10.压板机构自重掉落过程中顺畅、不卡顿
11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
归零、标准点检块：
1.归零块：对产品厚度检测机构GT进行归零
2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]


==================================================
SAB-B类详细信息
==================================================

零件组成:
- **Deflector** - 导流片，用于气流导向
- **inflator** - 发生器，气袋充气装置
- **cushion** - 气袋，主要缓冲组件
- **Harness** - 线束，电气连接组件
- **soft cover** - 软包布，柔性覆盖材料

工艺流程:
#### ST10 ST10
（一）
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: 导流片无错装、漏装、安装方向正确
   - 过程防错要求: 无夹伤，发生器正确，发生器插入方向正确，发生器螺柱方向正确

#### ST20 ST20
（一）
1. 工艺过程描述: 拿取组件，将其固定在夹具中
   - 人or设备: 人
   - 产品特性要求: 不伤螺丝，检测发生器方向，并检测支架安装到位
   - 过程防错要求: nan

2. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: nan

3. 工艺过程描述: 将气袋拉直，上夹具夹紧气袋
   - 人or设备: 人
   - 产品特性要求: 光滑无毛刺，向上拉直需要左右上下可调
   - 过程防错要求: nan

4. 工艺过程描述: 退出光栅，按下启动按钮
设备自动进行折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠方式，气袋无破损，无异物
   - 过程防错要求: 折叠动作要同步，折叠尺尽量不晃动，折叠尺可换，折叠尺防错

5. 工艺过程描述: 将一半气袋塞入软包布中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 踩脚踏，取下组件，整理好包布，扣入螺柱，送入下一工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST30 ST30
（一）
1. 工艺过程描述: 拿取产品放入夹具，气缸夹紧螺柱
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 将产品放入夹具，并将线束插入夹具
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 按按钮，扫描气袋条码，关安全门
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 高度检查
   - 人or设备: 设备
   - 产品特性要求: 正确的模块尺寸
   - 过程防错要求: nan

5. 工艺过程描述: 打印客户标签，正确的条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 黏贴客户标签
   - 人or设备: 设备
   - 产品特性要求: 正确的客户标签内容
   - 过程防错要求: 条码阈值，条码数量，条码角度可控制

7. 工艺过程描述: 按双手按钮，扫描客户标签条码，释放组件，包装
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

设备配置:
#### ST10 SAB-B-ST10
一、SAB-发生器、线束预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备下方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋及线束条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器标签扫描OK/NG，气袋标签扫描OK/NG，影像OK/NG等指示灯
6.相机检测面须配置背景板，防止周围环境影响相机检测
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
10.设备两侧1组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.线束GT测量方式一次测量法：即检测机构基准压头与GT检测头同时接触基准面与测量面，通过一次循环计算线束高度
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.导流片安装方向正确、导流片无错装、漏装
通过设备内部IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.线束安装到位
通过设备GT线束检测机构100%检测线束安装高度，发送给追溯，追溯判断是否在线束高度范围内
4.无扫A做B
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、发生器夹持治具
线束GT检测机构-Fixture 
（一）机械要求:
1. 线束GT检测机构：
1.每次测量检测面位置不变，测量过程中检测头需与检测端面垂直
2.需要有足够的检测行程，GT行程不能顶死，行程在60~80%之间。
3.线束高度检测机构检测时压力最小66N，最大132N，速度不能过快
4.GT放大器安装在机构旁，便于观察
5.基准面与测量面从线束卡扣 到发生器连接器端面（黑色塑料面），测量两者高度差值
6.基准压头与GT顶针不能卡顿，GT顶针检测过程中不能擦伤线束
7.高度检测基准压头到位需有C型传感器感应
8.高度检测头到位后需等待最小250ms，最大500ms后开始检测
发生器夹持治具：
1.左右发生器共用一副治具
2.治具底部增加传感器，检测发生器放置到位。
3.导流片防错依靠设备下方相机防错
4.发生器放入治具定位圆筒内，不晃动
5.治具后侧带有气缸，气缸上带有一对夹爪。发生器放置到位后，两侧夹爪伸出夹紧包住发生器，发生器不前倾，不歪斜。夹持位置避开发生器焊渣，焊缝。
6.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
7.两侧安装把手，方便拿取
8.治具电气哈丁，快换接头适配原有设备接口

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 SAB-B-ST20
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断气袋是否歪斜
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备前方需要2组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。自动转折机构部分外部使用亚克力板进行物理防护
5.设备卷折需能实现inboard和outboard折叠的需求；折叠首折尺寸和折尺尺寸需要满足图纸的要求;
6.设备增设扫描枪，扫描气袋条码，增加滑轨，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代
12.设备由气袋卷折模组、上气袋夹持模组及产品定位机构3大部分组成
13.气袋卷折模组可实现上下，左右移动，上下移动由伺服实现，伺服行程450mm，极限位置都须有限位开关、机械硬限位及独立零位传感器；横向左右利用气缸完成移动，带有硬限位及缓冲装置；伺服控制旋转气缸从而满足折尺的正反转。
14.上部气袋夹持机构有传感器检测气袋放置到位；内部小气缸夹持气袋，开口大小≤4mm，无夹手风险
15.夹持机构整体上下可通过伺服移动。左右滑轨，可分别单独通过拉拔销进行移动。下方有托板防止异物掉落。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.伺服驱动器需要有STO功能
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.气袋不歪斜：
通过设备上方的相机影像来确保气袋夹持不歪斜
5.正确的折叠
通过预设的折叠参数保证气袋折叠。折叠参数上传追溯控制
方案1：[具体方案]

二、1.产品定位治具
2.折叠尺-Fixture 
（一）机械要求:
1. 产品定位治具：
1.左右共用一副工装，螺柱夹爪材质铜，增加螺纹。发生器螺柱夹持不松脱
2.治具带有发生器放反防错
3.治具设计轻便，不干涉操作
4.治具设计成哈丁接口快换
5.治具与产品接触部分无锐边，毛刺，表面需倒角
6.治具无夹手风险
7.传感器放大器安装位置便于维修调试
8.两侧安装把手，方便拿取
9.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
折叠尺：
1.折尺尺寸根据实际产品图纸制作，表面光滑，无毛刺。折尺滑槽座，滑槽按ALV统一标准设计，由二维码防错，折尺上开凹槽粘贴二维码，避免剐蹭
2.折尺固定滑槽尺寸需与折尺滑槽座尺寸适宜，公差合理，折尺插入后，前端不晃动、歪斜

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST30 SAB-B-ST30
一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备周边防护板材质要求：PC阳光板，防爆，厚度≥8mm
4.设备配安全升降门，安全门设计为本质安全（两侧滑轮+钢索），手拍启动
5.设备左上方需增加泄气圆孔，直径150mm
6.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的线束电性能
通过E-check电检机构100%在线检测
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
5.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
方案1：[具体方案]

二、1.产品定位治具
2.E-check检测机构
3.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
  1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
  2.治具托板为平板
  3.治具设计合理，无安全风险
  4.治具设计轻便，整体重量≤7.5Kg
  5.传感器放大器安装位置便于维修调试
  6.传感器检测发生器螺柱，无需夹爪夹持
  7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
  8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
  9.两侧安装把手，方便拿取
  10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
E-check检测机构:
 1.E-check检测模组可整体快换，气电接口使用哈丁。
 2.线束插入检测治具后锁紧，不晃动
 3.探针焊接电线焊点要光滑，不能有虚焊或毛刺，线路应用热缩管隔离，选择镀银线，内径0.5mm² 
 4.气缸传感器检测，需要有到位点和原点监控
 5.电阻仪通讯线必须采用原装屏蔽线通讯，不允许中间 有焊接或转接。
 6.检测机构与电阻仪接线方式为四线法，并联点需尽量接近E-check检测机构，减少内阻
 7.E-check内部结构根据不同线束仿形设计
 8.线束锁紧装置设计时，槽公差+0.05mm，块公差-0.05mm，槽和块硬度处理，表面光滑处理
 9.L型连锁装置，连锁杆必须安装轴套，固定螺丝应采用内六角螺丝加装防松弹片固定
 10.L型解锁装置是线束卡扣解锁机构，与短路片分离器固定座连接（解锁杆必须安装轴套，保证解锁装置顺畅运行。）
 11.轴套：MISUMI标准件
 12.舌片在探针座导向槽里面前后移动，导向槽设计尺寸走上公差+0.02～+0.05，表面粗糙度1.6；舌片尺寸走下公差-0.02～-0.05，表面粗糙度1.6
 13.探针安装孔应保证探针方便安装并固定牢固，保证探针组装后中心与线束PIN在一个中心位置，与线束pin针配合良好
 14.探针孔位设计时参考选择探针型号要求的探针加工孔位（参考华容探针选型手册）
 15.探针座材质：PEI1000。
 16.探针座设计必须有安装定位孔，要保证安装一致性，便于拆装更换
 17.探针座尽量做长贴近产品，以更好的导向舌片。
 18.舌片固定后不应与探针座有干涉，与探针的间隙保证在0.3-0.5mm之间
 19.舌片材质PEI2300，舌片安装孔采用圆孔，可定位，不得调节，保证拆装一致性。
 20.舌片前端厚0.5mm，舌片插入理想深度1.5mm（依照实际产品定义选择） 
 21.舌片宽度大于短路片间距1mm
 22.选用一体式探针，表面镀金。（可参照探针厂商推荐的选型手册或厂家技术支持。推荐华荣一体探针型号：PH-2H PH-3H PH-4H,可根据产品线束PIN的直径选择。）
 23.探针伸出长度应保证与线束pin针有良好的接触，探针压缩行程1/3 - 2/3行程之间。
 24.探针与电阻仪连接线选用0.5mm²的镀银线，线路应用热缩管隔离。
 25.探针选用碗口或九爪型（依照实际产品定义选择） 
 26.卡扣仿形槽尺寸根据具体产品线束结构设计
 27.卡扣仿形槽上下左右工差应控制在单边0.1mm
 28.仿型槽安装位置应有定位孔定位，线束卡扣仿型槽应增加线束卡扣限位
 29.通用底板依据标机设计
厚度GT检测机构：
  1.厚度检测机构按照最新的设备标机设计
  2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
  3.GT头下压过程中顺畅，不卡顿
  4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
  5.厚度检测能覆盖20mm-50mm之间的产品
  6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
  7.测厚压板左右考虑共用，下压时避开产品发生器区域
  8.压板需设计成快换，并带有防错
  9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
  10.压板机构自重掉落过程中顺畅、不卡顿
  11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
归零、标准点检块：
  1.归零块：对产品厚度检测机构GT进行归零
  2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
  3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]


==================================================
SAB-C类详细信息
==================================================

零件组成:
- **Deflector** - 导流片，用于气流导向
- **inflator** - 发生器，气袋充气装置
- **cushion** - 气袋，主要缓冲组件
- **Harness** - 线束，电气连接组件
- **Bracket** - 支架，结构支撑组件
- **Nuts** - 螺母，紧固件
- **soft cover** - 软包布，柔性覆盖材料

工艺流程:
#### ST10 ST10
（一）
1. 工艺过程描述: 1.取发生器。取导流片安装到发生器上
2取线束保护支架安装到发生器上
3.完成后将总成件放入夹具
4.脚踏开关-夹具锁紧总成件
   - 人or设备: 人
   - 产品特性要求: 1.正确的发生器，无漏装，反装，多装
2.正确的导流片，无漏装，反装，多装
3.正确的线束保护支架，无漏装，反装，多装
4.正确总成件无漏装，反装，多装
   - 过程防错要求: 1.人工目视检查
2.人工目视检查
3.人工目视检查
4.1人工目视检查
4.2夹具设计防上下装反，方向装反
4.3传感器检测发生器螺柱到位后夹紧

2. 工艺过程描述: 设备自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 1.条码内容扫描错误，设备报警

3. 工艺过程描述: 1.设备自动检测导流片
2.线束保护支架方向
3.检测完成后，踩脚踏，夹具松开产品，员工接住,夹具下方有防掉落钣金接住。
   - 人or设备: 设备
   - 产品特性要求: 1.正确的导流片，无漏装，反装，多装
2.正确的线束保护支架，无漏装，反装，多装
3.正确总成件无漏装，反装，多装
   - 过程防错要求: 需要通过MSA
1.1导流片上下挂反，扫描仪位置扫描不到，设备不动作
1.2导流片左右挂反，扫描仪位置扫描不到，设备不动作
2.1线束保护支架上下挂反，扫描仪位置扫描不到，设备不动作
2.2线束保护支架左右挂反，扫描仪位置扫描不到，设备不动作
3.1总成件上下挂反，扫描仪位置扫描不到，设备不动作
3.2总成件左右挂反，扫描仪位置扫描不到，设备不动作

4. 工艺过程描述: 1.拿取气袋
2.员工手动将发生器套入气袋
   - 人or设备: 人
   - 产品特性要求: 1.发生器正确的穿入气袋层
2. 正确的气袋方向 
3.气袋无划伤
4.气袋上方无异物掉落
   - 过程防错要求: nan

#### ST20 ST20
（一）
1. 工艺过程描述: 1，拿取组件扫描发生器条码（设备内原有的固定扫码器）
   - 人or设备: 人
   - 产品特性要求: 1.发生器条码无错扫描/漏扫描
   - 过程防错要求: 1.1在光栅内操作，避免扫A做B
1.2发生器漏扫描/扫描错误，设备报警

2. 工艺过程描述: 1，将组件螺柱放置在夹具中 （发生器扫码4s内完成动作）
   - 人or设备: 人
   - 产品特性要求: 1.不伤螺丝
   - 过程防错要求: 沿用9BUX夹具

3. 工艺过程描述: 1.夹紧发生器螺柱
   - 人or设备: 设备
   - 产品特性要求: 1.夹具下部传感器检测螺柱放置到位
2.夹取发生器方向正确
   - 过程防错要求: 沿用9BUX夹具
1.1 需要两个对应位置的传感器检测到位，否则设备报警
1.2 发生器左右挂反，位置错误，扫描仪位置扫描不到，以及对应的螺柱检测传感器检测不到。设备报警

4. 工艺过程描述: 1.扫描气袋条码（设备内原有的固定扫码器）
   - 人or设备: 设备
   - 产品特性要求: 1.气袋条码无漏扫描/错扫描
2.气袋上方无异物掉落
   - 过程防错要求: 1.气袋漏扫描/扫描错误，设备
1.2先扫描发生器，后扫气袋，顺序错误设备报警报警
2.1 气袋上方P1 A级螺母包裹及标识控制.
2.2 人工目视检查

5. 工艺过程描述: 1.将气袋拉直，上夹具夹紧气袋
   - 人or设备: 人
   - 产品特性要求: 1.光滑无毛刺，夹具向上拉直需要左右上下可调
   - 过程防错要求: nan

6. 工艺过程描述: 1.退出光栅，设备自动进行折叠
   - 人or设备: 设备
   - 产品特性要求: 1.正确的折叠方向
2.正确的折叠参数
3.卷折叠时无异物带入
   - 过程防错要求: 1 折叠电机方向控制，反向则气袋无法成卷.
2.1 折叠电机参数控制
2.2 人工皮尺测量.
3.人工目视检查
折叠动作要同步，折叠尺尽量不晃动，折叠尺可换，折叠尺防错

7. 工艺过程描述: 1.将一半气袋塞入软包布中
   - 人or设备: 人
   - 产品特性要求: 1.气袋无褶皱
2.包布无撕裂
3.包布无漏装
   - 过程防错要求: 1.1 折叠电机参数控制
1.2 人工整理及目视检查
2. 人工目视检查
3. 人工目视检查

8. 工艺过程描述: 1.踩脚踏，取下组件，整理好包布，扣入螺柱，送入下一工位
   - 人or设备: 人
   - 产品特性要求: 1.气袋无刮伤
   - 过程防错要求: 1.人工目视检查

#### ST30 ST30
（一）
1. 工艺过程描述: 1.将产品放入夹具
   - 人or设备: 人
   - 产品特性要求: 1.产品左右方向正确
   - 过程防错要求: 1.传感器检测发生器位置

2. 工艺过程描述: 1.扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 1.气袋无漏扫描
   - 过程防错要求: 1.气袋漏扫描，设备不动作

3. 工艺过程描述: 1.装支架
   - 人or设备: 人
   - 产品特性要求: 1.支架无错装，漏装
   - 过程防错要求: 1.夹具上支架检测有无。
2.夹具通过特征定位支架防错装
3.人工目视检查

4. 工艺过程描述: 1.锁定支架
   - 人or设备: 设备
   - 产品特性要求: 1.支架安装到位
2.正确的支架
3.锁定支架不可移动
   - 过程防错要求: 1.夹具上支架检测有无。
2.夹具通过特征定位支架防错装
3.人工目视检查

5. 工艺过程描述: 出螺母
   - 人or设备: 设备
   - 产品特性要求: 1.正确的螺母型号
2.正确的数量
   - 过程防错要求: nan

6. 工艺过程描述: 手动预拧螺母
   - 人or设备: 人
   - 产品特性要求: 1.正确的螺母型号
2.正确的数量
   - 过程防错要求: nan

7. 工艺过程描述: 打螺母
   - 人or设备: 设备
   - 产品特性要求: 1.正确的扭力
2.正确的数量
   - 过程防错要求: 1.电动螺丝枪扭力检测，异常设备报警
2.1 PLC计数功能。每个产品两颗螺母
2.2人工目视检查

8. 工艺过程描述: 拿取产品放入夹具，气缸夹紧螺柱
   - 人or设备: 设备
   - 产品特性要求: 1.20N压力下检测厚度 48±5mm
   - 过程防错要求: 1.气压数据体现压力，气压数据通过PLC判断数据异常设备报警

9. 工艺过程描述: 将产品放入夹具，并将线束插入夹具
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

10. 工艺过程描述: 按按钮，扫描气袋条码，关安全门
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 高度检查
   - 人or设备: 设备
   - 产品特性要求: 正确的模块尺寸
   - 过程防错要求: nan

12. 工艺过程描述: 打印客户标签，正确的条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

13. 工艺过程描述: 黏贴客户标签
   - 人or设备: 设备
   - 产品特性要求: 正确的客户标签内容
   - 过程防错要求: 条码阈值，条码数量，条码角度可控制

14. 工艺过程描述: 按双手按钮，扫描客户标签条码，释放组件，包装
   - 人or设备: nan
   - 产品特性要求: nan
   - 过程防错要求: nan

设备配置:
#### ST10 SAB-C-ST10
一、SAB-发生器、线束预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；
2.设备下方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器标签扫描OK/NG，气袋标签扫描OK/NG，影像OK/NG、E-check检测OK/NG 等10个指示灯
6.相机检测面须配置背景板，防止周围环境影响相机检测
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，无安全风险
10.设备两侧1组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.线束GT测量方式一次测量法：即检测机构基准压头与GT检测头同时接触基准面与测量面，通过一次循环计算线束高度
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的导流片，无漏装，反装，多装
通过设备内部IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.线束安装到位
通过设备GT线束检测机构100%检测线束安装高度，发送给追溯，追溯判断是否在线束高度范围内
4.正确的线束保护支架，无漏装，反装，多装
通过设备内部IV拍照识别导流片安装位置，安装方向
方案1：[具体方案]

二、发生器夹持治具
线束GT检测机构-Fixture 
（一）机械要求:
1. 线束GT检测机构：
1.每次测量检测面位置不变，测量过程中检测头需与检测端面垂直
2.需要有足够的检测行程，GT行程不能顶死，行程在60~80%之间。
3.线束高度检测机构检测时压力最小66N，最大132N，速度不能过快
4.GT放大器安装在机构旁，便于观察
5.基准面与测量面从线束卡扣 到发生器连接器端面（黑色塑料面），测量两者高度差值
6.基准压头与GT顶针不能卡顿，GT顶针检测过程中不能擦伤线束
7.高度检测基准压头到位需有C型传感器感应
8.高度检测头到位后需等待最小250ms，最大500ms后开始检测
发生器夹持治具：
1.左右发生器共用一副治具
2.治具底部增加传感器，检测发生器放置到位。
3.导流片防错依靠设备下方相机防错
4.发生器放入治具定位圆筒内，不晃动
5.治具后侧带有气缸，气缸上带有一对夹爪。发生器放置到位后，两侧夹爪伸出夹紧发生器，发生器不前倾，不歪斜。夹持位置避开发生器焊渣，焊缝。
6.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
7.两侧安装把手，方便拿取
8.治具电气哈丁，快换接头适配原有设备接口

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 SAB-C-ST20
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断气袋是否歪斜
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备前方需要2组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。自动转折机构部分外部使用亚克力板进行物理防护
5.设备卷折需能实现inboard和outboard折叠的需求；折叠首折尺寸和折尺尺寸需要满足图纸的要求;
6.设备增设扫描枪，扫描气袋条码，增加滑轨，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代
12.设备由气袋卷折模组、上气袋夹持模组及产品定位机构3大部分组成
13.气袋卷折模组可实现上下，左右移动，上下移动由伺服实现，伺服行程450mm，极限位置都须有限位开关、机械硬限位及独立零位传感器；横向左右利用气缸完成移动，带有硬限位及缓冲装置；伺服控制旋转气缸从而满足折尺的正反转。
14.上部气袋夹持机构有传感器检测气袋放置到位；内部小气缸夹持气袋，开口大小≤4mm，无夹手风险
15.夹持机构整体上下可通过伺服移动。左右滑轨，可分别单独通过拉拔销进行移动。下方有托板防止异物掉落。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.伺服驱动器需要有STO功能
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.气袋不歪斜：
通过设备上方的相机影像来确保气袋夹持不歪斜
5.正确的折叠
通过预设的折叠参数保证气袋折叠。折叠参数上传追溯控制
方案1：[具体方案]

二、1.产品定位治具
2.折叠尺-Fixture 
（一）机械要求:
1. 产品定位治具：
1.左右共用一副工装，螺柱夹爪材质铜，增加螺纹。发生器螺柱夹持不松脱
2.治具带有发生器放反防错
3.治具设计轻便，不干涉操作
4.治具设计成哈丁接口快换
5.治具与产品接触部分无锐边，毛刺，表面需倒角
6.治具无夹手风险
7.传感器放大器安装位置便于维修调试
8.两侧安装把手，方便拿取
9.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
折叠尺：
1.折尺尺寸根据实际产品图纸制作，表面光滑，无毛刺。折尺滑槽座，滑槽按ALV统一标准设计，由二维码防错，折尺上开凹槽粘贴二维码，避免剐蹭
2.折尺固定滑槽尺寸需与折尺滑槽座尺寸适宜，公差合理，折尺插入后，前端不晃动、歪斜

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST30 SAB-C-ST30
一、SAB-螺母拧紧设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
4.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。
5.设备上有扫描OK/NG、过程OK/NG、光栅OK/NG等给工人提示的状态指示灯，绿色表示OK，红色表示NG。
6.设备配置阿特拉斯手拧螺母枪。螺母枪挂在机械悬臂上，安装位置符合人机工厂，员工操作方便
7.设备配置螺母自动供料器，供料器前端为振动盘，螺母输送至送料轨道上，轨道后端带有分料机构，分料机构处带有吹气机构，通过电磁阀控制吹气，将螺母吹进料管里。后端设有接料盒，料管与接料盒存在一定的高度差，吹至料管里的螺母可自由滑落到接料盒内。
8.设备两侧1组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范
14.一个产品折叠完成后触发一次电磁阀吹出2个螺母。

（三）防错及点检要求:
要求1：1、用接近传感器检测发生器支架并进行防错，接近传感器A+B亮，即左侧支架到位且正确，接近传感器B+C亮，即右侧支架到位且正确
2、背板上气袋区域应有传感器检测气袋有无
3、在扫码开始至螺丝高度检测完成前，接近传感器+气袋传感器自有信号开始，应保证常亮，若出现超过1s的信号断开，应判定为“扫A做B”的情况，需要人为修正异常
4、打螺母顺序需要管控，参照现有产线在检测板上增加传感器管控
方案1：[具体方案]

一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备周边防护板材质要求：PC阳光板，防爆，厚度≥8mm
4.设备配安全升降门，安全门设计为本质安全（两侧滑轮+钢索），手拍启动
5.设备左上方需增加泄气圆孔，直径150mm
6.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的线束电性能
通过E-check电检机构100%在线检测
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
5.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
方案1：[具体方案]

二、拧螺母定位治具-Fixture 
（一）机械要求:
1. 1.左右产品共用一副治具
1.产品竖直放入工装，由定位板支撑；定位板1及定位板2一高一低，用于辅助保证螺母垂直（尺寸待产品图纸更新）
2.定位板1 与 定位板2可以互换，或上下两档位调整，用于适配左右件，且可以识别出各自位置
3.定位销采用圆头，从底板背后安装，用于定位支架的两个安装孔
4.背板上支架区域配吸铁石，辅助支架定位，吸铁石从背部背后安装
5.夹具背板上应预留条码窗口，便于扫码；加工开孔时，四周需要倒角处理，避免剐蹭气袋

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

二、1.产品定位治具
2.E-check检测机构
3.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
  1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
  2.治具托板为平板
  3.治具设计合理，无安全风险
  4.治具设计轻便，整体重量≤7.5Kg
  5.传感器放大器安装位置便于维修调试
  6.传感器检测发生器螺柱，无需夹爪夹持
  7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
  8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
  9.两侧安装把手，方便拿取
  10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
E-check检测机构:
 1.E-check检测模组可整体快换，气电接口使用哈丁。
 2.线束插入检测治具后锁紧，不晃动
 3.探针焊接电线焊点要光滑，不能有虚焊或毛刺，线路应用热缩管隔离，选择镀银线，内径0.5mm² 
 4.气缸传感器检测，需要有到位点和原点监控
 5.电阻仪通讯线必须采用原装屏蔽线通讯，不允许中间 有焊接或转接。
 6.检测机构与电阻仪接线方式为四线法，并联点需尽量接近E-check检测机构，减少内阻
 7.E-check内部结构根据不同线束仿形设计
 8.线束锁紧装置设计时，槽公差+0.05mm，块公差-0.05mm，槽和块硬度处理，表面光滑处理
 9.L型连锁装置，连锁杆必须安装轴套，固定螺丝应采用内六角螺丝加装防松弹片固定
 10.L型解锁装置是线束卡扣解锁机构，与短路片分离器固定座连接（解锁杆必须安装轴套，保证解锁装置顺畅运行。）
 11.轴套：MISUMI标准件
 12.舌片在探针座导向槽里面前后移动，导向槽设计尺寸走上公差+0.02～+0.05，表面粗糙度1.6；舌片尺寸走下公差-0.02～-0.05，表面粗糙度1.6
 13.探针安装孔应保证探针方便安装并固定牢固，保证探针组装后中心与线束PIN在一个中心位置，与线束pin针配合良好
 14.探针孔位设计时参考选择探针型号要求的探针加工孔位（参考华容探针选型手册）
 15.探针座材质：PEI1000。
 16.探针座设计必须有安装定位孔，要保证安装一致性，便于拆装更换
 17.探针座尽量做长贴近产品，以更好的导向舌片。
 18.舌片固定后不应与探针座有干涉，与探针的间隙保证在0.3-0.5mm之间
 19.舌片材质PEI2300，舌片安装孔采用圆孔，可定位，不得调节，保证拆装一致性。
 20.舌片前端厚0.5mm，舌片插入理想深度1.5mm（依照实际产品定义选择） 
 21.舌片宽度大于短路片间距1mm
 22.选用一体式探针，表面镀金。（可参照探针厂商推荐的选型手册或厂家技术支持。推荐华荣一体探针型号：PH-2H PH-3H PH-4H,可根据产品线束PIN的直径选择。）
 23.探针伸出长度应保证与线束pin针有良好的接触，探针压缩行程1/3 - 2/3行程之间。
 24.探针与电阻仪连接线选用0.5mm²的镀银线，线路应用热缩管隔离。
 25.探针选用碗口或九爪型（依照实际产品定义选择） 
 26.卡扣仿形槽尺寸根据具体产品线束结构设计
 27.卡扣仿形槽上下左右工差应控制在单边0.1mm
 28.仿型槽安装位置应有定位孔定位，线束卡扣仿型槽应增加线束卡扣限位
 29.通用底板依据标机设计
厚度GT检测机构：
  1.厚度检测机构按照最新的设备标机设计
  2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
  3.GT头下压过程中顺畅，不卡顿
  4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
  5.厚度检测能覆盖20mm-50mm之间的产品
  6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
  7.测厚压板左右考虑共用，下压时避开产品发生器区域
  8.压板需设计成快换，并带有防错
  9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
  10.压板机构自重掉落过程中顺畅、不卡顿
  11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
归零、标准点检块：
  1.归零块：对产品厚度检测机构GT进行归零
  2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
  3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]


==================================================
SAB-D类详细信息
==================================================

零件组成:
- **Deflector** - 导流片，用于气流导向
- **inflator** - 发生器，气袋充气装置
- **cushion** - 气袋，主要缓冲组件
- **Harness** - 线束，电气连接组件
- **hard cover** - 硬盖，刚性覆盖组件

工艺流程:
#### ST10 ST10
（一）
1. 工艺过程描述: 人手将线束连接器组装到发生器上
   - 人or设备: 人
   - 产品特性要求: 连接器组装到位，连接器锁扣压入到位
   - 过程防错要求: nan

2. 工艺过程描述: 人工将组件放入夹具中，启动设备夹紧发生器，扫发生器标签和线束标签
   - 人or设备: 设备
   - 产品特性要求: 发生器正确地放入治具中
   - 过程防错要求: 发生器放置方向-夹具防错
发生器和线束物料-追溯防错

3. 工艺过程描述: 设备自动检测线束连接器高度
   - 人or设备: 设备
   - 产品特性要求: 正确判断连接器高度
   - 过程防错要求: GT检测线束高度

4. 工艺过程描述: 从治具中取出组件，并将导流片组装到发生器上
   - 人or设备: 人
   - 产品特性要求: 导流片组装方向正确，导流片多装有设计防错，检测导流片凸台位置（参考模块图纸）
   - 过程防错要求: 导流片漏装、装错方向防错

5. 工艺过程描述: 将组件放入导流片检测治具，启动设备，设备自动检测导流片
   - 人or设备: 设备
   - 产品特性要求: 正确判断导流片方向（检测导流片凸点位置）
   - 过程防错要求: 相机检测导流片

6. 工艺过程描述: 从治具中取出组件，拿取气袋，将发生器穿入气袋孔中
   - 人or设备: 人
   - 产品特性要求: 发生器与气袋组装到位
   - 过程防错要求: nan

7. 工艺过程描述: 将组件放入下工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST20 ST20
（一）
1. 工艺过程描述: 员工将组件放入夹具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 夹具自动夹紧螺柱
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 员工将气袋拉直放入上夹头中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 上夹头自动夹紧
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 员工手持线束头，扫描线束条码
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 员工退出安全光栅
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 自动扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: nan

8. 工艺过程描述: 设备自动折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠
   - 过程防错要求: nan

9. 工艺过程描述: 员工将气袋套入包布中
   - 人or设备: 人
   - 产品特性要求: 包布组装正确，Z折位置能在包布开孔处可见
   - 过程防错要求: 下工位相机检查Z折位置

10. 工艺过程描述: 踩脚踏触发
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 松开组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 员工将包布包好
   - 人or设备: nan
   - 产品特性要求: nan
   - 过程防错要求: nan

13. 工艺过程描述: 人手将线束组装到罩盖
   - 人or设备: 人
   - 产品特性要求: 线束扣入罩盖底部的两个卡扣和侧面卡扣，线束黑色圈没有露出罩盖外侧
   - 过程防错要求: 相机检测线束组装的位置

14. 工艺过程描述: 将发生器螺柱组装到罩盖底部的两个孔内，压入到底
   - 人or设备: 人
   - 产品特性要求: 气袋组装到位，没有超出罩盖外沿，发生器螺柱完全装入孔中
   - 过程防错要求: 检测螺柱高度

15. 工艺过程描述: 将组件放入下治具中，并扫码线束标签
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: 夹具仿形防错

16. 工艺过程描述: 拿取上罩盖，将上罩盖放入上治具中
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: 夹具仿形防错

17. 工艺过程描述: 启动设备，设备自动将罩盖压入
   - 人or设备: 设备
   - 产品特性要求: 罩盖压入到位，所有卡扣均组装到位
   - 过程防错要求: 相机检查Z折位置
检测卡扣压入高度

18. 工艺过程描述: 人手粘贴警告标签
   - 人or设备: 人
   - 产品特性要求: 标签粘贴到位
   - 过程防错要求: nan

19. 工艺过程描述: 设备自动扫码警告标签，并自动顶出产品
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

20. 工艺过程描述: 将组件放入下工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST30 ST30
（一）
1. 工艺过程描述: 拿取产品放入夹具，气缸夹紧螺柱
   - 人or设备: 设备
   - 产品特性要求: 1.20N压力下检测厚度 48±5mm
   - 过程防错要求: 1.气压数据体现压力，气压数据通过PLC判断数据异常设备报警

2. 工艺过程描述: 将产品放入夹具，并将线束插入夹具
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 按按钮，扫描气袋条码，关安全门
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 高度检查
   - 人or设备: 设备
   - 产品特性要求: 正确的模块尺寸
   - 过程防错要求: nan

5. 工艺过程描述: 打印客户标签，正确的条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

6. 工艺过程描述: 黏贴客户标签
   - 人or设备: 设备
   - 产品特性要求: 正确的客户标签内容
   - 过程防错要求: 条码阈值，条码数量，条码角度可控制

7. 工艺过程描述: 按双手按钮，扫描客户标签条码，释放组件，包装
   - 人or设备: nan
   - 产品特性要求: nan
   - 过程防错要求: nan

设备配置:
#### ST10 SAB-D-ST10
一、SAB-发生器、线束预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；
2.设备下方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器标签扫描OK/NG，气袋标签扫描OK/NG，影像OK/NG、E-check检测OK/NG 等10个指示灯
6.相机检测面须配置背景板，防止周围环境影响相机检测
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，无安全风险
10.设备两侧1组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.线束GT测量方式一次测量法：即检测机构基准压头与GT检测头同时接触基准面与测量面，通过一次循环计算线束高度
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的导流片，无漏装，反装，多装
通过设备内部IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.线束安装到位
通过设备GT线束检测机构100%检测线束安装高度，发送给追溯，追溯判断是否在线束高度范围内
4.正确的线束保护支架，无漏装，反装，多装
通过设备内部IV拍照识别导流片安装位置，安装方向
方案1：[具体方案]

二、发生器夹持治具
线束GT检测机构-Fixture 
（一）机械要求:
1. 线束GT检测机构：
1.每次测量检测面位置不变，测量过程中检测头需与检测端面垂直
2.需要有足够的检测行程，GT行程不能顶死，行程在60~80%之间。
3.线束高度检测机构检测时压力最小66N，最大132N，速度不能过快
4.GT放大器安装在机构旁，便于观察
5.基准面与测量面从线束卡扣 到发生器连接器端面（黑色塑料面），测量两者高度差值
6.基准压头与GT顶针不能卡顿，GT顶针检测过程中不能擦伤线束
7.高度检测基准压头到位需有C型传感器感应
8.高度检测头到位后需等待最小250ms，最大500ms后开始检测
发生器夹持治具：
1.左右发生器共用一副治具
2.治具底部增加传感器，检测发生器放置到位。
3.导流片防错依靠设备下方相机防错
4.发生器放入治具定位圆筒内，不晃动
5.治具后侧带有气缸，气缸上带有一对夹爪。发生器放置到位后，两侧夹爪伸出夹紧发生器，发生器不前倾，不歪斜。夹持位置避开发生器焊渣，焊缝。
6.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
7.两侧安装把手，方便拿取
8.治具电气哈丁，快换接头适配原有设备接口

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 SAB-D-ST20
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断气袋是否歪斜
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备前方需要2组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。自动转折机构部分外部使用亚克力板进行物理防护
5.设备卷折需能实现inboard和outboard折叠的需求；折叠首折尺寸和折尺尺寸需要满足图纸的要求;
6.设备增设扫描枪，扫描气袋条码，增加滑轨，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代
12.设备由气袋卷折模组、上气袋夹持模组及产品定位机构3大部分组成
13.气袋卷折模组可实现上下，左右移动，上下移动由伺服实现，伺服行程450mm，极限位置都须有限位开关、机械硬限位及独立零位传感器；横向左右利用气缸完成移动，带有硬限位及缓冲装置；伺服控制旋转气缸从而满足折尺的正反转。
14.上部气袋夹持机构有传感器检测气袋放置到位；内部小气缸夹持气袋，开口大小≤4mm，无夹手风险
15.夹持机构整体上下可通过伺服移动。左右滑轨，可分别单独通过拉拔销进行移动。下方有托板防止异物掉落。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.伺服驱动器需要有STO功能
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.气袋不歪斜：
通过设备上方的相机影像来确保气袋夹持不歪斜
5.正确的折叠
通过预设的折叠参数保证气袋折叠。折叠参数上传追溯控制
方案1：[具体方案]

一、SAB-罩盖压合设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方配置扫码枪，扫线束、气袋及壳体上警示标签。下方安装托板，防止异物掉落
3.设备前方需配1组安全光栅，光栅需接入安全回路进行控制；光栅离夹点风险点距离≥200mm
4.设备需满足上、下分体式压合罩盖的要求;
5.人机屏样式参照Autoliv最新标准（Autoliv提供人机屏3D数模）。
6.设备下方空间利用，放置罩盖压合治具，设备有足够柔性，预留铰链式罩壳压合机构空间。
7.罩盖压合治具平台设计合理，强度需保证。
8.快换夹具气电接口选用Harting HAN-Modular 气电一体式接插件，传感器线需要经集线器再接入接插件针脚，不能直接接入接插件针脚。
9.预留辅助压杆气缸机构空间孔位
10.设备顶上带有蜂鸣器的红黄绿三色灯
11.设备配有2个智能相机，#1相机固定于设备底部，检测下罩壳内气袋组件线束卡扣到位，#2相机安装于手移滑台导轨机构上，可手动左右切换、移动位置，检测气袋包布内Z折墨点有无以及检测线束黑丝胶圈是否露出下罩壳
12.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足；配线都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，建议在发热较高的元器件周围布置散热风扇；电箱柜内最少需要预留30%空间。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的线束卡扣位置
通过相机拍照识别线束无黑色圈露出保证正确的正确的线束卡扣位置
2.正确的发生器螺柱露出高度
通过C型感应光电竖直安装距离等于图纸上定义的螺柱高度保证正确的发生器螺柱露出高度
3.正确的放置上、下罩盖壳体
通过上下夹具仿形保证正确的放置上、下罩盖壳体
4.正确的Z折尺寸
通过相机拍照对比漏出墨点保证正确的正确的Z折尺寸
5.卡扣按压到位
通过C型光电感应卡扣按压伸缩棒信号保证卡扣按压到位
6.正确的警示标签
通过扫警示标签内容进追溯系统对比保证正确的警示标签
7.正确的螺柱露出高度
通过C型光电安装高度遵循产品图纸保证正确的螺柱露出高度
8.正确的推卡扣机构
通过手持式追溯扫码枪扫机构上二维码进行防错电检保证正确的推卡扣及压持机构
9.正确的夹具工装
通过HARTING接插件针脚识别保证正确的夹具工装
方案1：[具体方案]

二、1.产品定位治具
2.折叠尺-Fixture 
（一）机械要求:
1. 产品定位治具：
1.左右共用一副工装，螺柱夹爪材质铜，增加螺纹。发生器螺柱夹持不松脱
2.治具带有发生器放反防错
3.治具设计轻便，不干涉操作
4.治具设计成哈丁接口快换
5.治具与产品接触部分无锐边，毛刺，表面需倒角
6.治具无夹手风险
7.传感器放大器安装位置便于维修调试
8.两侧安装把手，方便拿取
9.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
折叠尺：
1.折尺尺寸根据实际产品图纸制作，表面光滑，无毛刺。折尺滑槽座，滑槽按ALV统一标准设计，由二维码防错，折尺上开凹槽粘贴二维码，避免剐蹭
2.折尺固定滑槽尺寸需与折尺滑槽座尺寸适宜，公差合理，折尺插入后，前端不晃动、歪斜

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

二、罩盖压合治具-Fixture 
（一）机械要求:
1. 1.设备上、下工装根据产品罩盖外轮廓仿形设计、上下工装内各有2传感器感应罩壳存在。
2.上、下工装治具附有吸盘，装配在治具上，吸盘位置根据产品尺寸大小排布，上下工装吸盘负压值均有负压表进行监控。
3.上、下工装满足快换设计，有电路气路连接需求的快换接头需使用Harting HAN-Modular 气电一体式接插件。
4.上压合工装有定位销保证产品上模外轮廓尺寸，下压合工装有定位销保证产品下模外轮廓尺寸。
5.上、下压合工装内都需设计有卡扣导向限位气缸，对上、下罩盖卡扣进行内限位。
6.下罩盖四周应设计有推卡扣气缸机构，在上罩盖下压至预压合位置时，对上罩盖的卡扣起到导向作用，推卡扣机构设计为快换。
7.下工装治具对产品卡扣有避让，并带有卡扣到位检测，每个卡扣都使用1个C型光电检测卡扣卡到位，产品压合到位后不能使产品卡扣受力变形。
8.下罩盖工装应包含一夹紧气缸，夹紧气袋发生器组件，工装内各有一C型光电检测螺柱，C型光电安装位置应更遵循产品图纸定义。
9.下模工装底部设计一顶升气缸，用于罩盖压合结束后，气缸将产品顶出。
10.需制作一罩盖卡扣组装高度不良点检件用于点检卡扣C型光电ON/OFF，不良尺寸定义由奥托立夫提供。

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST30 SAB-D-ST30
一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备周边防护板材质要求：PC阳光板，防爆，厚度≥8mm
4.设备配安全升降门，安全门设计为本质安全（两侧滑轮+钢索），手拍启动
5.设备左上方需增加泄气圆孔，直径150mm
6.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的线束电性能
通过E-check电检机构100%在线检测
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
5.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
方案1：[具体方案]

二、1.产品定位治具
2.E-check检测机构
3.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
  1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
  2.治具托板为平板
  3.治具设计合理，无安全风险
  4.治具设计轻便，整体重量≤7.5Kg
  5.传感器放大器安装位置便于维修调试
  6.传感器检测发生器螺柱，无需夹爪夹持
  7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
  8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
  9.两侧安装把手，方便拿取
  10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
E-check检测机构:
 1.E-check检测模组可整体快换，气电接口使用哈丁。
 2.线束插入检测治具后锁紧，不晃动
 3.探针焊接电线焊点要光滑，不能有虚焊或毛刺，线路应用热缩管隔离，选择镀银线，内径0.5mm² 
 4.气缸传感器检测，需要有到位点和原点监控
 5.电阻仪通讯线必须采用原装屏蔽线通讯，不允许中间 有焊接或转接。
 6.检测机构与电阻仪接线方式为四线法，并联点需尽量接近E-check检测机构，减少内阻
 7.E-check内部结构根据不同线束仿形设计
 8.线束锁紧装置设计时，槽公差+0.05mm，块公差-0.05mm，槽和块硬度处理，表面光滑处理
 9.L型连锁装置，连锁杆必须安装轴套，固定螺丝应采用内六角螺丝加装防松弹片固定
 10.L型解锁装置是线束卡扣解锁机构，与短路片分离器固定座连接（解锁杆必须安装轴套，保证解锁装置顺畅运行。）
 11.轴套：MISUMI标准件
 12.舌片在探针座导向槽里面前后移动，导向槽设计尺寸走上公差+0.02～+0.05，表面粗糙度1.6；舌片尺寸走下公差-0.02～-0.05，表面粗糙度1.6
 13.探针安装孔应保证探针方便安装并固定牢固，保证探针组装后中心与线束PIN在一个中心位置，与线束pin针配合良好
 14.探针孔位设计时参考选择探针型号要求的探针加工孔位（参考华容探针选型手册）
 15.探针座材质：PEI1000。
 16.探针座设计必须有安装定位孔，要保证安装一致性，便于拆装更换
 17.探针座尽量做长贴近产品，以更好的导向舌片。
 18.舌片固定后不应与探针座有干涉，与探针的间隙保证在0.3-0.5mm之间
 19.舌片材质PEI2300，舌片安装孔采用圆孔，可定位，不得调节，保证拆装一致性。
 20.舌片前端厚0.5mm，舌片插入理想深度1.5mm（依照实际产品定义选择） 
 21.舌片宽度大于短路片间距1mm
 22.选用一体式探针，表面镀金。（可参照探针厂商推荐的选型手册或厂家技术支持。推荐华荣一体探针型号：PH-2H PH-3H PH-4H,可根据产品线束PIN的直径选择。）
 23.探针伸出长度应保证与线束pin针有良好的接触，探针压缩行程1/3 - 2/3行程之间。
 24.探针与电阻仪连接线选用0.5mm²的镀银线，线路应用热缩管隔离。
 25.探针选用碗口或九爪型（依照实际产品定义选择） 
 26.卡扣仿形槽尺寸根据具体产品线束结构设计
 27.卡扣仿形槽上下左右工差应控制在单边0.1mm
 28.仿型槽安装位置应有定位孔定位，线束卡扣仿型槽应增加线束卡扣限位
 29.通用底板依据标机设计
厚度GT检测机构：
  1.厚度检测机构按照最新的设备标机设计
  2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
  3.GT头下压过程中顺畅，不卡顿
  4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
  5.厚度检测能覆盖20mm-50mm之间的产品
  6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
  7.测厚压板左右考虑共用，下压时避开产品发生器区域
  8.压板需设计成快换，并带有防错
  9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
  10.压板机构自重掉落过程中顺畅、不卡顿
  11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
归零、标准点检块：
  1.归零块：对产品厚度检测机构GT进行归零
  2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
  3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]


==================================================
SAB-E类详细信息
==================================================

零件组成:
- **Deflector** - 导流片，用于气流导向
- **inflator** - 发生器，气袋充气装置
- **cushion** - 气袋，主要缓冲组件
- **Bracket** - 支架，结构支撑组件
- **Nuts** - 螺母，紧固件
- **housing** - 外壳，保护性外罩

工艺流程:
#### ST10 ST10
（一）
1. 工艺过程描述: 将发生器放在夹具上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器
   - 过程防错要求: 发生器校验位比对

3. 工艺过程描述: 发生器称重检测
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器重量
   - 过程防错要求: nan

4. 工艺过程描述: 预装支架/发生器/气袋/卡环
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: 二级零件扫描

5. 工艺过程描述: 扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: FIFO

6. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: 气袋校验位比对

7. 工艺过程描述: 将预装好的组件放入夹具
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 锁定支架位置

8. 工艺过程描述: 发生器角度定位
   - 人or设备: 设备
   - 产品特性要求: 发生器,支架,气袋,卡环的相对位置正确
正确的发生器方向
正确的发生器角度
（发生器角度上下活动量在图纸范围内）
   - 过程防错要求: nan

9. 工艺过程描述: 夹紧组件
   - 人or设备: 设备
   - 产品特性要求: 1.气袋&支架位置正确
2.发生器正确插入气袋(上下Taco）
3.发生器支架位置锁定
4.发生器前后位置锁定
4.卡环位置正确
   - 过程防错要求: nan

10. 工艺过程描述: 锁紧2个卡环
   - 人or设备: 设备
   - 产品特性要求: 打点深度2.2-2.4 mm
buckle打点位置0-5°
Buckle 位置/方向符合图纸要求
卡环位置符合图纸要求
   - 过程防错要求: Bandit 过程中卡环不可有干涉
Bandit 过程中枪体接触产品

符合MPS067相关要求

11. 工艺过程描述: 产品组件取至下一工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST20 ST20
（一）
1. 工艺过程描述: 组件与罩盖预装
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 固定组件
   - 人or设备: 设备
   - 产品特性要求: 发生器支架安装正确
（卡入卡扣卡槽）
   - 过程防错要求: nan

3. 工艺过程描述: 气袋+1折叠尺插入气袋
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 气袋+1折叠尺插入位置正确

4. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 先进先出

5. 工艺过程描述: 上气袋插入夹子,整理气袋
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 正确的气袋夹持位置

6. 工艺过程描述: 自动折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠方向
正确的折叠尺
起始折叠位置正确
折叠符合PID 图纸
折叠无松散
   - 过程防错要求: nan

7. 工艺过程描述: 罩盖闭合
   - 人or设备: 设备
   - 产品特性要求: 卡扣正确压入卡槽并完全露出
无夹气袋
   - 过程防错要求: nan

8. 工艺过程描述: 自动扫描罩盖条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 校验位比对

9. 工艺过程描述: 释放组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST30 ST30
（一）
1. 工艺过程描述: 将罩盖放入夹具，并固定
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 扫描罩盖条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 移动E-check 探头并锁住发生器
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 设备检测需求满足MPS128

4. 工艺过程描述: 按按钮设备自动检测（电阻&厚度）
   - 人or设备: 设备
   - 产品特性要求: 正确的模块厚度和电阻
   - 过程防错要求: nan

5. 工艺过程描述: 打印客户标签
   - 人or设备: 设备
   - 产品特性要求: 正确的客户标签内容，方向，位置
   - 过程防错要求: nan

6. 工艺过程描述: 黏贴客户标签
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

7. 工艺过程描述: 扫描客户标签条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: 客户标签角度检测

8. 工艺过程描述: 释放组件
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

设备配置:
#### ST10 SAB-E-ST10
一、SAB-发生器预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器扫描OK/NG，气袋扫描OK/NG，影像OK/NG 等指示灯
6.发生器夹持治具下方台面配置黑色背景板
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
10.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.导流片安装方向正确、导流片无错装、漏装
导流片通过上方IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.无扫A做B
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、发生器、气袋预装治具-Fixture 
（一）机械要求:
1. 1.发生器平放插入治具内，以发生器塑料端面为基准，治具内带有传感器检测放置到位。
2.治具夹持不能对发生器端面造成磕碰损伤
3.治具上配置发生器螺柱导向功能，原位伸出，发生器夹持后缩回
4.治具松开，发生器不会掉落
5.治具夹紧发生器后不晃动、松动，治具夹持后间隙需小于发生器直径，夹持长度至少要15mm
6.发生器夹持治具整体设计哈丁成快换形式，到位后气缸自锁。
7.治具于产品接触部分无锐边，毛刺，表面需倒角
8.治具无夹手风险
9.治具带有防护罩，防止螺丝掉落

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 SAB-E-ST20
一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照Hard Cover folding标机制作
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备整体框架有如下4个部分组成；
  3.1 左、右气袋夹紧机构；
  3.2 左、右气袋折叠机构；
  3.3 左、右加一折尺机构；工装底座及周围附件；
  3.4 快换工装、折尺、加一折尺、卡扣外导向、卡扣压尺、气袋整理、防气袋抽出；
4.电气箱分上、下部分，下电气箱旁放置气源处理件及阀岛；
5.脚踏开关安装到设备下方，外缘露出型材2公分；
6.左、右两侧X向伺服模组形成不小于500mm；
7.左、右两侧X向伺服行程不能一样，需保证单个夹头能移动到中间位置；
8.模组选型尽量选用封闭式模组，最大程度上减少A级螺母；
9.整个机构需优化设计要考虑减少螺母使用量；
10.折尺快换机构采用Autoliv标准快换结构；
11.左、右两侧X向伺服行程不小于260mm，Z向行程不小于450mm；
12.工装底座角度调节范围不小于±10°；
13.工装底座前后伺服调节范围不小于±50mm；
14.夹具合模伺服输出扭矩不小于150NM；
15.配置1个设备状态指示灯盒
16.设备上方增设相机，检测气袋折叠状态
16.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关
17.设备顶上带有蜂鸣器的红黄绿三色灯
18.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
19.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.正确的加一折尺位置：
通过设备相机影像，检测气袋位置及气袋与加一折尺位置
5.气袋不歪斜：
通过设备下方的相机影像来确保气袋夹持不歪斜
方案1：[具体方案]

二、折尺、罩盖压合治具-Fixture 
（一）机械要求:
1. 1.罩盖压装夹具要求方便拆卸，可以快速换型，换型时扫码防错；
2.折尺尺寸符合图纸要求，可快速换型，换型时扫码防错；
3.+1折叠尺位置正确，+1折叠尺可以快速换型，换型时扫码防错；
4.传感器检查罩盖安装到位，吸盘能有效吸附罩盖；
5.折叠完气袋固定机构能有效固定气袋，固定机构可以快速换型，换型时扫码防错；
6.螺柱夹持机构能有效夹持螺柱，不损坏螺柱，传感器能够检测螺柱到位；
7.每一个卡脚处需要一个卡脚导向机构，导向机构可以快速换型，换型时扫码防错；
8.气袋折叠后，须有辅助整理气袋机构，防止夹气袋，整理机构可以快速换型，换型时扫码防错；
9.传感器检查每个卡脚卡扣到位；
10.传感器安装位置需合理，能够检测罩盖安装到位，安装紧固不易松动；
11.吸盘的数量和位置需结合罩盖形状进行设计，要求能够有效吸附罩盖，起到固定壳体作用（需提供备件），吸盘具有负压检测功能；
12.发生器螺柱夹持机构需能有效夹持住螺柱，保证螺柱不松脱，不能损伤螺柱的螺牙（需提供备件）；
13.螺柱检测到位要求螺柱高度在≥13.5mm时检测到信号，检测机构高度位置调整好以后要求打定位销固定；
14.要求罩壳及卡扣有稳定、可靠的定位和夹紧，保证合模后卡扣准确、完好的卡入卡槽内；
15.罩盖翻转侧的夹具上需有定位柱定位罩盖，罩盖卡扣内侧和外侧都需要有定位柱，定位柱数量和形状按照卡扣数量和形状设计，不能损伤罩盖定位孔和撕裂线，定位柱不易松脱，掉落，光滑，不尖锐，不能损伤气袋，定位柱能够伸缩；
16.安装发生器侧的罩盖定位柱要求能够伸缩，定位柱须在罩盖内测，定位柱数量和形状按照罩盖开孔数量和形状设计，不能损伤罩盖定位孔和撕裂线，定位柱不易松脱，掉落，光滑，不尖锐，不能损伤气袋；（保留设计，现场调试确有需求再加上）
17.罩盖上、下模设计需考虑减重，在不影响定位和整体强度条件下开减重孔；
18.翻转侧的夹具要求设计成可以左右调节，旋转链接轴要求耐磨损；
19.加一折尺位置尺寸需满足折叠图纸要求；
20.+1折尺表面光滑，无毛刺，+1折尺方便快换，切换产品时通过扫描折尺上二维码进行防错；
21.折叠时须有视觉检测+1折叠尺位置正确；
22.罩盖上每一个卡脚处都需要有外导向块，导向块要求能够前后调节，气缸控制导向块伸出，缩回，导向块方便快换，换型时通过扫描导向块上二维码进行防错；
23.压板压住折叠完的气袋，保证气袋不松散；
24.压板表面光滑，无毛刺，不能损伤气袋；
25.压板要求能够前后调节，不易变形，断裂；
26.气袋整理机构需安装在罩盖小头处，将外漏的气袋推回罩盖内，防止罩盖扣合时气袋背带被夹；
27.整理机构上推板表面光滑，无毛刺，不能损伤气袋，能够前后调节，不易变形，断裂；
28.压板和整理机构方便快换，换型时通过扫描压板和整理机构上二维码进行防错；
29.传感器检测每个罩盖卡脚安装状态，能够识别卡脚安装异常，导杆头部形状需完全覆盖卡脚端面，保证检测可靠；
30.检测头上下伸缩要求防转，上下伸缩顺滑不卡顿；
31.要求每个卡扣到位单独检测；

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
2.折尺、加一折尺二维码防错
方案1：[具体方案]

#### ST30 SAB-E-ST30
一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
4.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
5.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
6.设备顶上带有蜂鸣器的红黄绿三色灯
7.设备设计合理，尺寸紧凑，空间无浪费
8.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
方案1：[具体方案]

二、1.产品定位治具
2.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
2.治具托板为平板
3.治具设计合理，无安全风险
4.治具设计轻便，整体重量≤7.5Kg
5.传感器放大器安装位置便于维修调试
6.传感器检测发生器螺柱，无需夹爪夹持
7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
9.两侧安装把手，方便拿取
10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
厚度GT检测机构：
1.厚度检测机构按照最新的设备标机设计
2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
3.GT头下压过程中顺畅，不卡顿
4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
5.厚度检测能覆盖20mm-50mm之间的产品
6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
7.测厚压板左右考虑共用，下压时避开产品发生器区域
8.压板需设计成快换，并带有防错
9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
10.压板机构自重掉落过程中顺畅、不卡顿
11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
归零、标准点检块：
1.归零块：对产品厚度检测机构GT进行归零
2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]


==================================================
SAB-F类详细信息
==================================================

零件组成:
- **Deflector** - 导流片，用于气流导向
- **inflator** - 发生器，气袋充气装置
- **cushion** - 气袋，主要缓冲组件
- **hard cover** - 硬盖，刚性覆盖组件
- **housing** - 外壳，保护性外罩
- **3D heat** - 3D加热元件，温控组件

工艺流程:
#### ST10 ST10
（一）
1. 工艺过程描述: 将发生器放入夹具，设备自动夹紧发生器
   - 人or设备: 人
   - 产品特性要求: 正确的发生器
   - 过程防错要求: 无夹伤，发生器正确，发生器插入方向正确，发生器螺柱方向正确

2. 工艺过程描述: 设备自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 拿取导流片，安装到发生器上，踩脚踏，检测导流片方向
   - 人or设备: 设备
   - 产品特性要求: 1. 导流片无漏装,错装
2.正确安装方向
   - 过程防错要求: 需要通过MSA

4. 工艺过程描述: 拿取气袋，手工将其安装到发生器上
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

5. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: nan

6. 工艺过程描述: 夹具打开，拿出组件，送至下一工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST20 ST20
（一）
1. 工艺过程描述: 将产品放入夹具，并夹紧
   - 人or设备: 设备
   - 产品特性要求: 发生器装入气袋方向正确
   - 过程防错要求: nan

2. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 出螺母
   - 人or设备: 设备
   - 产品特性要求: 正确的螺母型号和数量
   - 过程防错要求: 螺母计数功能

4. 工艺过程描述: 打螺母
   - 人or设备: 设备
   - 产品特性要求: 正确的扭力，正确的顺序，正确的数量
   - 过程防错要求: nan

5. 工艺过程描述: 检测螺母高度
   - 人or设备: 设备
   - 产品特性要求: 正确的螺母高度
   - 过程防错要求: nan

6. 工艺过程描述: 拿取组件，将其固定在夹具中
   - 人or设备: 人
   - 产品特性要求: 螺柱无损伤
   - 过程防错要求: 1.发生器条码扫描错误，设备报警
2.下工站扫描，设备报警
3.扫描后不能离开光栅，离开光栅，设备报警

7. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋
   - 过程防错要求: 光滑无毛刺，向上拉直需要左右上下可调

8. 工艺过程描述: 将气袋拉直，上夹具夹紧气袋
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

9. 工艺过程描述: 退出光栅，设备自动进行折叠
   - 人or设备: 设备
   - 产品特性要求: 正确的折叠方式，气袋无破损，无异物
   - 过程防错要求: nan

10. 工艺过程描述: 将一半气袋塞入软包布中
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

11. 工艺过程描述: 踩脚踏，取下组件，整理好包布，扣入螺柱，送入下一工位
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

12. 工艺过程描述: 将组件螺柱插入机器手夹具内夹紧
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: 机械手螺柱夹紧牢固

13. 工艺过程描述: 扫描仪自动扫描气袋条码，相机拍照检测包布安装方向
   - 人or设备: 设备
   - 产品特性要求: 包布安装方向正确
   - 过程防错要求: 相机拍照防错

14. 工艺过程描述: 按双手按钮，设备开始热压
   - 人or设备: 设备
   - 产品特性要求: 产品热压形状，尺寸符合图纸要求
   - 过程防错要求: nan

15. 工艺过程描述: 热压完成，自动送出产品
   - 人or设备: 设备
   - 产品特性要求: nan
   - 过程防错要求: nan

#### ST50 ST50
（一）
1. 工艺过程描述: 将罩盖放入夹具
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

2. 工艺过程描述: 取发生器组件按入壳体螺柱孔，设备夹紧螺柱
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

3. 工艺过程描述: 将线束卡入罩盖开口处
   - 人or设备: 人
   - 产品特性要求: nan
   - 过程防错要求: nan

4. 工艺过程描述: 将罩盖压合
   - 人or设备: 人
   - 产品特性要求: 卡扣正确压入卡槽并完全露出
无夹气袋
   - 过程防错要求: nan

5. 工艺过程描述: 夹具松开，取出组件，送到下一工位
   - 人or设备: nan
   - 产品特性要求: nan
   - 过程防错要求: nan

设备配置:
#### ST10 SAB-F-ST10
一、SAB-发生器预装设备-Equipment 
（一）机械要求:
1. 1.设备整体框架由铝型材组成，底下装福马轮；电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；人机屏安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断导流片安装
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备增设扫描枪，扫描发生器、气袋条码
5.配置1个设备状态指示灯盒，有过程OK/NG，发生器扫描OK/NG，气袋扫描OK/NG，影像OK/NG 等指示灯
6.发生器夹持治具下方台面配置黑色背景板
7.设备内部有独立照明灯，照明灯可通过HMI里的按钮来开关；外部光源无法影响内部相机检测
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
10.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.导流片安装方向正确、导流片无错装、漏装
导流片通过上方IV拍照识别导流片安装位置，安装方向
2.正确的发生器、气袋
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断当前使用的物件是否是正确的
3.无扫A做B
通过扫描枪扫描发生器、气袋条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、发生器、气袋预装治具-Fixture 
（一）机械要求:
1. 1.发生器平放插入治具内，以发生器塑料端面为基准，治具内带有传感器检测放置到位。
2.治具夹持不能对发生器端面造成磕碰损伤
3.治具上配置发生器螺柱导向功能，原位伸出，发生器夹持后缩回
4.治具松开，发生器不会掉落
5.治具夹紧发生器后不晃动、松动，治具夹持后间隙需小于发生器直径，夹持长度至少要15mm
6.发生器夹持治具整体设计哈丁成快换形式，到位后气缸自锁。
7.治具于产品接触部分无锐边，毛刺，表面需倒角
8.治具无夹手风险
9.治具带有防护罩，防止螺丝掉落

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：1.治具通过哈丁接口防错
方案1：[具体方案]

#### ST20 SAB-F-ST20
一、SAB-螺母拧紧设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
4.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。
5.设备上有扫描OK/NG、过程OK/NG、光栅OK/NG等给工人提示的状态指示灯，绿色表示OK，红色表示NG。
6.设备配置阿特拉斯手拧螺母枪。螺母枪挂在机械悬臂上，安装位置符合人机工厂，员工操作方便
7.设备配置螺母自动供料器，供料器前端为振动盘，螺母输送至送料轨道上，轨道后端带有分料机构，分料机构处带有吹气机构，通过电磁阀控制吹气，将螺母吹进料管里。后端设有接料盒，料管与接料盒存在一定的高度差，吹至料管里的螺母可自由滑落到接料盒内。
8.设备两侧1组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范
14.一个产品折叠完成后触发一次电磁阀吹出2个螺母。

（三）防错及点检要求:
要求1：1、用接近传感器检测发生器支架并进行防错，接近传感器A+B亮，即左侧支架到位且正确，接近传感器B+C亮，即右侧支架到位且正确
2、背板上气袋区域应有传感器检测气袋有无
3、在扫码开始至螺丝高度检测完成前，接近传感器+气袋传感器自有信号开始，应保证常亮，若出现超过1s的信号断开，应判定为“扫A做B”的情况，需要人为修正异常
4、打螺母顺序需要管控，参照现有产线在检测板上增加传感器管控
方案1：[具体方案]

一、SAB-气袋折叠设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方增设相机，判断气袋是否歪斜
3.相机安装稳固、不晃动，安装防护罩避免碰撞。固定后需要做防松处理并在螺母上画白线标记
4.设备前方需要2组安全光栅，硬件要接入安全回路进行控制；光栅离夹手点距离≥200mm。自动转折机构部分外部使用亚克力板进行物理防护
5.设备卷折需能实现inboard和outboard折叠的需求；折叠首折尺寸和折尺尺寸需要满足图纸的要求;
6.设备增设扫描枪，扫描气袋条码，增加滑轨，可左右平移。下方安装托板，防止异物掉落
7.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
8.设备顶上带有蜂鸣器的红黄绿三色灯
9.设备设计合理，尺寸紧凑，空间无浪费
10.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
11.产品上方机构螺母需异物管理，无掉落风险，上方线束轧带用波纹管缠绕替代
12.设备由气袋卷折模组、上气袋夹持模组及产品定位机构3大部分组成
13.气袋卷折模组可实现上下，左右移动，上下移动由伺服实现，伺服行程450mm，极限位置都须有限位开关、机械硬限位及独立零位传感器；横向左右利用气缸完成移动，带有硬限位及缓冲装置；伺服控制旋转气缸从而满足折尺的正反转。
14.上部气袋夹持机构有传感器检测气袋放置到位；内部小气缸夹持气袋，开口大小≤4mm，无夹手风险
15.夹持机构整体上下可通过伺服移动。左右滑轨，可分别单独通过拉拔销进行移动。下方有托板防止异物掉落。

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.伺服驱动器需要有STO功能
16.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的气袋：
通过设备固定扫描器扫描气袋条码，PLC发送条码到追溯，追溯比对判断是否使用正确的气袋
2.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
3.正确的折尺
扫描折尺上的二维码，发送给PLC，PLC判断折尺是否是当前正确的折尺
4.气袋不歪斜：
通过设备上方的相机影像来确保气袋夹持不歪斜
5.正确的折叠
通过预设的折叠参数保证气袋折叠。折叠参数上传追溯控制
方案1：[具体方案]

一、SAB-热压设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.上料口增加安全光栅及安全升降门，生产时安全门下降，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
3.设备内部需预留维修空间，在上料口右侧增加维修安全门
4.下料口增加滑道，机器人移动至下料区域松开，产品通过滑道滑入下料口
5.设备下方增加不良品红箱，入口增加传感器并与程序联动
6.在设备热压整形机构两侧增加滑道，热压模具通过滑道推入治具安装位置。到位后两侧气缸伸出对模具限位，下方带有定位销的气缸抬起对模组定位。
7.产品通过机器人在工站内自动传递，产品从加热炉中加热取出到放入整形模组内节拍最快3s，最慢不能超过6s
8.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG，影像OK/NG 等指示灯
9.设备顶上带有蜂鸣器的红黄绿三色灯
10.机器人选用发那科，并带软浮动（机器人型号：LRM200iD/7L），机器人能自动回原
11.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险
12.机器人夹持产品，放入加热炉，加热完成后放入整形模具直至整个热压过程完成并下料，整个过程机器人保持夹持产品状态
13.推荐使用伺服压机来控制整形距离、接近速度和压力（品牌LIND  型号LDE125-300-C-L10-D-FA-A-ZO2KW），整形压机机架与压机气缸需要满足35.6KN（8000Lbs）的压力
14.使用国产加热炉对产品进行加热，加热炉内部顶部与底部各配置一个红外线温度传感器，监控加热区域
15.产品通过在加热炉内加热对其表面毛毡软化，加热炉内部加热分4区域控制
16.加热炉内部顶部与底部各配置一个红外线温度传感器，监控加热区域

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
方案1：[具体方案]

二、拧螺母定位治具-Fixture 
（一）机械要求:
1. 1.左右产品共用一副治具
1.产品竖直放入工装，由定位板支撑；定位板1及定位板2一高一低，用于辅助保证螺母垂直（尺寸待产品图纸更新）
2.定位板1 与 定位板2可以互换，或上下两档位调整，用于适配左右件，且可以识别出各自位置
3.定位销采用圆头，从底板背后安装，用于定位支架的两个安装孔
4.背板上支架区域配吸铁石，辅助支架定位，吸铁石从背部背后安装
5.夹具背板上应预留条码窗口，便于扫码；加工开孔时，四周需要倒角处理，避免剐蹭气袋

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

二、1.产品定位治具
2.折叠尺-Fixture 
（一）机械要求:
1. 产品定位治具：
1.左右共用一副工装，螺柱夹爪材质铜，增加螺纹。发生器螺柱夹持不松脱
2.治具带有发生器放反防错
3.治具设计轻便，不干涉操作
4.治具设计成哈丁接口快换
5.治具与产品接触部分无锐边，毛刺，表面需倒角
6.治具无夹手风险
7.传感器放大器安装位置便于维修调试
8.两侧安装把手，方便拿取
9.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
折叠尺：
1.折尺尺寸根据实际产品图纸制作，表面光滑，无毛刺。折尺滑槽座，滑槽按ALV统一标准设计，由二维码防错，折尺上开凹槽粘贴二维码，避免剐蹭
2.折尺固定滑槽尺寸需与折尺滑槽座尺寸适宜，公差合理，折尺插入后，前端不晃动、歪斜

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

二、1.热压模具
2.发生器夹持机构-Fixture 
（一）机械要求:
1. 热压摸具：
1.整形模具内腔仿型设计，上模型腔设计需避开发生器部分
2.整形模芯建议设计成分段式，并配置镶块。根据调试结果进行调整
3.上下模分体式建议分3段分体。前部发生器、气袋两部分做成分体式，后部折弯根据数模从开始翘起起点开始做分体
4.模具后方两侧需增加气缸，产品放入后气缸前推合模
5.在整形模组四周需要增加冷却机构，以保证模组在热压过程中温度的一致性
6.整形模组折弯处考虑产品整形后回弹，建议折弯角度设计成过弯（参考值：过弯10°或20°）
发生器夹持机构：
1.前端根据发生器螺柱方向，内部增加对射传感器，检测发生器放置到位，夹紧产品
2.发生器夹持机构尾部与机器人连接。可通过快换盘快换
3.设备入料口增设发生器夹持机构原点定位基座

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

#### ST50 SAB-F-ST50
一、SAB-罩盖压合设备-Equipment 
（一）机械要求:
1. 1.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
2.设备上方配置扫码枪，扫线束、气袋及壳体上警示标签。下方安装托板，防止异物掉落
3.设备前方需配1组安全光栅，光栅需接入安全回路进行控制；光栅离夹点风险点距离≥200mm
4.设备需满足上、下分体式压合罩盖的要求;
5.人机屏样式参照Autoliv最新标准（Autoliv提供人机屏3D数模）。
6.设备下方空间利用，放置罩盖压合治具，设备有足够柔性，预留铰链式罩壳压合机构空间。
7.罩盖压合治具平台设计合理，强度需保证。
8.快换夹具气电接口选用Harting HAN-Modular 气电一体式接插件，传感器线需要经集线器再接入接插件针脚，不能直接接入接插件针脚。
9.预留辅助压杆气缸机构空间孔位
10.设备顶上带有蜂鸣器的红黄绿三色灯
11.设备配有2个智能相机，#1相机固定于设备底部，检测下罩壳内气袋组件线束卡扣到位，#2相机安装于手移滑台导轨机构上，可手动左右切换、移动位置，检测气袋包布内Z折墨点有无以及检测线束黑丝胶圈是否露出下罩壳
12.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足；配线都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，建议在发热较高的元器件周围布置散热风扇；电箱柜内最少需要预留30%空间。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.相机影像检测图片需具备Auto-sorting功能，上传到追溯保存
11.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
12.不同型号产品的自动（含点检）流程程序及参数分开
13.每次循环开始需要对传感器进行ON/OFF自检
14.每次生产循环为单击触发检测，在进行第二次循环拍照前，需确认上一个影像OK信号已清除
15.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.正确的线束卡扣位置
通过相机拍照识别线束无黑色圈露出保证正确的正确的线束卡扣位置
2.正确的发生器螺柱露出高度
通过C型感应光电竖直安装距离等于图纸上定义的螺柱高度保证正确的发生器螺柱露出高度
3.正确的放置上、下罩盖壳体
通过上下夹具仿形保证正确的放置上、下罩盖壳体
4.正确的Z折尺寸
通过相机拍照对比漏出墨点保证正确的正确的Z折尺寸
5.卡扣按压到位
通过C型光电感应卡扣按压伸缩棒信号保证卡扣按压到位
6.正确的警示标签
通过扫警示标签内容进追溯系统对比保证正确的警示标签
7.正确的螺柱露出高度
通过C型光电安装高度遵循产品图纸保证正确的螺柱露出高度
8.正确的推卡扣机构
通过手持式追溯扫码枪扫机构上二维码进行防错电检保证正确的推卡扣及压持机构
9.正确的夹具工装
通过HARTING接插件针脚识别保证正确的夹具工装
方案1：[具体方案]

一、SAB-终检设备-Equipment 
（一）机械要求:
1. 1.设备整体框架按照ALV Final Check 标机制造
2.设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；电气箱把手使用内嵌式不要突出在外；安装脚踏在设备中间；设备底部使用福马轮；设备顶部需要安装照明灯；人机屏和过程监控指示灯安装位置在1500mm~1700mm之间；机械外露部分无锐边、锐角，与产品接触的工装、夹具、机构必须无锐边、毛刺等损伤产品的风险
3.设备前方需要1组4级安全光栅，硬件接入安全回路进行控制，光栅离夹手点距离≥200mm。
4.设备增设扫描枪，扫描气袋条码，可左右平移。下方安装托板，防止异物掉落
5.配置1个设备状态指示灯盒，有过程OK/NG，气袋标签扫描OK/NG等指示灯
6.设备顶上带有蜂鸣器的红黄绿三色灯
7.设备设计合理，尺寸紧凑，空间无浪费
8.设备设计符合人机工程，员工作业面高度(含启动按钮)在900~1100mm间。无安全风险

（二）电气要求:
1. 1.电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足配线且都需要走配线槽，并且对应标注清楚相应的号码管，对于有触电风险的地方另外需要有铭牌警示标识；整体电柜需注意散热，发热较高的元器件周围布置散热风扇；电箱柜内IO点位最少需要预留30%。
2.控制电源为DC24V，开关电源的功率有不少于20%的预留
3.PLC I/O出线要先接到端子排上，每个端子只允许接入一根信号线。插槽须预留2个可插模块的空间，以便以后扩展。每个CPU的存储卡及代码存储器和数据存储器的容量必须有50%以上的余量。
4.设备上需要用到插座的须选择防护等级为IP44以上的工业插座，并带有接地
5.扫描仪由PLC控制触发并接收扫描内容，PLC再将条码内容发送给追溯系统。
6.设备电气柜内有一个独立的交换机，需预留4个网口。
7.人机屏上IO点位描述需详细，清晰。手动页面气缸动作描述需详细，清晰
8.操作界面能体现当前工步的操作内容，能显示设备初始位置和设备模具的传感器状态
9.增加相应强制点检程序，点检过程必须在触摸屏上列出清单，按顺序点检
10.PLC程序应该有必要的注释，程序注释要尽量简明易懂，没有歧义，注释可以采用中文或英文，不允许使用汉语拼音，注释中尽量不要使用缩写。
11.不同型号产品的自动（含点检）流程程序及参数分开
12.每次循环开始需要对传感器进行ON/OFF自检
13.整机符合通用电气安全规范

（三）防错及点检要求:
要求1：1.无跳工序生产：
通过固定扫描器扫描组件条码，发送给追溯，追溯判断组件是否是当前正确生产顺序的产品
2.正确的产品厚度
通过GT厚度检测机构，检测产品厚度
3.正确的标签内容
通过扫描枪扫描总成标签内容，发生给追溯系统，判断标签内容是否正确
4.正确的标签角度：
通过扫描枪扫描总成标签角度，发生给追溯系统，判断标签角度是否正确
方案1：[具体方案]

二、罩盖压合治具-Fixture 
（一）机械要求:
1. 1.设备上、下工装根据产品罩盖外轮廓仿形设计、上下工装内各有2传感器感应罩壳存在。
2.上、下工装治具附有吸盘，装配在治具上，吸盘位置根据产品尺寸大小排布，上下工装吸盘负压值均有负压表进行监控。
3.上、下工装满足快换设计，有电路气路连接需求的快换接头需使用Harting HAN-Modular 气电一体式接插件。
4.上压合工装有定位销保证产品上模外轮廓尺寸，下压合工装有定位销保证产品下模外轮廓尺寸。
5.上、下压合工装内都需设计有卡扣导向限位气缸，对上、下罩盖卡扣进行内限位。
6.下罩盖四周应设计有推卡扣气缸机构，在上罩盖下压至预压合位置时，对上罩盖的卡扣起到导向作用，推卡扣机构设计为快换。
7.下工装治具对产品卡扣有避让，并带有卡扣到位检测，每个卡扣都使用1个C型光电检测卡扣卡到位，产品压合到位后不能使产品卡扣受力变形。
8.下罩盖工装应包含一夹紧气缸，夹紧气袋发生器组件，工装内各有一C型光电检测螺柱，C型光电安装位置应更遵循产品图纸定义。
9.下模工装底部设计一顶升气缸，用于罩盖压合结束后，气缸将产品顶出。
10.需制作一罩盖卡扣组装高度不良点检件用于点检卡扣C型光电ON/OFF，不良尺寸定义由奥托立夫提供。

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

二、1.产品定位治具
2.厚度GT检测机构-Fixture 
（一）机械要求:
1. 产品定位治具：
1.产品左右共用一副定位治具，电气哈丁接口，接口固定在治具上
2.治具托板为平板
3.治具设计合理，无安全风险
4.治具设计轻便，整体重量≤7.5Kg
5.传感器放大器安装位置便于维修调试
6.传感器检测发生器螺柱，无需夹爪夹持
7.治具底板材质铝合金，底部对应设备标机机台滑槽设计。
8.底板上增加治具电气哈丁接口，对接设备机台哈丁接头。
9.两侧安装把手，方便拿取
10.治具底板与设备平台通过定位销定位，定位孔内加衬套，防止磨损。
厚度GT检测机构：
1.厚度检测机构按照最新的设备标机设计
2.厚度检测机构中间增加GT ，GT探针下降，接触下方顶块，得到数值，对产品厚度进行检测
3.GT头下压过程中顺畅，不卡顿
4.GT需要有足够的检测行程，GT头不能顶死，GT行程在满行程60%~80%之间
5.厚度检测能覆盖20mm-50mm之间的产品
6.厚度检测机构具备整形拍打功能，气缸缸径选择需考虑整形力
7.测厚压板左右考虑共用，下压时避开产品发生器区域
8.压板需设计成快换，并带有防错
9. 整个压板机构自重（包含压板、连接板和导柱）需满足产品图纸要求
10.压板机构自重掉落过程中顺畅、不卡顿
11.压板通过快换连接板两侧的滑槽划入，后方增加限位块，对压板进行限位
归零、标准点检块：
1.归零块：对产品厚度检测机构GT进行归零
2.标准块：标准块厚度高于归零块零面5mm（公差±0.3）
3.归零块、标准块设计轻量化，交付时需提供尺寸检测报告

（二）电气要求:
1. 1.换型页面显示当前治具点位信息

（三）防错及点检要求:
要求1：治具通过哈丁接口防错
方案1：[具体方案]

