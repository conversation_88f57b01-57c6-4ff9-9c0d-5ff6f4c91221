# 设备图片和参数上传功能说明

## 功能概述
为设备要求页面的每个工站添加了设备图片和参数上传功能，参考产线Layout的上传格式，让用户可以为每个设备工站上传设备图片并填写设备参数。

## 主要功能

### 1. 设备图片上传
- ✅ **图片格式支持**：JPG、PNG格式
- ✅ **文件大小限制**：最大5MB
- ✅ **图片预览**：上传后立即显示预览
- ✅ **图片删除**：支持删除已上传的图片
- ✅ **图片模态查看**：点击图片可放大查看
- ✅ **文件名显示**：在预览区域显示文件名

### 2. 设备参数填写
- ✅ **设备长度(mm)**：设备长度参数
- ✅ **设备宽度(mm)**：设备宽度参数
- ✅ **设备高度(mm)**：设备高度参数
- ✅ **节拍(s)**：设备节拍时间参数
- ✅ **换型时间(min)**：设备换型时间参数
- ✅ **实时保存**：参数修改后自动保存

### 3. 数据持久化
- ✅ **数据保存**：图片和参数数据保存到设备工站数据结构中
- ✅ **数据恢复**：页面刷新或重新生成工站时自动恢复数据
- ✅ **数据同步**：与设备工站的其他信息同步保存

## 技术实现

### 1. HTML结构修改 (`static/js/station_generator.js`)

#### 1.1 为所有设备工站格式添加图片和参数上传区域
```javascript
<!-- 设备图片和参数上传区域 -->
<div style="margin-bottom: 1rem;">
    <h5>📷 设备图片和参数</h5>
    <div style="display: flex; gap: 15px; min-height: 200px;">
        <!-- 左侧图片上传区域 -->
        <div style="flex: 2;">
            <h6>设备图片</h6>
            <div id="equipment-image-preview-${index}">
                <!-- 图片预览区域 -->
            </div>
            <div>
                <input type="file" id="equipment-image-input-${index}" />
                <button onclick="上传图片">📁 上传图片</button>
                <button onclick="删除图片">🗑️ 删除图片</button>
            </div>
        </div>
        
        <!-- 右侧参数区域 -->
        <div style="flex: 1;">
            <h6>基本参数</h6>
            <div>
                <label>设备长度(mm):</label>
                <input id="equipment-length-${index}" />
                <label>设备宽度(mm):</label>
                <input id="equipment-width-${index}" />
                <label>设备高度(mm):</label>
                <input id="equipment-height-${index}" />
                <label>节拍(s):</label>
                <input id="equipment-cycle-time-${index}" />
                <label>换型时间(min):</label>
                <input id="equipment-changeover-time-${index}" />
            </div>
        </div>
    </div>
</div>
```

#### 1.2 支持的设备工站格式
- ✅ **传统格式** (`createTraditionalEquipmentStationHtml`)
- ✅ **Equipment和Fixture分离格式** (`createEquipmentFixtureStationHtml`)
- ✅ **简化Markdown格式** (`createSimpleMarkdownStationHtml`)

### 2. JavaScript功能函数 (`templates/index.html`)

#### 2.1 图片上传处理
```javascript
function handleEquipmentImageUpload(event, stationIndex) {
    // 文件类型和大小验证
    // 图片读取和预览
    // 数据保存到全局变量
    // 更新预览显示
}
```

#### 2.2 图片预览更新
```javascript
function updateEquipmentImagePreview(stationIndex) {
    // 根据数据状态更新预览区域
    // 支持图片显示和占位符显示
}
```

#### 2.3 图片删除
```javascript
function deleteEquipmentImage(stationIndex) {
    // 确认删除操作
    // 清除数据和预览
    // 重置文件输入
}
```

#### 2.4 参数更新
```javascript
function updateEquipmentParameter(stationIndex, paramName, value) {
    // 更新参数数据
    // 自动保存到设备工站数据
}
```

### 3. 数据管理 (`static/js/station_manager.js`)

#### 3.1 数据结构
```javascript
// 全局图片数据
window.equipmentImagesData = {
    [stationIndex]: {
        imageData: "data:image/jpeg;base64,...",
        fileName: "设备图片.jpg"
    }
};

// 全局参数数据
window.equipmentParametersData = {
    [stationIndex]: {
        length: "1000",
        width: "800",
        height: "1500",
        cycle_time: "30",
        changeover_time: "15"
    }
};
```

#### 3.2 数据持久化
```javascript
function saveEquipmentStationImageAndParameters(stationIndex) {
    // 将图片和参数数据保存到设备工站数据结构中
    details.equipment_image_data = imageData.imageData;
    details.equipment_image_filename = imageData.fileName;
    details.equipment_length = parameters.length;
    details.equipment_width = parameters.width;
    details.equipment_height = parameters.height;
    details.equipment_cycle_time = parameters.cycle_time;
    details.equipment_changeover_time = parameters.changeover_time;
}
```

#### 3.3 数据恢复
```javascript
function initializeEquipmentStationImageAndParameters(stationIndex) {
    // 从设备工站数据中恢复图片和参数
    // 更新预览和输入框显示
}
```

### 4. 事件监听和初始化

#### 4.1 工站生成后自动初始化
```javascript
attachEquipmentStationListeners() {
    this.equipmentStations.forEach((station, index) => {
        setTimeout(() => {
            initializeEquipmentStationImageAndParameters(index);
        }, 100);
    });
}
```

#### 4.2 全局函数暴露
- ✅ `initializeEquipmentStationImageAndParameters`
- ✅ `saveEquipmentStationImageAndParameters`

## 用户界面

### 1. 图片上传区域
- **预览区域**：显示上传的图片或占位符
- **上传按钮**：绿色主题，与设备页面风格一致
- **删除按钮**：红色，仅在有图片时显示
- **文件名显示**：在图片右上角显示文件名

### 2. 参数输入区域
- **标签清晰**：每个参数都有明确的标签和单位
  - 设备长度(mm)、设备宽度(mm)、设备高度(mm)
  - 节拍(s)、换型时间(min)
- **输入框样式**：与页面整体风格一致
- **实时保存**：输入后自动保存，无需手动操作

### 3. 布局设计
- **左右分布**：图片区域占2/3，参数区域占1/3
- **紧凑设计**：适合在工站内容中显示
- **响应式**：适应不同屏幕尺寸

## 数据流程

### 1. 上传流程
```
用户选择图片 → 文件验证 → 读取文件 → 更新预览 → 保存数据 → 显示成功提示
```

### 2. 参数更新流程
```
用户输入参数 → 触发change事件 → 更新全局数据 → 保存到工站数据 → 记录日志
```

### 3. 数据恢复流程
```
生成工站 → 延迟初始化 → 读取工站数据 → 恢复图片和参数 → 更新界面显示
```

## 兼容性

### 1. 浏览器支持
- ✅ **现代浏览器**：Chrome、Firefox、Safari、Edge
- ✅ **文件API**：FileReader、File API
- ✅ **ES6语法**：箭头函数、模板字符串

### 2. 设备工站格式支持
- ✅ **传统格式**：完全支持
- ✅ **Equipment/Fixture分离**：完全支持
- ✅ **简化Markdown**：完全支持

### 3. 数据格式兼容
- ✅ **向后兼容**：不影响现有设备工站数据
- ✅ **可选功能**：图片和参数为可选，不影响基本功能

## 测试验证

### 验证项目
1. ✅ 可以为每个设备工站上传图片
2. ✅ 图片上传后正确显示预览
3. ✅ 可以删除已上传的图片
4. ✅ 可以填写和修改设备参数
5. ✅ 参数修改后自动保存
6. ✅ 页面刷新后数据正确恢复
7. ✅ 新增工站时功能正常
8. ✅ 删除工站时数据正确清理
9. ✅ 所有设备工站格式都支持此功能
10. ✅ 图片点击可以放大查看

## 总结

成功为设备要求页面的每个工站添加了设备图片和参数上传功能，实现了：

1. **完整的图片管理**：上传、预览、删除、放大查看
2. **便捷的参数填写**：长度、宽度、高度、功率等关键参数
3. **可靠的数据持久化**：自动保存和恢复机制
4. **统一的用户体验**：与产线Layout上传格式保持一致
5. **全面的格式支持**：适用于所有类型的设备工站

这个功能大大增强了设备要求页面的实用性，让用户可以更直观地管理设备信息，包括图片和关键参数，为设备管理提供了完整的解决方案。
