#!/usr/bin/env python3
"""
测试可编辑标题功能
验证工站框中只保留可编辑的标题，移除重复的内容区域
"""

import re
import requests

def test_editable_titles():
    """测试可编辑标题功能是否正确实现"""
    
    print("=== 测试可编辑标题功能 ===\n")
    
    try:
        # 读取JavaScript文件内容
        with open('static/js/station_generator.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        print("✅ 成功读取station_generator.js文件")
        
        # 1. 检查是否移除了原来的标签+文本框结构
        print("\n1. 检查原有结构移除...")
        
        # 检查是否还有label+textarea的组合
        label_textarea_pattern = r'<label.*?>.*?工艺过程描述.*?</label>.*?<textarea'
        if re.search(label_textarea_pattern, js_content, re.DOTALL):
            print("❌ 仍然存在label+textarea的旧结构")
            return False
        else:
            print("✅ 已移除label+textarea的旧结构")
        
        # 2. 检查新的可编辑输入框
        print("\n2. 检查可编辑输入框...")
        
        # 检查是否有input type="text"
        input_count = len(re.findall(r'<input\s+type="text"', js_content))
        if input_count >= 3:  # 应该有至少3个输入框（工艺过程描述、产品特性要求、过程防错要求）
            print(f"✅ 找到{input_count}个可编辑输入框")
        else:
            print(f"❌ 只找到{input_count}个输入框，应该至少有3个")
        
        # 3. 检查输入框的样式
        print("\n3. 检查输入框样式...")
        
        # 检查是否有正确的样式设置
        style_patterns = [
            r'width:\s*100%',
            r'background:\s*#f8f9fa',
            r'font-weight:\s*500'
        ]
        
        for pattern in style_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                print(f"✅ 找到样式设置: {pattern} ({len(matches)}个)")
            else:
                print(f"❌ 未找到样式设置: {pattern}")
        
        # 4. 检查默认值设置
        print("\n4. 检查默认值设置...")
        
        default_values = [
            '工艺过程描述',
            '产品特性要求', 
            '过程防错要求'
        ]
        
        for value in default_values:
            if value in js_content:
                print(f"✅ 找到默认值: {value}")
            else:
                print(f"❌ 未找到默认值: {value}")
        
        # 5. 检查事件绑定
        print("\n5. 检查事件绑定...")
        
        # 检查onchange事件
        onchange_count = len(re.findall(r'onchange="updateProcessStep', js_content))
        if onchange_count >= 3:
            print(f"✅ 找到{onchange_count}个onchange事件绑定")
        else:
            print(f"❌ 只找到{onchange_count}个事件绑定，应该至少有3个")
        
        # 6. 检查字段映射
        print("\n6. 检查字段映射...")
        
        field_mappings = [
            "'description'",
            "'quality_requirements'",
            "'error_prevention'"
        ]
        
        for field in field_mappings:
            if field in js_content:
                print(f"✅ 找到字段映射: {field}")
            else:
                print(f"❌ 未找到字段映射: {field}")
        
        print("\n=== 可编辑标题测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

def generate_editable_titles_preview():
    """生成可编辑标题预览HTML"""
    print("\n=== 生成可编辑标题预览 ===")
    
    try:
        # 创建预览文件
        preview_html = """
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可编辑标题预览</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .preview-container { max-width: 800px; margin: 0 auto; }
        .step-preview { 
            border: 1px solid #e8e8e8; 
            border-radius: 4px; 
            padding: 0.5rem; 
            margin-bottom: 0.5rem; 
            background: white; 
        }
        .step-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            margin-bottom: 0.4rem; 
        }
        .step-title-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .step-title { 
            margin: 0; 
            color: #1a73e8; 
            font-size: 0.85rem; 
            font-weight: 500; 
        }
        .operator-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        .editable-title { 
            width: 100%; 
            padding: 0.4rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.85rem; 
            font-weight: 500; 
            color: #333; 
            background: #f8f9fa;
            margin-bottom: 0.4rem;
        }
        .editable-title:last-child {
            margin-bottom: 0;
        }
        .editable-title:focus {
            outline: none;
            border-color: #1a73e8;
            background: white;
        }
        select { 
            width: 80px; 
            padding: 0.2rem; 
            border: 1px solid #ddd; 
            border-radius: 3px; 
            font-size: 0.75rem; 
            height: 28px; 
        }
        .delete-btn {
            background: #ff7875; 
            color: white; 
            border: none; 
            border-radius: 2px; 
            padding: 1px 4px; 
            cursor: pointer; 
            font-size: 0.65rem;
        }
        .operator-label {
            font-weight: 500; 
            font-size: 0.8rem; 
            color: #555; 
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h2>可编辑标题预览</h2>
        <p>新的设计：移除重复内容，只保留可编辑的标题</p>
        
        <div class="step-preview">
            <!-- 步骤标题行：步骤号 + 人or设备 + 删除按钮 -->
            <div class="step-header">
                <div class="step-title-group">
                    <h6 class="step-title">步骤 1</h6>
                    <div class="operator-group">
                        <label class="operator-label">人or设备:</label>
                        <select>
                            <option>人</option>
                        </select>
                    </div>
                </div>
                <button class="delete-btn">×</button>
            </div>
            
            <!-- 可编辑标题 -->
            <input type="text" class="editable-title" value="人工检查包装材料完整性" placeholder="请输入工艺过程描述">
            <input type="text" class="editable-title" value="包装材料无破损，标签清晰" placeholder="请输入产品特性要求">
            <input type="text" class="editable-title" value="目视检查，发现问题立即停止" placeholder="请输入过程防错要求">
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e8f4fd; border-radius: 4px;">
            <h3>可编辑标题改进说明：</h3>
            <ul>
                <li>✅ 移除了标签+文本框的重复结构</li>
                <li>✅ 每个内容项变成可编辑的输入框</li>
                <li>✅ 输入框具有默认提示文字</li>
                <li>✅ 样式统一，视觉更简洁</li>
                <li>✅ 背景色区分，便于识别可编辑区域</li>
                <li>✅ 聚焦时背景变白，提供视觉反馈</li>
            </ul>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #f0f9ff; border-radius: 4px;">
            <h3>布局对比：</h3>
            
            <h4>修改前（重复结构）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 步骤 1    人or设备: [选择器]                      × │
├─────────────────────────────────────────────────────────┤
│ 工艺过程描述: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 产品特性要求: [──────────文本框──────────────────] │
├─────────────────────────────────────────────────────────┤
│ 过程防错要求: [──────────文本框──────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
            
            <h4>修改后（简洁结构）：</h4>
            <pre style="background: #fff; padding: 10px; border-radius: 4px; font-size: 12px;">
┌─────────────────────────────────────────────────────────┐
│ 步骤 1    人or设备: [选择器]                      × │
├─────────────────────────────────────────────────────────┤
│ [────────────可编辑标题1────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ [────────────可编辑标题2────────────────────────] │
├─────────────────────────────────────────────────────────┤
│ [────────────可编辑标题3────────────────────────] │
└─────────────────────────────────────────────────────────┘
            </pre>
        </div>
    </div>
</body>
</html>
        """
        
        with open('editable_titles_preview.html', 'w', encoding='utf-8') as f:
            f.write(preview_html)
        
        print("✅ 可编辑标题预览文件已生成: editable_titles_preview.html")
        print("💡 可以在浏览器中打开此文件查看新布局效果")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成预览失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试可编辑标题功能...\n")
    
    # 运行功能测试
    function_ok = test_editable_titles()
    
    # 生成预览
    preview_ok = generate_editable_titles_preview()
    
    print(f"\n{'='*60}")
    if function_ok:
        print("🎉 所有测试通过！")
        print("✅ 已移除重复的标签+文本框结构")
        print("✅ 实现了可编辑标题功能")
        print("✅ 布局更加简洁清晰")
        
        if preview_ok:
            print("✅ 可编辑标题预览文件已生成")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
    
    print(f"{'='*60}")
