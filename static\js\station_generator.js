/**
 * 动态工站生成器
 * 根据AI生成内容中的工站数量动态创建工站位，并将内容自动分配到对应工站中
 */

class StationGenerator {
    constructor() {
        this.processStations = [];
        this.equipmentStations = [];
    }

    /**
     * 根据解析的工站信息动态生成工艺页面的工站
     * @param {Array} stations - 工站信息数组
     */
    generateProcessStations(stations, preserveExisting = false) {
        this.processStations = stations;
        const container = document.getElementById('stations-list');

        if (!container) {
            console.error('未找到工站容器');
            return;
        }

        // 根据参数决定是否保留现有内容
        if (!preserveExisting) {
            // 清空现有内容（用于AI生成或完全重新生成）
            container.innerHTML = '';
        }

        // 为每个工站生成HTML
        stations.forEach((station, index) => {
            const stationHtml = this.createProcessStationHtml(station, index);
            container.insertAdjacentHTML('beforeend', stationHtml);
        });

        // 添加事件监听器
        this.attachProcessStationListeners();
    }

    /**
     * 在指定位置插入单个工站（不清除现有内容）
     * @param {Object} station - 工站数据
     * @param {number} insertIndex - 插入位置索引
     */
    insertSingleProcessStation(station, insertIndex) {
        const container = document.getElementById('stations-list');

        if (!container) {
            console.error('未找到工站容器');
            return;
        }

        // 生成新工站的HTML
        const stationHtml = this.createProcessStationHtml(station, insertIndex);

        // 获取现有的工站元素
        const existingStations = container.querySelectorAll('.station-block');

        if (insertIndex === 0) {
            // 插入到开头
            container.insertAdjacentHTML('afterbegin', stationHtml);
        } else if (insertIndex >= existingStations.length) {
            // 插入到末尾
            container.insertAdjacentHTML('beforeend', stationHtml);
        } else {
            // 插入到指定位置
            const targetStation = existingStations[insertIndex];
            targetStation.insertAdjacentHTML('beforebegin', stationHtml);
        }

        // 更新所有工站的索引属性
        this.updateStationIndices();

        // 添加事件监听器
        this.attachProcessStationListeners();
    }

    /**
     * 更新所有工站的data-station-index属性
     */
    updateStationIndices() {
        const container = document.getElementById('stations-list');
        if (!container) return;

        const stationBlocks = container.querySelectorAll('.station-block');
        stationBlocks.forEach((block, index) => {
            block.setAttribute('data-station-index', index);
        });
    }

    /**
     * 创建工艺工站的HTML
     * @param {Object} station - 工站信息
     * @param {number} index - 工站索引
     * @returns {string} HTML字符串
     */
    createProcessStationHtml(station, index) {
        const stepsHtml = station.process_steps ?
            station.process_steps.map((step, stepIndex) =>
                this.createProcessStepHtml(step, index, stepIndex, station.process_steps.length)
            ).join('') :
            '';

        return `
            <!-- 工站前插入按钮 - 只在第一个工站前显示 -->
            ${index === 0 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertProcessStationBefore(${index})"
                        style="background: #1890ff; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站前插入新工站">
                    ↑ 在此前插入工站
                </button>
            </div>
            ` : ''}

            <div class="station-block compact" data-station-index="${index}" style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 0.8rem; margin-bottom: 1rem; background: #fafbfc;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.6rem; padding-bottom: 0.4rem; border-bottom: 1px solid #e0e0e0;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <button onclick="toggleStationCollapse(${index})"
                                style="background: none; border: none; color: #1a73e8; cursor: pointer; font-size: 0.8rem; padding: 0; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center;"
                                title="折叠/展开">
                            <span class="collapse-icon">▼</span>
                        </button>
                        <h4 style="margin: 0; color: #1a73e8; font-size: 1rem; font-weight: 600; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                            ondblclick="makeStationTitleEditable(${index}, this)"
                            onmouseover="this.style.backgroundColor='#f0f7ff'"
                            onmouseout="this.style.backgroundColor='transparent'"
                            title="双击编辑工站标题">
                            ST${station.station_number} - ${station.station_name}
                        </h4>
                    </div>
                    <div style="display: flex; gap: 0.3rem;">
                        <button onclick="insertProcessStationAfter(${index})"
                                style="background: #1890ff; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;"
                                title="在此工站后插入新工站">
                            插入工站
                        </button>
                        <button onclick="deleteProcessStation(${index})"
                                style="background: #ff4d4f; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;">
                            删除
                        </button>
                    </div>
                </div>

                <div class="station-content" data-station-index="${index}" style="transition: all 0.3s ease;">

                    <div class="process-steps-container compact" data-station-index="${index}">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                            <h5 style="margin: 0; color: #333; font-size: 0.9rem; font-weight: 500;">工艺步骤:</h5>
                            <div style="display: flex; gap: 0.3rem;">
                                <button onclick="insertProcessStep(${index}, 0)"
                                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.75rem;"
                                        title="在开头插入步骤">
                                    ↑插入
                                </button>
                                <button onclick="addProcessStep(${index})"
                                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.75rem;"
                                        title="在末尾添加步骤">
                                    +添加
                                </button>
                            </div>
                        </div>
                        ${stepsHtml}
                    </div>
                </div>
            </div>

            <!-- 工站后插入按钮（仅在最后一个工站显示） -->
            ${index === this.processStations.length - 1 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertProcessStationAfter(${index})"
                        style="background: #1890ff; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站后插入新工站">
                    ↓ 在此后插入工站
                </button>
            </div>
            ` : ''}
        `;
    }

    /**
     * 创建工艺步骤的HTML
     * @param {Object} step - 步骤信息
     * @param {number} stationIndex - 工站索引
     * @param {number} stepIndex - 步骤索引
     * @param {number} totalSteps - 总步骤数
     * @returns {string} HTML字符串
     */
    createProcessStepHtml(step, stationIndex, stepIndex, totalSteps = 0) {
        return `
            <div class="process-step compact" data-step-index="${stepIndex}" style="border: 1px solid #e8e8e8; border-radius: 4px; padding: 0.5rem; margin-bottom: 0.5rem; background: white;">
                <!-- 步骤标题行：步骤号 + 人or设备 + 操作按钮 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.4rem;">
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <h6 style="margin: 0; color: #1a73e8; font-size: 0.85rem; font-weight: 500;">步骤 ${step.step_number}</h6>
                        <div style="display: flex; align-items: center; gap: 0.3rem;">
                            <label style="font-weight: 500; font-size: 0.8rem; color: #555; white-space: nowrap;">人or设备:</label>
                            <select onchange="updateProcessStep(${stationIndex}, ${stepIndex}, 'operator', this.value)"
                                    style="width: 80px; padding: 0.2rem; border: 1px solid #ddd; border-radius: 3px; font-size: 0.75rem; height: 28px;">
                                <option value="">选择</option>
                                <option value="人" ${step.operator === '人' ? 'selected' : ''}>人</option>
                                <option value="设备" ${step.operator === '设备' ? 'selected' : ''}>设备</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: flex; gap: 0.2rem;">
                        <!-- 带悬停菜单的插入按钮 -->
                        <div class="step-insert-menu" style="position: relative; display: inline-block;">
                            <button class="step-insert-btn"
                                    style="background: #52c41a; color: white; border: none; border-radius: 2px; padding: 2px 6px; cursor: pointer; font-size: 0.65rem; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;"
                                    onmouseenter="showStepInsertMenu(${stationIndex}, ${stepIndex}, this)"
                                    onmouseleave="hideStepInsertMenu(this)">
                                +
                            </button>
                            <div class="step-insert-dropdown" style="display: none; position: absolute; right: 0; top: 100%; background: white; border: 1px solid #ddd; border-radius: 3px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); z-index: 1000; min-width: 120px;">
                                <div onclick="insertProcessStepBefore(${stationIndex}, ${stepIndex}); hideStepInsertMenu(this.parentElement.parentElement.querySelector('.step-insert-btn'))"
                                     style="padding: 0.3rem 0.5rem; cursor: pointer; font-size: 0.7rem; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s;"
                                     onmouseover="this.style.backgroundColor='#f5f5f5'"
                                     onmouseout="this.style.backgroundColor='white'">
                                    在此步骤前插入
                                </div>
                                <div onclick="insertProcessStepAfter(${stationIndex}, ${stepIndex}); hideStepInsertMenu(this.parentElement.parentElement.querySelector('.step-insert-btn'))"
                                     style="padding: 0.3rem 0.5rem; cursor: pointer; font-size: 0.7rem; transition: background-color 0.2s;"
                                     onmouseover="this.style.backgroundColor='#f5f5f5'"
                                     onmouseout="this.style.backgroundColor='white'">
                                    在此步骤后插入
                                </div>
                            </div>
                        </div>
                        <button onclick="deleteProcessStep(${stationIndex}, ${stepIndex})"
                                style="background: #ff7875; color: white; border: none; border-radius: 2px; padding: 2px 6px; cursor: pointer; font-size: 0.65rem; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                            ×
                        </button>
                    </div>
                </div>

                <!-- 工艺过程描述 -->
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                    <label style="font-weight: 500; font-size: 0.8rem; color: #555; white-space: nowrap; min-width: 90px;">工艺过程描述:</label>
                    <textarea onchange="updateProcessStep(${stationIndex}, ${stepIndex}, 'description', this.value)"
                              style="flex: 1; height: 35px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; resize: none; font-size: 0.8rem; line-height: 1.3;"
                              placeholder="请输入工艺过程描述">${step.description || ''}</textarea>
                </div>

                <!-- 产品特性要求 -->
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.4rem;">
                    <label style="font-weight: 500; font-size: 0.8rem; color: #555; white-space: nowrap; min-width: 90px;">产品特性要求:</label>
                    <textarea onchange="updateProcessStep(${stationIndex}, ${stepIndex}, 'quality_requirements', this.value)"
                              style="flex: 1; height: 35px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; resize: none; font-size: 0.8rem; line-height: 1.3;"
                              placeholder="请输入产品特性要求">${step.quality_requirements || ''}</textarea>
                </div>

                <!-- 过程防错要求 -->
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <label style="font-weight: 500; font-size: 0.8rem; color: #555; white-space: nowrap; min-width: 90px;">过程防错要求:</label>
                    <textarea onchange="updateProcessStep(${stationIndex}, ${stepIndex}, 'error_prevention', this.value)"
                              style="flex: 1; height: 35px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; resize: none; font-size: 0.8rem; line-height: 1.3;"
                              placeholder="请输入过程防错要求">${step.error_prevention || ''}</textarea>
                </div>
            </div>
        `;
    }

    /**
     * 根据解析的设备工站信息动态生成设备页面的工站
     * @param {Array} equipmentStations - 设备工站信息数组
     */
    generateEquipmentStations(equipmentStations) {
        console.log('[DEBUG] generateEquipmentStations被调用，设备工站数量:', equipmentStations.length);
        console.log('[DEBUG] 设备工站数据:', equipmentStations);

        this.equipmentStations = equipmentStations;
        const container = document.getElementById('equipment-stations-list');

        if (!container) {
            console.error('未找到设备工站容器 #equipment-stations-list');
            return;
        }

        console.log('[DEBUG] 找到设备工站容器，开始生成HTML');

        // 清空现有内容
        container.innerHTML = '';

        if (equipmentStations.length === 0) {
            console.log('[DEBUG] 设备工站数组为空，显示提示信息');
            container.innerHTML = '<div style="padding: 2rem; text-align: center; color: #999;">暂无设备工站信息</div>';
            return;
        }

        // 为每个设备工站生成HTML
        equipmentStations.forEach((station, index) => {
            console.log(`[DEBUG] 生成设备工站 ${index}:`, station);
            const stationHtml = this.createEquipmentStationHtml(station, index);
            console.log(`[DEBUG] 设备工站 ${index} HTML长度:`, stationHtml.length);
            container.insertAdjacentHTML('beforeend', stationHtml);
        });

        console.log('[DEBUG] 设备工站HTML生成完成，添加事件监听器');

        // 添加事件监听器
        this.attachEquipmentStationListeners();

        console.log('[DEBUG] 设备工站生成完成');
    }

    /**
     * 在指定位置插入单个设备工站（不清除现有内容）
     * @param {Object} station - 设备工站数据
     * @param {number} insertIndex - 插入位置索引
     */
    insertSingleEquipmentStation(station, insertIndex) {
        const container = document.getElementById('equipment-stations-list');

        if (!container) {
            console.error('未找到设备工站容器');
            return;
        }

        // 生成新设备工站的HTML
        const stationHtml = this.createEquipmentStationHtml(station, insertIndex);

        // 获取现有的设备工站元素
        const existingStations = container.querySelectorAll('.equipment-station-block');

        if (insertIndex === 0) {
            // 插入到开头
            container.insertAdjacentHTML('afterbegin', stationHtml);
        } else if (insertIndex >= existingStations.length) {
            // 插入到末尾
            container.insertAdjacentHTML('beforeend', stationHtml);
        } else {
            // 插入到指定位置
            const targetStation = existingStations[insertIndex];
            targetStation.insertAdjacentHTML('beforebegin', stationHtml);
        }

        // 更新所有设备工站的索引属性
        this.updateEquipmentStationIndices();

        // 添加事件监听器
        this.attachEquipmentStationListeners();

        console.log(`[DEBUG] 在位置 ${insertIndex} 插入设备工站: ST${station.station_number}`);
    }

    /**
     * 更新所有设备工站的data-station-index属性
     */
    updateEquipmentStationIndices() {
        const container = document.getElementById('equipment-stations-list');
        if (!container) return;

        const stationBlocks = container.querySelectorAll('.equipment-station-block');
        stationBlocks.forEach((block, index) => {
            block.setAttribute('data-station-index', index);

            // 更新工站内部的索引相关元素
            this.updateEquipmentStationIndexElements(block, index);
        });
    }

    /**
     * 更新设备工站内部的索引相关元素
     * @param {Element} stationBlock - 工站块元素
     * @param {number} newIndex - 新的索引
     */
    updateEquipmentStationIndexElements(stationBlock, newIndex) {
        // 更新所有包含索引的onclick属性
        const elementsWithOnclick = stationBlock.querySelectorAll('[onclick]');
        elementsWithOnclick.forEach(element => {
            const onclickAttr = element.getAttribute('onclick');
            if (onclickAttr) {
                // 更新函数调用中的第一个参数（通常是stationIndex）
                const updatedOnclick = onclickAttr.replace(/\(\d+/g, `(${newIndex}`);
                element.setAttribute('onclick', updatedOnclick);
            }
        });

        // 更新所有包含索引的onchange属性
        const elementsWithOnchange = stationBlock.querySelectorAll('[onchange]');
        elementsWithOnchange.forEach(element => {
            const onchangeAttr = element.getAttribute('onchange');
            if (onchangeAttr) {
                // 更新函数调用中的第一个参数（通常是stationIndex）
                const updatedOnchange = onchangeAttr.replace(/\(\d+/g, `(${newIndex}`);
                element.setAttribute('onchange', updatedOnchange);
            }
        });

        // 更新所有包含工站索引的ID
        const elementsWithId = stationBlock.querySelectorAll('[id*="-"]');
        elementsWithId.forEach(element => {
            const id = element.id;
            if (id && id.includes('-')) {
                // 更新ID中的数字部分
                const updatedId = id.replace(/-\d+(-|$)/, `-${newIndex}$1`);
                element.id = updatedId;
            }
        });
    }

    /**
     * 创建设备工站的HTML
     * @param {Object} station - 设备工站信息
     * @param {number} index - 工站索引
     * @returns {string} HTML字符串
     */
    createEquipmentStationHtml(station, index) {
        const details = station.equipment_details || {};
        const stationName = station.station_name || '设备信息';

        // 检查是否有新格式的详细字段
        const hasMarkdownFields = details.mechanical_requirements || details.electrical_requirements || details.error_prevention_requirements;

        if (hasMarkdownFields) {
            // 使用新的Markdown格式布局
            return this.createMarkdownEquipmentStationHtml(station, index);
        } else {
            // 使用传统格式布局
            return this.createTraditionalEquipmentStationHtml(station, index);
        }
    }

    /**
     * 创建传统格式的设备工站HTML（与工艺页面保持一致的底层格式和逻辑）
     */
    createTraditionalEquipmentStationHtml(station, index) {
        const details = station.equipment_details || {};
        const stationName = station.station_name || '设备信息';

        return `
            <!-- 设备工站前插入按钮 - 只在第一个工站前显示 -->
            ${index === 0 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertEquipmentStationBefore(${index})"
                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站前插入新设备工站">
                    ↑ 在此前插入设备工站
                </button>
            </div>
            ` : ''}

            <div class="equipment-station-block compact" data-station-index="${index}" style="border: 1px solid #e0e0e0; border-radius: 6px; padding: 0.8rem; margin-bottom: 1rem; background: #fafbfc;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.6rem; padding-bottom: 0.4rem; border-bottom: 1px solid #e0e0e0;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <button onclick="toggleEquipmentStationCollapse(${index})"
                                style="background: none; border: none; color: #52c41a; cursor: pointer; font-size: 0.8rem; padding: 0; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center;"
                                title="折叠/展开">
                            <span class="collapse-icon">▼</span>
                        </button>
                        <h4 style="margin: 0; color: #52c41a; font-size: 1rem; font-weight: 600; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                            ondblclick="makeEquipmentStationTitleEditable(${index}, this)"
                            onmouseover="this.style.backgroundColor='#f0f8f0'"
                            onmouseout="this.style.backgroundColor='transparent'"
                            title="双击编辑设备工站标题">
                            ST${station.station_number} - ${stationName}
                        </h4>
                    </div>
                    <div style="display: flex; gap: 0.3rem;">
                        <button onclick="insertEquipmentStationAfter(${index})"
                                style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;"
                                title="在此工站后插入新设备工站">
                            插入设备工站
                        </button>
                        <button onclick="deleteEquipmentStation(${index})"
                                style="background: #ff4d4f; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;">
                            删除
                        </button>
                    </div>
                </div>

                <div class="station-content" data-station-index="${index}" style="transition: all 0.3s ease;">

                    <!-- 设备图片和参数上传区域 -->
                    <div style="margin-bottom: 1rem;">
                        <h5 style="margin: 0 0 0.5rem 0; color: #52c41a; font-size: 0.9rem; font-weight: 600;">📷 设备图片和参数</h5>
                        <div style="display: flex; gap: 15px; min-height: 200px;">
                            <!-- 左侧图片上传区域 -->
                            <div style="flex: 2; background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0; display: flex; flex-direction: column;">
                                <h6 style="margin-bottom: 10px; font-size: 14px; color: #333;">设备图片</h6>
                                <div id="equipment-image-preview-${index}" style="flex: 1; overflow: hidden; text-align: center; margin-bottom: 10px; min-height: 120px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                    <div style="color: #999;">
                                        <div style="font-size: 32px; margin-bottom: 5px;">🖼️</div>
                                        <p style="margin: 0; font-size: 12px;">点击下方按钮上传设备图片</p>
                                        <small>支持 JPG, PNG 格式</small>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: center; gap: 8px;">
                                    <input type="file" id="equipment-image-input-${index}" accept="image/*" style="display: none;" onchange="handleEquipmentImageUpload(event, ${index})">
                                    <button onclick="document.getElementById('equipment-image-input-${index}').click()" class="btn" style="background: #52c41a; color: white; font-size: 12px; padding: 4px 8px;">
                                        📁 上传图片
                                    </button>
                                    <button onclick="deleteEquipmentImage(${index})" class="btn" style="background: #ff7875; color: white; display: none; font-size: 12px; padding: 4px 8px;" id="delete-equipment-image-btn-${index}">
                                        🗑️ 删除图片
                                    </button>
                                </div>
                            </div>

                            <!-- 右侧参数区域 -->
                            <div style="flex: 1; background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0;">
                                <h6 style="margin-bottom: 10px; font-size: 14px; color: #333;">基本参数</h6>
                                <div style="display: flex; flex-direction: column; gap: 10px;">
                                    <div>
                                        <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备长度(mm):</label>
                                        <input type="text" id="equipment-length-${index}" placeholder="请输入设备长度"
                                               onchange="updateEquipmentParameter(${index}, 'length', this.value)"
                                               style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备宽度(mm):</label>
                                        <input type="text" id="equipment-width-${index}" placeholder="请输入设备宽度"
                                               onchange="updateEquipmentParameter(${index}, 'width', this.value)"
                                               style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备高度(mm):</label>
                                        <input type="text" id="equipment-height-${index}" placeholder="请输入设备高度"
                                               onchange="updateEquipmentParameter(${index}, 'height', this.value)"
                                               style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备节拍(s):</label>
                                        <input type="text" id="equipment-cycle-time-${index}" placeholder="请输入设备节拍时间"
                                               onchange="updateEquipmentParameter(${index}, 'cycle_time', this.value)"
                                               style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                    </div>
                                    <div>
                                        <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">换型时间(min):</label>
                                        <input type="text" id="equipment-changeover-time-${index}" placeholder="请输入换型时间"
                                               onchange="updateEquipmentParameter(${index}, 'changeover_time', this.value)"
                                               style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 原有的设备信息区域 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.6rem;">
                        <div>
                            <label style="display: block; margin-bottom: 0.2rem; font-weight: 500; color: #333; font-size: 0.85rem;">设备类型:</label>
                            <input type="text"
                                   value="${details.equipment_type || ''}"
                                   onchange="updateEquipmentStationInfo(${index}, 'equipment_type', this.value)"
                                   style="width: 100%; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; font-size: 0.85rem;"
                                   placeholder="请输入设备类型">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.2rem; font-weight: 500; color: #333; font-size: 0.85rem;">技术要求:</label>
                            <textarea onchange="updateEquipmentStationInfo(${index}, 'technical_requirements', this.value)"
                                      style="width: 100%; height: 50px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; resize: none; font-size: 0.8rem; line-height: 1.3;"
                                      placeholder="请输入技术要求">${details.technical_requirements || ''}</textarea>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.2rem; font-weight: 500; color: #333; font-size: 0.85rem;">设备参数:</label>
                            <textarea onchange="updateEquipmentStationInfo(${index}, 'equipment_parameters', this.value)"
                                      style="width: 100%; height: 50px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; resize: none; font-size: 0.8rem; line-height: 1.3;"
                                      placeholder="请输入设备参数">${details.equipment_parameters || ''}</textarea>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 0.2rem; font-weight: 500; color: #333; font-size: 0.85rem;">安全要求:</label>
                            <textarea onchange="updateEquipmentStationInfo(${index}, 'safety_requirements', this.value)"
                                      style="width: 100%; height: 50px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 3px; resize: none; font-size: 0.8rem; line-height: 1.3;"
                                      placeholder="请输入安全要求">${details.safety_requirements || ''}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 设备工站后插入按钮（仅在最后一个工站显示） -->
            ${index === this.equipmentStations.length - 1 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertEquipmentStationAfter(${index})"
                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站后插入新设备工站">
                    ↓ 在此后插入设备工站
                </button>
            </div>
            ` : ''}
        `;
    }

    /**
     * 创建Markdown格式的设备工站HTML（支持Equipment和Fixture分离）
     */
    createMarkdownEquipmentStationHtml(station, index) {
        const details = station.equipment_details || {};
        const stationName = station.station_name || '设备信息';

        // 检查是否有分离的Equipment和Fixture数据
        const hasEquipmentFixtureData = details.equipment_mechanical_requirements ||
                                       details.fixture_mechanical_requirements ||
                                       details.equipment_electrical_requirements ||
                                       details.fixture_electrical_requirements;

        if (hasEquipmentFixtureData) {
            return this.createEquipmentFixtureStationHtml(station, index);
        } else {
            return this.createSimpleMarkdownStationHtml(station, index);
        }
    }

    /**
     * 创建Equipment和Fixture分离的设备工站HTML（与工艺页面保持一致的底层格式和逻辑）
     */
    createEquipmentFixtureStationHtml(station, index) {
        const details = station.equipment_details || {};
        const stationName = station.station_name || '设备信息';

        return `
            <!-- 设备工站前插入按钮 - 只在第一个工站前显示 -->
            ${index === 0 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertEquipmentStationBefore(${index})"
                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站前插入新设备工站">
                    ↑ 在此前插入设备工站
                </button>
            </div>
            ` : ''}

            <div class="equipment-station-block equipment-fixture-format" data-station-index="${index}" style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; background: #fafbfc;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem; padding-bottom: 0.5rem; border-bottom: 2px solid #52c41a;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <button onclick="toggleEquipmentStationCollapse(${index})"
                                style="background: none; border: none; color: #52c41a; cursor: pointer; font-size: 0.8rem; padding: 0; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center;"
                                title="折叠/展开">
                            <span class="collapse-icon">▼</span>
                        </button>
                        <h4 style="margin: 0; color: #52c41a; font-size: 1.1rem; font-weight: 600; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                            ondblclick="makeEquipmentStationTitleEditable(${index}, this)"
                            onmouseover="this.style.backgroundColor='#f0f8f0'"
                            onmouseout="this.style.backgroundColor='transparent'"
                            title="双击编辑设备工站标题">
                            ST${station.station_number} - ${stationName}
                        </h4>
                    </div>
                    <div style="display: flex; gap: 0.3rem;">
                        <button onclick="insertEquipmentStationAfter(${index})"
                                style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;"
                                title="在此工站后插入新设备工站">
                            插入设备工站
                        </button>
                        <button onclick="deleteEquipmentStation(${index})"
                                style="background: #ff4d4f; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 0.75rem;">
                            删除
                        </button>
                    </div>
                </div>

                <div class="station-content" data-station-index="${index}" style="transition: all 0.3s ease;">

                <!-- 设备图片和参数上传区域 -->
                <div style="margin-bottom: 1rem;">
                    <h5 style="margin: 0 0 0.5rem 0; color: #52c41a; font-size: 0.9rem; font-weight: 600;">📷 设备图片和参数</h5>
                    <div style="display: flex; gap: 15px; min-height: 200px;">
                        <!-- 左侧图片上传区域 -->
                        <div style="flex: 2; background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0; display: flex; flex-direction: column;">
                            <h6 style="margin-bottom: 10px; font-size: 14px; color: #333;">设备图片</h6>
                            <div id="equipment-image-preview-${index}" style="flex: 1; overflow: hidden; text-align: center; margin-bottom: 10px; min-height: 120px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                <div style="color: #999;">
                                    <div style="font-size: 32px; margin-bottom: 5px;">🖼️</div>
                                    <p style="margin: 0; font-size: 12px;">点击下方按钮上传设备图片</p>
                                    <small>支持 JPG, PNG 格式</small>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center; gap: 8px;">
                                <input type="file" id="equipment-image-input-${index}" accept="image/*" style="display: none;" onchange="handleEquipmentImageUpload(event, ${index})">
                                <button onclick="document.getElementById('equipment-image-input-${index}').click()" class="btn" style="background: #52c41a; color: white; font-size: 12px; padding: 4px 8px;">
                                    📁 上传图片
                                </button>
                                <button onclick="deleteEquipmentImage(${index})" class="btn" style="background: #ff7875; color: white; display: none; font-size: 12px; padding: 4px 8px;" id="delete-equipment-image-btn-${index}">
                                    🗑️ 删除图片
                                </button>
                            </div>
                        </div>

                        <!-- 右侧参数区域 -->
                        <div style="flex: 1; background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0;">
                            <h6 style="margin-bottom: 10px; font-size: 14px; color: #333;">基本参数</h6>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备长度(mm):</label>
                                    <input type="text" id="equipment-length-${index}" placeholder="请输入设备长度"
                                           onchange="updateEquipmentParameter(${index}, 'length', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备宽度(mm):</label>
                                    <input type="text" id="equipment-width-${index}" placeholder="请输入设备宽度"
                                           onchange="updateEquipmentParameter(${index}, 'width', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备高度(mm):</label>
                                    <input type="text" id="equipment-height-${index}" placeholder="请输入设备高度"
                                           onchange="updateEquipmentParameter(${index}, 'height', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备节拍(s):</label>
                                    <input type="text" id="equipment-cycle-time-${index}" placeholder="请输入设备节拍时间"
                                           onchange="updateEquipmentParameter(${index}, 'cycle_time', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">换型时间(min):</label>
                                    <input type="text" id="equipment-changeover-time-${index}" placeholder="请输入换型时间"
                                           onchange="updateEquipmentParameter(${index}, 'changeover_time', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备类型 -->
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.9rem;">设备类型:</label>
                    <input type="text"
                           value="${details.equipment_type || ''}"
                           onchange="updateEquipmentStationInfo(${index}, 'equipment_type', this.value)"
                           style="width: 100%; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; font-size: 0.9rem;"
                           placeholder="请输入设备类型">
                </div>

                <!-- Equipment部分 -->
                <div style="margin-bottom: 1.5rem; padding: 1rem; border: 1px solid #d9f7be; border-radius: 6px; background: #f6ffed;">
                    <h5 style="margin: 0 0 1rem 0; color: #52c41a; font-size: 1rem; font-weight: 600;">一、设备要求 (Equipment)</h5>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.85rem;">机械要求:</label>
                        <textarea onchange="updateEquipmentStationInfo(${index}, 'equipment_mechanical_requirements', this.value)"
                                  style="width: 100%; height: 80px; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.8rem; line-height: 1.4;"
                                  placeholder="请输入设备机械要求">${details.equipment_mechanical_requirements || ''}</textarea>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.85rem;">电气要求:</label>
                        <textarea onchange="updateEquipmentStationInfo(${index}, 'equipment_electrical_requirements', this.value)"
                                  style="width: 100%; height: 80px; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.8rem; line-height: 1.4;"
                                  placeholder="请输入设备电气要求">${details.equipment_electrical_requirements || ''}</textarea>
                    </div>

                    <div style="margin-bottom: 0;">
                        <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.85rem;">防错及点检要求:</label>
                        <textarea onchange="updateEquipmentStationInfo(${index}, 'equipment_error_prevention_requirements', this.value)"
                                  style="width: 100%; height: 80px; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.8rem; line-height: 1.4;"
                                  placeholder="请输入设备防错及点检要求">${details.equipment_error_prevention_requirements || ''}</textarea>
                    </div>
                </div>

                <!-- Fixture部分 -->
                <div style="margin-bottom: 0.5rem; padding: 1rem; border: 1px solid #ffd6cc; border-radius: 6px; background: #fff7e6;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <h5 style="margin: 0; color: #fa8c16; font-size: 1rem; font-weight: 600;">二、夹具要求 (Fixture)</h5>
                        <button onclick="addFixtureBlock(${index})"
                                style="background: #fa8c16; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 0.75rem;"
                                title="添加新夹具">
                            ➕ 添加夹具
                        </button>
                    </div>

                    <!-- 夹具列表容器 -->
                    <div id="fixtures-container-${index}">
                        ${this.generateFixtureBlocks(details, index)}
                    </div>
                </div>
                </div>
            </div>

            <!-- 设备工站后插入按钮（仅在最后一个工站显示） -->
            ${index === this.equipmentStations.length - 1 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertEquipmentStationAfter(${index})"
                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站后插入新设备工站">
                    ↓ 在此后插入设备工站
                </button>
            </div>
            ` : ''}
        `;
    }

    /**
     * 创建简化的Markdown格式设备工站HTML（与工艺页面保持一致的底层格式和逻辑）
     */
    createSimpleMarkdownStationHtml(station, index) {
        const details = station.equipment_details || {};
        const stationName = station.station_name || '设备信息';

        return `
            <!-- 设备工站前插入按钮 - 只在第一个工站前显示 -->
            ${index === 0 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertEquipmentStationBefore(${index})"
                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站前插入新设备工站">
                    ↑ 在此前插入设备工站
                </button>
            </div>
            ` : ''}

            <div class="equipment-station-block markdown-format" data-station-index="${index}" style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; background: #fafbfc;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem; padding-bottom: 0.5rem; border-bottom: 2px solid #52c41a;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <button onclick="toggleEquipmentStationCollapse(${index})"
                                style="background: none; border: none; color: #52c41a; cursor: pointer; font-size: 0.8rem; padding: 0; width: 16px; height: 16px; display: flex; align-items: center; justify-content: center;"
                                title="折叠/展开">
                            <span class="collapse-icon">▼</span>
                        </button>
                        <h4 style="margin: 0; color: #52c41a; font-size: 1.1rem; font-weight: 600; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                            ondblclick="makeEquipmentStationTitleEditable(${index}, this)"
                            onmouseover="this.style.backgroundColor='#f0f8f0'"
                            onmouseout="this.style.backgroundColor='transparent'"
                            title="双击编辑设备工站标题">
                            ST${station.station_number} - ${stationName}
                        </h4>
                    </div>
                    <div style="display: flex; gap: 0.3rem;">
                        <button onclick="insertEquipmentStationAfter(${index})"
                                style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;"
                                title="在此工站后插入新设备工站">
                            插入设备工站
                        </button>
                        <button onclick="deleteEquipmentStation(${index})"
                                style="background: #ff4d4f; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer; font-size: 0.75rem;">
                            删除
                        </button>
                    </div>
                </div>

                <div class="station-content" data-station-index="${index}" style="transition: all 0.3s ease;">

                <!-- 设备图片和参数上传区域 -->
                <div style="margin-bottom: 1rem;">
                    <h5 style="margin: 0 0 0.5rem 0; color: #52c41a; font-size: 0.9rem; font-weight: 600;">📷 设备图片和参数</h5>
                    <div style="display: flex; gap: 15px; min-height: 200px;">
                        <!-- 左侧图片上传区域 -->
                        <div style="flex: 2; background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0; display: flex; flex-direction: column;">
                            <h6 style="margin-bottom: 10px; font-size: 14px; color: #333;">设备图片</h6>
                            <div id="equipment-image-preview-${index}" style="flex: 1; overflow: hidden; text-align: center; margin-bottom: 10px; min-height: 120px; border: 2px dashed #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                                <div style="color: #999;">
                                    <div style="font-size: 32px; margin-bottom: 5px;">🖼️</div>
                                    <p style="margin: 0; font-size: 12px;">点击下方按钮上传设备图片</p>
                                    <small>支持 JPG, PNG 格式</small>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center; gap: 8px;">
                                <input type="file" id="equipment-image-input-${index}" accept="image/*" style="display: none;" onchange="handleEquipmentImageUpload(event, ${index})">
                                <button onclick="document.getElementById('equipment-image-input-${index}').click()" class="btn" style="background: #52c41a; color: white; font-size: 12px; padding: 4px 8px;">
                                    📁 上传图片
                                </button>
                                <button onclick="deleteEquipmentImage(${index})" class="btn" style="background: #ff7875; color: white; display: none; font-size: 12px; padding: 4px 8px;" id="delete-equipment-image-btn-${index}">
                                    🗑️ 删除图片
                                </button>
                            </div>
                        </div>

                        <!-- 右侧参数区域 -->
                        <div style="flex: 1; background: #fff; padding: 15px; border-radius: 6px; border: 1px solid #e0e0e0;">
                            <h6 style="margin-bottom: 10px; font-size: 14px; color: #333;">基本参数</h6>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备长度(mm):</label>
                                    <input type="text" id="equipment-length-${index}" placeholder="请输入设备长度"
                                           onchange="updateEquipmentParameter(${index}, 'length', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备宽度(mm):</label>
                                    <input type="text" id="equipment-width-${index}" placeholder="请输入设备宽度"
                                           onchange="updateEquipmentParameter(${index}, 'width', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备高度(mm):</label>
                                    <input type="text" id="equipment-height-${index}" placeholder="请输入设备高度"
                                           onchange="updateEquipmentParameter(${index}, 'height', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">设备节拍(s):</label>
                                    <input type="text" id="equipment-cycle-time-${index}" placeholder="请输入设备节拍时间"
                                           onchange="updateEquipmentParameter(${index}, 'cycle_time', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 3px; font-weight: 500; color: #333; font-size: 12px;">换型时间(min):</label>
                                    <input type="text" id="equipment-changeover-time-${index}" placeholder="请输入换型时间"
                                           onchange="updateEquipmentParameter(${index}, 'changeover_time', this.value)"
                                           style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 3px; font-size: 12px;">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 设备类型 -->
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.9rem;">设备类型:</label>
                    <input type="text"
                           value="${details.equipment_type || ''}"
                           onchange="updateEquipmentStationInfo(${index}, 'equipment_type', this.value)"
                           style="width: 100%; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; font-size: 0.9rem;"
                           placeholder="请输入设备类型">
                </div>

                <!-- 机械要求 -->
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.9rem;">机械要求:</label>
                    <textarea onchange="updateEquipmentStationInfo(${index}, 'mechanical_requirements', this.value)"
                              style="width: 100%; height: 80px; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.85rem; line-height: 1.4;"
                              placeholder="请输入机械要求">${details.mechanical_requirements || details.technical_requirements || ''}</textarea>
                </div>

                <!-- 电气要求 -->
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.9rem;">电气要求:</label>
                    <textarea onchange="updateEquipmentStationInfo(${index}, 'electrical_requirements', this.value)"
                              style="width: 100%; height: 80px; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.85rem; line-height: 1.4;"
                              placeholder="请输入电气要求">${details.electrical_requirements || details.equipment_parameters || ''}</textarea>
                </div>

                <!-- 防错及点检要求 -->
                <div style="margin-bottom: 0.5rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.9rem;">防错及点检要求:</label>
                    <textarea onchange="updateEquipmentStationInfo(${index}, 'error_prevention_requirements', this.value)"
                              style="width: 100%; height: 80px; padding: 0.4rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.85rem; line-height: 1.4;"
                              placeholder="请输入防错及点检要求">${details.error_prevention_requirements || details.safety_requirements || ''}</textarea>
                </div>
                </div>
            </div>

            <!-- 设备工站后插入按钮（仅在最后一个工站显示） -->
            ${index === this.equipmentStations.length - 1 ? `
            <div style="text-align: center; margin: 0.5rem 0;">
                <button onclick="insertEquipmentStationAfter(${index})"
                        style="background: #52c41a; color: white; border: none; border-radius: 3px; padding: 0.2rem 0.5rem; cursor: pointer; font-size: 0.7rem; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'"
                        onmouseout="this.style.opacity='0.7'"
                        title="在此工站后插入新设备工站">
                    ↓ 在此后插入设备工站
                </button>
            </div>
            ` : ''}
        `;
    }

    /**
     * 添加工艺工站事件监听器
     */
    attachProcessStationListeners() {
        // 这里可以添加额外的事件监听器
        console.log('工艺工站事件监听器已添加');
    }

    /**
     * 添加设备工站事件监听器
     */
    attachEquipmentStationListeners() {
        // 初始化所有设备工站的图片和参数数据
        this.equipmentStations.forEach((station, index) => {
            // 延迟初始化，确保DOM元素已经创建
            setTimeout(() => {
                if (typeof initializeEquipmentStationImageAndParameters === 'function') {
                    initializeEquipmentStationImageAndParameters(index);
                }
            }, 100);
        });

        console.log('设备工站事件监听器已添加');
    }

    /**
     * 获取当前所有工站数据
     * @returns {Object} 包含工艺和设备工站数据的对象
     */
    getAllStationsData() {
        return {
            processStations: this.processStations,
            equipmentStations: this.equipmentStations
        };
    }

    /**
     * 生成夹具块HTML
     * @param {Object} details - 设备详细信息
     * @param {number} stationIndex - 工站索引
     * @returns {string} 夹具块HTML字符串
     */
    generateFixtureBlocks(details, stationIndex) {
        // 检查是否有现有的夹具数据
        const fixtures = details.fixtures || [];

        // 如果没有夹具数据，创建一个默认夹具
        if (fixtures.length === 0) {
            fixtures.push({
                fixture_name: '夹具1',
                mechanical_requirements: details.fixture_mechanical_requirements || '请输入夹具机械要求',
                electrical_requirements: details.fixture_electrical_requirements || '请输入夹具电气要求',
                error_prevention_requirements: details.fixture_error_prevention_requirements || '请输入夹具防错及点检要求'
            });
        }

        return fixtures.map((fixture, fixtureIndex) =>
            this.createSingleFixtureBlockHtml(fixture, stationIndex, fixtureIndex)
        ).join('');
    }

    /**
     * 创建单个夹具块HTML
     * @param {Object} fixture - 夹具信息
     * @param {number} stationIndex - 工站索引
     * @param {number} fixtureIndex - 夹具索引
     * @returns {string} 单个夹具块HTML字符串
     */
    createSingleFixtureBlockHtml(fixture, stationIndex, fixtureIndex) {
        return `
            <div class="fixture-block" data-fixture-index="${fixtureIndex}" style="margin-bottom: 1rem; padding: 0.8rem; border: 1px solid #ffb366; border-radius: 4px; background: #fffbf5;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.8rem;">
                    <h6 style="margin: 0; color: #fa8c16; font-size: 0.9rem; font-weight: 600; cursor: pointer; padding: 2px 4px; border-radius: 3px; transition: background-color 0.2s;"
                        ondblclick="makeFixtureNameEditable(${stationIndex}, ${fixtureIndex}, this)"
                        onmouseover="this.style.backgroundColor='#fff2e6'"
                        onmouseout="this.style.backgroundColor='transparent'"
                        title="双击编辑夹具名称">
                        🔧 ${fixture.fixture_name || `夹具${fixtureIndex + 1}`}
                    </h6>
                    <div style="display: flex; gap: 0.3rem;">
                        <button onclick="addFixtureBlockAfter(${stationIndex}, ${fixtureIndex})"
                                style="background: #fa8c16; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;"
                                title="在此夹具后添加新夹具">
                            ➕ 添加
                        </button>
                        <button onclick="deleteFixtureBlock(${stationIndex}, ${fixtureIndex})"
                                style="background: #ff4d4f; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer; font-size: 0.7rem;"
                                title="删除此夹具">
                            🗑️ 删除
                        </button>
                    </div>
                </div>

                <!-- 夹具文件上传区域 -->
                <div style="margin-bottom: 1rem;">
                    <h6 style="margin: 0 0 0.5rem 0; color: #fa8c16; font-size: 0.85rem; font-weight: 600;">📎 夹具分解构图</h6>
                    <div style="background: #fff; padding: 12px; border-radius: 4px; border: 1px solid #e0e0e0;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                            <span style="font-size: 12px; color: #333; font-weight: 500;">夹具文件</span>
                            <div style="display: flex; gap: 6px;">
                                <input type="file" id="fixture-files-input-${stationIndex}-${fixtureIndex}" accept="image/*,.pdf,.doc,.docx" multiple style="display: none;" onchange="handleFixtureFilesUpload(event, ${stationIndex}, ${fixtureIndex})">
                                <button onclick="document.getElementById('fixture-files-input-${stationIndex}-${fixtureIndex}').click()" class="btn" style="background: #fa8c16; color: white; font-size: 11px; padding: 3px 6px;">
                                    📁 上传文件
                                </button>
                                <button onclick="clearAllFixtureFiles(${stationIndex}, ${fixtureIndex})" class="btn" style="background: #ff7875; color: white; font-size: 11px; padding: 3px 6px; display: none;" id="clear-fixture-files-btn-${stationIndex}-${fixtureIndex}">
                                    🗑️ 清空所有
                                </button>
                            </div>
                        </div>
                        <div id="fixture-files-preview-${stationIndex}-${fixtureIndex}" style="min-height: 80px; border: 2px dashed #ddd; border-radius: 4px; padding: 8px;">
                            <div style="text-align: center; color: #999; padding: 15px;">
                                <div style="font-size: 24px; margin-bottom: 3px;">📎</div>
                                <p style="margin: 0; font-size: 11px;">点击上方按钮上传夹具文件</p>
                                <small style="font-size: 10px;">支持 JPG, PNG, PDF, DOC, DOCX 格式</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 0.8rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.8rem;">机械要求:</label>
                    <textarea onchange="updateFixtureInfo(${stationIndex}, ${fixtureIndex}, 'mechanical_requirements', this.value)"
                              style="width: 100%; height: 60px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.75rem; line-height: 1.3;"
                              placeholder="请输入夹具机械要求">${fixture.mechanical_requirements || ''}</textarea>
                </div>

                <div style="margin-bottom: 0.8rem;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.8rem;">电气要求:</label>
                    <textarea onchange="updateFixtureInfo(${stationIndex}, ${fixtureIndex}, 'electrical_requirements', this.value)"
                              style="width: 100%; height: 60px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.75rem; line-height: 1.3;"
                              placeholder="请输入夹具电气要求">${fixture.electrical_requirements || ''}</textarea>
                </div>

                <div style="margin-bottom: 0;">
                    <label style="display: block; margin-bottom: 0.3rem; font-weight: 600; color: #333; font-size: 0.8rem;">防错及点检要求:</label>
                    <textarea onchange="updateFixtureInfo(${stationIndex}, ${fixtureIndex}, 'error_prevention_requirements', this.value)"
                              style="width: 100%; height: 60px; padding: 0.3rem; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-size: 0.75rem; line-height: 1.3;"
                              placeholder="请输入夹具防错及点检要求">${fixture.error_prevention_requirements || ''}</textarea>
                </div>
            </div>
        `;
    }
}

// 创建全局实例
const stationGenerator = new StationGenerator();

/**
 * 使工站标题可编辑
 * @param {number} stationIndex - 工站索引
 * @param {HTMLElement} titleElement - 标题元素
 */
function makeStationTitleEditable(stationIndex, titleElement) {
    // 获取当前标题文本
    const currentText = titleElement.textContent.trim();

    // 解析当前的工站号和工站名称
    const match = currentText.match(/^ST(\d+)\s*-\s*(.+)$/);
    if (!match) {
        console.error('无法解析工站标题格式');
        return;
    }

    const stationNumber = match[1];
    const stationName = match[2];

    // 创建输入框
    const input = document.createElement('input');
    input.type = 'text';
    input.value = `ST${stationNumber} - ${stationName}`;
    input.style.cssText = `
        margin: 0;
        color: #1a73e8;
        font-size: 1rem;
        font-weight: 600;
        background: white;
        border: 2px solid #1a73e8;
        border-radius: 3px;
        padding: 2px 4px;
        width: 300px;
        font-family: inherit;
    `;

    // 替换标题元素
    titleElement.style.display = 'none';
    titleElement.parentNode.insertBefore(input, titleElement);

    // 选中输入框内容
    input.focus();
    input.select();

    // 保存函数
    const saveTitle = () => {
        const newValue = input.value.trim();

        // 验证格式
        const newMatch = newValue.match(/^ST(\d+)\s*-\s*(.+)$/);
        if (!newMatch) {
            alert('请使用正确的格式：ST[数字] - [工站名称]');
            input.focus();
            return;
        }

        const newStationNumber = newMatch[1];
        const newStationName = newMatch[2];

        // 更新标题显示
        titleElement.textContent = `ST${newStationNumber} - ${newStationName}`;

        // 更新数据
        if (stationGenerator.processStations[stationIndex]) {
            stationGenerator.processStations[stationIndex].station_number = newStationNumber;
            stationGenerator.processStations[stationIndex].station_name = newStationName;
        }

        // 恢复标题显示
        input.remove();
        titleElement.style.display = '';

        console.log(`工站标题已更新: ST${newStationNumber} - ${newStationName}`);
    };

    // 添加事件监听器
    input.addEventListener('blur', saveTitle);
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            saveTitle();
        } else if (e.key === 'Escape') {
            // 取消编辑
            input.remove();
            titleElement.style.display = '';
        }
    });

    // 设置焦点并选中文本
    input.focus();
    input.select();
}

/**
 * 显示步骤插入菜单
 * @param {number} stationIndex - 工站索引
 * @param {number} stepIndex - 步骤索引
 * @param {HTMLElement} buttonElement - 按钮元素
 */
window.showStepInsertMenu = function(stationIndex, stepIndex, buttonElement) {
    console.log(`[DEBUG] showStepInsertMenu called: station=${stationIndex}, step=${stepIndex}`);

    // 隐藏所有其他菜单
    hideAllStepInsertMenus();

    const menu = buttonElement.parentElement.querySelector('.step-insert-dropdown');
    if (menu) {
        menu.style.display = 'block';
        console.log(`[DEBUG] Menu displayed for step ${stepIndex}`);

        // 添加延迟，确保菜单在鼠标移出按钮时不会立即消失
        buttonElement.parentElement.addEventListener('mouseleave', function(e) {
            setTimeout(() => {
                if (!buttonElement.parentElement.matches(':hover')) {
                    menu.style.display = 'none';
                    console.log(`[DEBUG] Menu hidden for step ${stepIndex}`);
                }
            }, 100);
        });
    } else {
        console.error(`[ERROR] Menu not found for step ${stepIndex}`);
    }
}

/**
 * 隐藏步骤插入菜单
 * @param {HTMLElement} buttonElement - 按钮元素
 */
window.hideStepInsertMenu = function(buttonElement) {
    console.log(`[DEBUG] hideStepInsertMenu called`);
    const menu = buttonElement.parentElement.querySelector('.step-insert-dropdown');
    if (menu) {
        setTimeout(() => {
            if (!buttonElement.parentElement.matches(':hover')) {
                menu.style.display = 'none';
                console.log(`[DEBUG] Menu hidden after timeout`);
            }
        }, 100);
    }
}

/**
 * 隐藏所有步骤插入菜单
 */
window.hideAllStepInsertMenus = function() {
    console.log(`[DEBUG] hideAllStepInsertMenus called`);
    const allMenus = document.querySelectorAll('.step-insert-dropdown');
    allMenus.forEach(menu => {
        menu.style.display = 'none';
    });
    console.log(`[DEBUG] Hidden ${allMenus.length} menus`);
}
