<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工站插入功能测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafbfc;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        .btn.primary {
            background: #1890ff;
            color: white;
        }
        .btn.primary:hover {
            background: #40a9ff;
        }
        .btn.success {
            background: #52c41a;
            color: white;
        }
        .btn.success:hover {
            background: #73d13d;
        }
        .btn.danger {
            background: #ff4d4f;
            color: white;
        }
        .btn.danger:hover {
            background: #ff7875;
        }
        .debug-info {
            background: #f6f8fa;
            border: 1px solid #d0d7de;
            border-radius: 6px;
            padding: 16px;
            margin-top: 20px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .status.warning {
            background: #fffbe6;
            border: 1px solid #ffe58f;
            color: #faad14;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 工站插入功能测试页面</h1>
        
        <div class="test-section">
            <h2>📋 测试控制面板</h2>
            <div class="test-buttons">
                <button class="btn primary" onclick="initializeTestData()">初始化测试数据</button>
                <button class="btn success" onclick="testInsertStation()">测试插入工站</button>
                <button class="btn success" onclick="testInsertStep()">测试插入步骤</button>
                <button class="btn danger" onclick="clearAllStations()">清空所有工站</button>
            </div>
            
            <div id="status-display">
                <div class="status warning">
                    ⚠️ 请先点击"初始化测试数据"按钮开始测试
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🏭 工艺工站列表</h2>
            <div id="stations-list" style="min-height: 100px; border: 1px dashed #ccc; padding: 20px; text-align: center; color: #999;">
                工站将在这里显示...
            </div>
            
            <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button class="btn primary" onclick="addNewProcessStation()">添加工艺工站</button>
                <button class="btn" style="background: #faad14; color: white;" onclick="toggleAllStations(true)">折叠所有</button>
                <button class="btn" style="background: #52c41a; color: white;" onclick="toggleAllStations(false)">展开所有</button>
                <button class="btn" style="background: #ff7875; color: white;" onclick="clearAllStations()">清空所有工站</button>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 调试信息</h2>
            <div id="debug-log" class="debug-info">
                调试信息将在这里显示...
            </div>
            <button class="btn" onclick="clearDebugLog()">清空日志</button>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>

    <script>
        // 调试日志功能
        function addDebugLog(message, type = 'info') {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            debugLog.textContent += logEntry;
            debugLog.scrollTop = debugLog.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[DEBUG] ${message}`);
        }

        function clearDebugLog() {
            document.getElementById('debug-log').textContent = '';
        }

        function updateStatus(message, type = 'success') {
            const statusDisplay = document.getElementById('status-display');
            const statusClass = type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'success';
            const icon = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : '✅';
            
            statusDisplay.innerHTML = `<div class="status ${statusClass}">${icon} ${message}</div>`;
        }

        // 初始化测试数据
        function initializeTestData() {
            addDebugLog('开始初始化测试数据...');
            
            try {
                // 创建测试工站数据
                const testStations = [
                    {
                        station_number: '10',
                        station_name: '测试工站A',
                        content: '这是测试工站A的内容',
                        process_steps: [
                            {
                                step_number: '1',
                                description: '测试步骤1描述',
                                operator: '人',
                                quality_requirements: '测试质量要求1',
                                error_prevention: '测试防错要求1'
                            },
                            {
                                step_number: '2',
                                description: '测试步骤2描述',
                                operator: '设备',
                                quality_requirements: '测试质量要求2',
                                error_prevention: '测试防错要求2'
                            }
                        ]
                    },
                    {
                        station_number: '15',
                        station_name: '测试工站B',
                        content: '这是测试工站B的内容',
                        process_steps: [
                            {
                                step_number: '1',
                                description: '测试步骤B1描述',
                                operator: '人',
                                quality_requirements: '测试质量要求B1',
                                error_prevention: '测试防错要求B1'
                            }
                        ]
                    }
                ];

                // 设置全局数据
                if (typeof processStationsData !== 'undefined') {
                    processStationsData = testStations;
                    addDebugLog('测试数据已设置到processStationsData');
                } else {
                    addDebugLog('processStationsData未定义，创建全局变量', 'warning');
                    window.processStationsData = testStations;
                }

                // 生成工站显示
                if (typeof stationGenerator !== 'undefined' && stationGenerator) {
                    stationGenerator.generateProcessStations(testStations);
                    addDebugLog('使用现有stationGenerator生成工站');
                } else if (typeof StationGenerator !== 'undefined') {
                    window.stationGenerator = new StationGenerator();
                    stationGenerator.generateProcessStations(testStations);
                    addDebugLog('创建新的stationGenerator并生成工站');
                } else {
                    addDebugLog('StationGenerator类未定义', 'error');
                    updateStatus('StationGenerator类未定义，请检查JS文件加载', 'error');
                    return;
                }

                updateStatus('测试数据初始化成功，已生成2个测试工站');
                addDebugLog('测试数据初始化完成');

            } catch (error) {
                addDebugLog(`初始化失败: ${error.message}`, 'error');
                updateStatus(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 测试插入工站
        function testInsertStation() {
            addDebugLog('测试插入工站功能...');
            
            try {
                if (typeof insertProcessStation === 'function') {
                    insertProcessStation(1); // 在位置1插入新工站
                    addDebugLog('调用insertProcessStation(1)成功');
                    updateStatus('工站插入测试成功');
                } else {
                    addDebugLog('insertProcessStation函数未定义', 'error');
                    updateStatus('insertProcessStation函数未定义', 'error');
                }
            } catch (error) {
                addDebugLog(`插入工站失败: ${error.message}`, 'error');
                updateStatus(`插入工站失败: ${error.message}`, 'error');
            }
        }

        // 测试插入步骤
        function testInsertStep() {
            addDebugLog('测试插入步骤功能...');
            
            try {
                if (typeof insertProcessStep === 'function') {
                    insertProcessStep(0, 1); // 在工站0的位置1插入新步骤
                    addDebugLog('调用insertProcessStep(0, 1)成功');
                    updateStatus('步骤插入测试成功');
                } else {
                    addDebugLog('insertProcessStep函数未定义', 'error');
                    updateStatus('insertProcessStep函数未定义', 'error');
                }
            } catch (error) {
                addDebugLog(`插入步骤失败: ${error.message}`, 'error');
                updateStatus(`插入步骤失败: ${error.message}`, 'error');
            }
        }

        // 清空所有工站
        function clearAllStations() {
            addDebugLog('清空所有工站...');
            
            try {
                if (typeof processStationsData !== 'undefined') {
                    processStationsData.length = 0;
                }
                
                const stationsList = document.getElementById('stations-list');
                if (stationsList) {
                    stationsList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">工站将在这里显示...</div>';
                }
                
                updateStatus('所有工站已清空');
                addDebugLog('清空工站完成');
            } catch (error) {
                addDebugLog(`清空工站失败: ${error.message}`, 'error');
                updateStatus(`清空工站失败: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugLog('页面DOM加载完成');
            addDebugLog(`StationGenerator类是否定义: ${typeof StationGenerator !== 'undefined'}`);
            addDebugLog(`stationGenerator实例是否存在: ${typeof stationGenerator !== 'undefined' && stationGenerator !== null}`);
            
            // 检查必要的函数是否存在
            const requiredFunctions = [
                'addNewProcessStation',
                'insertProcessStation', 
                'insertProcessStep',
                'ensureStationGenerator'
            ];
            
            requiredFunctions.forEach(funcName => {
                addDebugLog(`${funcName}函数是否存在: ${typeof window[funcName] === 'function'}`);
            });
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            addDebugLog(`全局错误: ${event.error.message}`, 'error');
            updateStatus(`发生错误: ${event.error.message}`, 'error');
        });
    </script>
</body>
</html>
