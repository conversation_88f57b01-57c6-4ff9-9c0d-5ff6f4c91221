{"format_specification": {"description": "AI生成内容的标准格式规范，支持新的Markdown格式和旧的【】格式", "structure": {"process_section": {"markers": ["### 工艺部分", "### 一、工艺部分", "## 工艺部分", "# 工艺部分", "【工艺部分】", "一、工艺部分", "工艺部分：", "工艺部分"], "end_markers": ["### 设备部分", "## 设备部分", "# 设备部分", "### 二、设备部分", "【工艺部分结束】", "二、设备部分", "设备部分：", "设备部分"], "format": "使用Markdown标题格式，每个工站包含工艺过程描述、人or设备、产品特性要求、过程防错要求"}, "equipment_section": {"markers": ["### 设备部分", "## 设备部分", "# 设备部分", "### 二、设备部分", "【设备部分】", "二、设备部分", "设备部分：", "设备部分"], "end_markers": ["【设备部分结束】", "### 三、", "## 三、", "# 三、", "### 总结", "### 结论"], "format": "使用Markdown标题格式，包含Equipment和Fixture子部分，每部分有机械要求、电气要求、防错及点检要求"}}, "station_patterns": {"process_station": "#### ST\\d+\\s+[^\\n]+", "equipment_station": "#### ST\\d+\\s+[^\\n]+", "step_pattern": "\\d+\\.\\s+工艺过程描述:", "operator_pattern": "\\s*-\\s*人or设备:", "quality_pattern": "\\s*-\\s*产品特性要求:", "error_prevention_pattern": "\\s*-\\s*过程防错要求:"}, "example_format": "### 一、工艺部分\n\n#### ST10 SAB-B-Pre-assembly\n（一）\n1. 工艺过程描述: ...\n   - 人or设备: ...\n   - 产品特性要求: ...\n   - 过程防错要求: ...\n\n### 二、设备部分\n\n#### ST10 SAB-B-Pre-assembly\n一、预装设备-Equipment\n（一）机械要求:\n1. ...\n（二）电气要求:\n1. ...\n（三）防错及点检要求:\n要求1：...\n方案1：..."}, "parsing_rules": {"process_keywords": ["工艺过程描述", "人or设备", "产品特性要求", "过程防错要求", "ST\\d+"], "equipment_keywords": ["设备信息", "设备类型", "技术要求", "设备参数", "ST\\d+设备", "Equipment", "Fixture", "机械要求", "电气要求", "防错及点检要求"]}}