<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤功能调试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.danger { background: #dc3545; color: white; }
        .btn.warning { background: #ffc107; color: black; }
        .btn:hover { opacity: 0.8; }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .check-result {
            font-weight: bold;
        }
        .check-result.pass { color: #28a745; }
        .check-result.fail { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 步骤功能调试页面</h1>
        
        <div class="debug-panel">
            <h2>📋 快速诊断</h2>
            <button class="btn primary" onclick="quickDiagnosis()">一键诊断</button>
            <button class="btn success" onclick="testDirectCall()">直接调用测试</button>
            <button class="btn success" onclick="testSimpleCall()">简单测试</button>
            <button class="btn warning" onclick="checkConsoleErrors()">检查控制台错误</button>
            <button class="btn" onclick="clearAllLogs()">清空日志</button>
            
            <div id="main-status" class="status info">
                ℹ️ 点击"一键诊断"开始检查
            </div>
        </div>

        <div class="debug-panel">
            <h2>🏭 工站显示区域</h2>
            <div id="stations-list" style="min-height: 200px; border: 2px dashed #ccc; padding: 20px; border-radius: 6px;">
                工站将在这里显示...
            </div>
        </div>

        <div class="debug-panel">
            <h2>📝 详细调试日志</h2>
            <div id="debug-log" class="log">
                调试日志将在这里显示...
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="/static/js/station_generator.js"></script>
    <script src="/static/js/station_manager.js"></script>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearAllLogs() {
            document.getElementById('debug-log').textContent = '';
        }

        function updateStatus(message, type = 'info') {
            const element = document.getElementById('main-status');
            const icons = { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️' };
            element.className = `status ${type}`;
            element.textContent = `${icons[type]} ${message}`;
        }

        function quickDiagnosis() {
            log('=== 开始一键诊断 ===');
            clearAllLogs();
            
            // 1. 检查JavaScript文件加载
            log('1. 检查JavaScript文件加载...');
            const checks = [
                ['StationGenerator类', typeof StationGenerator !== 'undefined'],
                ['insertProcessStep函数', typeof insertProcessStep === 'function'],
                ['deleteProcessStep函数', typeof deleteProcessStep === 'function'],
                ['addProcessStep函数', typeof addProcessStep === 'function'],
                ['showStepInsertMenu函数', typeof showStepInsertMenu === 'function'],
                ['regenerateProcessStation函数', typeof regenerateProcessStation === 'function']
            ];
            
            let allPassed = true;
            checks.forEach(([name, result]) => {
                log(`   ${name}: ${result ? '✅' : '❌'}`);
                if (!result) allPassed = false;
            });
            
            if (!allPassed) {
                log('❌ JavaScript文件加载有问题', 'error');
                updateStatus('JavaScript文件加载失败', 'error');
                return;
            }
            
            // 2. 初始化测试数据
            log('2. 初始化测试数据...');
            try {
                initializeTestData();
                log('   ✅ 测试数据初始化成功');
            } catch (error) {
                log(`   ❌ 测试数据初始化失败: ${error.message}`, 'error');
                updateStatus(`数据初始化失败: ${error.message}`, 'error');
                return;
            }
            
            // 3. 检查DOM元素
            log('3. 检查DOM元素...');
            const stationsList = document.getElementById('stations-list');
            if (!stationsList) {
                log('   ❌ stations-list元素不存在', 'error');
                updateStatus('DOM元素缺失', 'error');
                return;
            }
            
            const stationBlocks = stationsList.querySelectorAll('.station-block');
            const stepElements = stationsList.querySelectorAll('.process-step');
            const insertButtons = stationsList.querySelectorAll('.step-insert-btn');
            
            log(`   工站块数量: ${stationBlocks.length}`);
            log(`   步骤元素数量: ${stepElements.length}`);
            log(`   插入按钮数量: ${insertButtons.length}`);
            
            if (stationBlocks.length === 0) {
                log('   ❌ 没有找到工站块', 'error');
                updateStatus('工站未正确生成', 'error');
                return;
            }
            
            // 4. 测试功能
            log('4. 测试基本功能...');
            testDirectCall();
            
            log('=== 一键诊断完成 ===');
            updateStatus('诊断完成，请查看日志详情', 'success');
        }

        function initializeTestData() {
            log('初始化测试数据...');

            try {
                if (typeof StationGenerator === 'undefined') {
                    throw new Error('StationGenerator类未定义');
                }

                const testStation = {
                    station_number: '10',
                    station_name: '调试测试工站',
                    content: '用于调试的测试工站',
                    process_steps: [
                        {
                            step_number: '1',
                            description: '调试步骤1',
                            operator: '人',
                            quality_requirements: '调试质量要求1',
                            error_prevention: '调试防错要求1'
                        },
                        {
                            step_number: '2',
                            description: '调试步骤2',
                            operator: '设备',
                            quality_requirements: '调试质量要求2',
                            error_prevention: '调试防错要求2'
                        }
                    ]
                };

                log(`创建测试工站数据: ${JSON.stringify(testStation, null, 2)}`);

                // 设置全局数据
                window.processStationsData = [testStation];
                log(`设置window.processStationsData，长度: ${window.processStationsData.length}`);

                // 同时设置全局变量（如果存在）
                if (typeof processStationsData !== 'undefined') {
                    processStationsData.length = 0;
                    processStationsData.push(testStation);
                    log(`设置全局processStationsData，长度: ${processStationsData.length}`);
                }

                // 验证数据设置
                if (window.processStationsData && window.processStationsData[0] && window.processStationsData[0].process_steps) {
                    log(`✅ 数据设置成功，步骤数: ${window.processStationsData[0].process_steps.length}`);
                } else {
                    throw new Error('数据设置失败');
                }

                // 确保stationGenerator存在
                if (!window.stationGenerator) {
                    window.stationGenerator = new StationGenerator();
                    log('创建了新的stationGenerator实例');
                } else {
                    log('使用现有的stationGenerator实例');
                }

                // 生成HTML
                log('开始生成工站HTML...');
                window.stationGenerator.generateProcessStations([testStation]);
                log('工站HTML已生成');

                // 验证DOM
                const stationsList = document.getElementById('stations-list');
                if (stationsList) {
                    const stationBlocks = stationsList.querySelectorAll('.station-block');
                    log(`DOM中找到 ${stationBlocks.length} 个工站块`);
                }

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
                throw error;
            }
        }

        function testDirectCall() {
            log('=== 开始直接调用测试 ===');

            try {
                // 详细检查数据状态
                log('检查数据状态...');
                log(`window.processStationsData类型: ${typeof window.processStationsData}`);
                log(`window.processStationsData是否为数组: ${Array.isArray(window.processStationsData)}`);
                log(`window.processStationsData长度: ${window.processStationsData ? window.processStationsData.length : 'undefined'}`);

                log(`全局processStationsData类型: ${typeof processStationsData}`);
                log(`全局processStationsData长度: ${processStationsData ? processStationsData.length : 'undefined'}`);

                // 优先使用window.processStationsData
                let stationsData = window.processStationsData || processStationsData;

                if (stationsData && stationsData[0]) {
                    log(`第一个工站存在: ${JSON.stringify(stationsData[0], null, 2)}`);
                } else {
                    log('第一个工站不存在，尝试初始化...');
                    initializeTestData();

                    // 再次检查
                    stationsData = window.processStationsData || processStationsData;
                    log(`初始化后 window.processStationsData长度: ${window.processStationsData ? window.processStationsData.length : 'undefined'}`);
                    log(`初始化后 全局processStationsData长度: ${processStationsData ? processStationsData.length : 'undefined'}`);

                    if (stationsData && stationsData[0]) {
                        log(`初始化后第一个工站: ${JSON.stringify(stationsData[0], null, 2)}`);
                    }
                }

                // 确保数据存在
                if (!stationsData || !stationsData[0]) {
                    throw new Error('无法初始化工站数据');
                }

                // 同步数据到全局变量
                if (window.processStationsData && window.processStationsData.length > 0) {
                    window.processStationsData = stationsData;
                    log('已同步数据到window.processStationsData');
                }
                if (typeof processStationsData !== 'undefined') {
                    processStationsData.length = 0;
                    processStationsData.push(...stationsData);
                    log('已同步数据到全局processStationsData');
                }

                const station = stationsData[0];

                // 检查process_steps
                if (!station.process_steps) {
                    log('process_steps不存在，创建空数组');
                    station.process_steps = [];
                }

                const beforeCount = station.process_steps.length;
                log(`测试前步骤数: ${beforeCount}`);

                // 测试添加步骤
                log('测试添加步骤...');
                if (typeof insertProcessStep === 'function') {
                    log('调用insertProcessStep...');
                    insertProcessStep(0, beforeCount);

                    setTimeout(() => {
                        try {
                            const afterCount = station.process_steps.length;
                            log(`测试后步骤数: ${afterCount}`);

                            if (afterCount > beforeCount) {
                                log('✅ 添加步骤功能正常', 'success');

                                // 测试删除步骤
                                log('测试删除步骤...');
                                if (typeof deleteProcessStep === 'function') {
                                    // 模拟用户确认
                                    const originalConfirm = window.confirm;
                                    window.confirm = () => {
                                        log('模拟用户确认删除');
                                        return true;
                                    };

                                    log('调用deleteProcessStep...');
                                    deleteProcessStep(0, 0);

                                    setTimeout(() => {
                                        try {
                                            const finalCount = station.process_steps.length;
                                            log(`删除后步骤数: ${finalCount}`);

                                            if (finalCount < afterCount) {
                                                log('✅ 删除步骤功能正常', 'success');
                                                updateStatus('所有功能测试通过！', 'success');
                                            } else {
                                                log('❌ 删除步骤功能异常', 'error');
                                                updateStatus('删除功能有问题', 'error');
                                            }

                                            window.confirm = originalConfirm;
                                        } catch (deleteError) {
                                            log(`删除测试异常: ${deleteError.message}`, 'error');
                                            window.confirm = originalConfirm;
                                        }
                                    }, 200);
                                } else {
                                    log('❌ deleteProcessStep函数不存在', 'error');
                                }
                            } else {
                                log('❌ 添加步骤功能异常', 'error');
                                updateStatus('添加功能有问题', 'error');
                            }
                        } catch (addError) {
                            log(`添加测试异常: ${addError.message}`, 'error');
                        }
                    }, 200);
                } else {
                    log('❌ insertProcessStep函数不存在', 'error');
                    updateStatus('核心函数缺失', 'error');
                }

            } catch (error) {
                log(`直接调用测试失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
                updateStatus(`测试失败: ${error.message}`, 'error');
            }
        }

        function checkConsoleErrors() {
            log('=== 检查控制台错误 ===');
            log('请打开浏览器开发者工具(F12)，查看Console标签页');
            log('如果有红色错误信息，请将错误信息告诉我');
            log('常见错误类型:');
            log('1. 404错误 - JavaScript文件加载失败');
            log('2. ReferenceError - 函数或变量未定义');
            log('3. TypeError - 类型错误');
            log('4. SyntaxError - 语法错误');
            
            // 检查网络请求
            log('检查JavaScript文件加载状态...');
            const scripts = document.querySelectorAll('script[src]');
            scripts.forEach(script => {
                log(`脚本文件: ${script.src}`);
            });
            
            updateStatus('请检查浏览器控制台错误信息', 'warning');
        }

        // 页面加载完成后自动运行诊断
        document.addEventListener('DOMContentLoaded', function() {
            log('页面DOM加载完成');
            
            // 延迟运行诊断，确保所有脚本都加载完成
            setTimeout(() => {
                log('自动运行快速诊断...');
                quickDiagnosis();
            }, 1000);
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            log(`❌ 全局错误: ${event.error.message}`, 'error');
            log(`错误位置: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
            updateStatus(`发生错误: ${event.error.message}`, 'error');
        });

        // 监听资源加载错误
        window.addEventListener('error', function(event) {
            if (event.target.tagName === 'SCRIPT') {
                log(`❌ 脚本加载失败: ${event.target.src}`, 'error');
                updateStatus('JavaScript文件加载失败', 'error');
            }
        }, true);

        // 简单测试函数
        function testSimpleCall() {
            log('=== 开始简单测试 ===');

            try {
                // 创建简单的测试数据
                const testData = [{
                    station_number: '10',
                    station_name: '简单测试工站',
                    content: '简单测试',
                    process_steps: [
                        {
                            step_number: '1',
                            description: '简单步骤1',
                            operator: '人',
                            quality_requirements: '要求1',
                            error_prevention: '防错1'
                        }
                    ]
                }];

                log(`创建测试数据: ${JSON.stringify(testData[0])}`);

                // 直接设置到全局变量
                window.processStationsData = testData;

                // 验证设置
                log(`设置后数据长度: ${window.processStationsData.length}`);
                log(`步骤数: ${window.processStationsData[0].process_steps.length}`);

                // 测试添加步骤
                log('测试添加步骤...');
                const beforeCount = window.processStationsData[0].process_steps.length;
                log(`添加前步骤数: ${beforeCount}`);

                // 直接调用函数
                insertProcessStep(0, beforeCount);

                setTimeout(() => {
                    const afterCount = window.processStationsData[0].process_steps.length;
                    log(`添加后步骤数: ${afterCount}`);

                    if (afterCount > beforeCount) {
                        log('✅ 添加步骤成功', 'success');
                        updateStatus('简单测试通过！', 'success');

                        // 测试删除
                        log('测试删除步骤...');
                        const originalConfirm = window.confirm;
                        window.confirm = () => true;

                        deleteProcessStep(0, 0);

                        setTimeout(() => {
                            const finalCount = window.processStationsData[0].process_steps.length;
                            log(`删除后步骤数: ${finalCount}`);

                            if (finalCount < afterCount) {
                                log('✅ 删除步骤成功', 'success');
                                updateStatus('所有简单测试通过！', 'success');
                            } else {
                                log('❌ 删除步骤失败', 'error');
                            }

                            window.confirm = originalConfirm;
                        }, 100);

                    } else {
                        log('❌ 添加步骤失败', 'error');
                        updateStatus('添加步骤失败', 'error');
                    }
                }, 100);

            } catch (error) {
                log(`简单测试失败: ${error.message}`, 'error');
                log(`错误堆栈: ${error.stack}`, 'error');
                updateStatus(`简单测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
