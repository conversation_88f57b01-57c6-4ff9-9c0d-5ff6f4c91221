#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Markdown格式的内容解析器
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from utils.content_parser import content_parser

def test_markdown_format():
    """测试新的Markdown格式解析"""
    
    # 模拟智能匹配生成的Markdown格式内容
    markdown_content = """
### 一、工艺部分

#### ST10 SAB-B-Pre-assembly
（一）
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: 导流片无错装、漏装、安装方向正确
   - 过程防错要求: 通过MSA

2. 工艺过程描述: 将发生器放入夹具，设备自动夹紧发生器，自动扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器
   - 过程防错要求: 无夹伤，发生器正确，发生器插入方向正确，发生器螺柱方向正确

#### ST20 SAB-B-Folding
（一）
1. 工艺过程描述: 扫描发生器条码
   - 人or设备: 设备
   - 产品特性要求: 正确的发生器

2. 工艺过程描述: 扫描气袋条码
   - 人or设备: 设备
   - 产品特性要求: 正确的气袋

3. 工艺过程描述: 将气袋拉直，上夹具夹紧气袋
   - 人or设备: 人
   - 产品特性要求: 夹具光滑无毛刺，向上拉直需要左右上下可调

### 二、设备部分

#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯
2. ST10工站配置支架压装、插线束高度检测以及发生器预装3小分站，可以独立进行作业
3. 设备左右和前方需要3组安全光栅，硬件要接入安全回路进行控制

（二）电气要求:
1. 电控系统的电源供给为单一电源，相应保护接地、功能性接地、过电流保护、漏电保护等都需要满足
2. 3个小分站的气动需要分开控制，气压表需要设置在正对人侧方便点检使用

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

二、预装夹具-Fixture 
（一）机械要求:
1. 发生器检测到位选用大款传感器，检测整个端面
2. 治具夹紧发生器后不晃动、松动

（二）电气要求:
无

（三）防错及点检要求:
要求1：导流片安装方向正确、导流片无错装、漏装
方案1：导流片通过上方IV拍照识别导流片安装位置，安装方向

#### ST20 SAB-B-Folding
一、折叠设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建，电气箱内嵌在整个框架底部；设备底部使用福马轮；设备顶部需要安装照片灯
2. 设备前方需要2组安全光栅，硬件要接入安全回路进行控制
"""
    
    print("=" * 80)
    print("测试Markdown格式内容解析")
    print("=" * 80)
    
    # 1. 测试内容分割
    print("\n1. 测试内容分割:")
    parsed_content = content_parser.parse_content(markdown_content)
    
    print(f"工艺部分长度: {len(parsed_content.get('process_content', ''))}")
    print(f"设备部分长度: {len(parsed_content.get('equipment_content', ''))}")
    
    # 2. 测试工艺工站解析
    if parsed_content.get('process_content'):
        print("\n2. 测试工艺工站解析:")
        process_stations = content_parser.extract_station_info(parsed_content['process_content'])
        
        print(f"解析到 {len(process_stations)} 个工站")
        
        for station in process_stations:
            print(f"\n工站: ST{station['station_number']} - {station['station_name']}")
            print(f"步骤数量: {len(station.get('process_steps', []))}")
            
            for step in station.get('process_steps', []):
                print(f"  步骤 {step.get('step_number', '?')}:")
                print(f"    工艺过程描述: {step.get('description', '未识别')}")
                print(f"    人or设备: {step.get('operator', '未识别')}")
                print(f"    产品特性要求: {step.get('quality_requirements', '未识别')}")
                print(f"    过程防错要求: {step.get('error_prevention', '未识别')}")
    
    # 3. 测试设备工站解析
    if parsed_content.get('equipment_content'):
        print("\n3. 测试设备工站解析:")
        equipment_stations = content_parser.extract_equipment_station_info(parsed_content['equipment_content'])
        
        print(f"解析到 {len(equipment_stations)} 个设备工站")
        
        for station in equipment_stations:
            print(f"\n设备工站: ST{station['station_number']}")
            details = station.get('equipment_details', {})
            print(f"  设备类型: {details.get('equipment_type', '未识别')}")
            print(f"  技术要求: {details.get('technical_requirements', '未识别')}")

def test_mixed_format():
    """测试混合格式内容"""
    
    print("\n" + "=" * 80)
    print("测试混合格式内容解析")
    print("=" * 80)
    
    # 混合了新旧格式的内容
    mixed_content = """
【工艺部分】
ST10 SAB-B-Pre-assembly
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
人or设备: 人
产品特性要求: 导流片无错装、漏装、安装方向正确
过程防错要求: 通过MSA
【工艺部分结束】

### 二、设备部分

#### ST10 SAB-B-Pre-assembly
一、预装设备-Equipment 
（一）机械要求:
1. 设备整体框架使用铝型材搭建
"""
    
    parsed_content = content_parser.parse_content(mixed_content)
    
    print(f"工艺部分长度: {len(parsed_content.get('process_content', ''))}")
    print(f"设备部分长度: {len(parsed_content.get('equipment_content', ''))}")
    
    if parsed_content.get('process_content'):
        process_stations = content_parser.extract_station_info(parsed_content['process_content'])
        print(f"工艺工站数量: {len(process_stations)}")
        
        for station in process_stations:
            print(f"工站: ST{station['station_number']} - {station['station_name']}")
            print(f"步骤数量: {len(station.get('process_steps', []))}")

def test_format_comparison():
    """对比新旧格式的解析效果"""
    
    print("\n" + "=" * 80)
    print("对比新旧格式解析效果")
    print("=" * 80)
    
    # 旧格式
    old_format = """
【工艺部分】
ST10 SAB-B-Pre-assembly
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
人or设备: 人
产品特性要求: 导流片无错装、漏装、安装方向正确
过程防错要求: 通过MSA
【工艺部分结束】
"""
    
    # 新格式
    new_format = """
### 一、工艺部分

#### ST10 SAB-B-Pre-assembly
（一）
1. 工艺过程描述: 拿取导流片，将导流片安装到发生器上
   - 人or设备: 人
   - 产品特性要求: 导流片无错装、漏装、安装方向正确
   - 过程防错要求: 通过MSA
"""
    
    print("旧格式解析结果:")
    old_parsed = content_parser.parse_content(old_format)
    if old_parsed.get('process_content'):
        old_stations = content_parser.extract_station_info(old_parsed['process_content'])
        for station in old_stations:
            for step in station.get('process_steps', []):
                print(f"  描述: {step.get('description', '未识别')}")
                print(f"  操作者: {step.get('operator', '未识别')}")
    
    print("\n新格式解析结果:")
    new_parsed = content_parser.parse_content(new_format)
    if new_parsed.get('process_content'):
        new_stations = content_parser.extract_station_info(new_parsed['process_content'])
        for station in new_stations:
            for step in station.get('process_steps', []):
                print(f"  描述: {step.get('description', '未识别')}")
                print(f"  操作者: {step.get('operator', '未识别')}")

if __name__ == "__main__":
    test_markdown_format()
    test_mixed_format()
    test_format_comparison()
    
    print("\n" + "=" * 80)
    print("测试完成")
    print("=" * 80)
