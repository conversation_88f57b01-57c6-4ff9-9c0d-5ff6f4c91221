<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工站问题调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-title {
            color: #1a73e8;
            margin-bottom: 15px;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
        }
        .debug-button {
            background: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-button:hover {
            background: #1557b0;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            color: #d32f2f;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #388e3c;
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #f57c00;
            background: #fff3e0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2 class="debug-title">工站问题调试工具</h2>
        <p>此工具用于调试工站相关的三个问题。</p>
        
        <h3>问题1: 工站插入按钮过多</h3>
        <button class="debug-button" onclick="checkInsertButtons()">检查插入按钮</button>
        <button class="debug-button" onclick="fixInsertButtons()">修复插入按钮</button>
        
        <h3>问题2: 设备页对已识别内容的工站无法进行删除操作</h3>
        <button class="debug-button" onclick="checkEquipmentDeleteButtons()">检查设备删除按钮</button>
        <button class="debug-button" onclick="testEquipmentDelete()">测试设备删除功能</button>
        
        <h3>问题3: 工艺页面对已识别内容不能进行新增步骤的操作</h3>
        <button class="debug-button" onclick="checkProcessStepButtons()">检查步骤新增按钮</button>
        <button class="debug-button" onclick="checkStationData()">检查工站数据结构</button>
        <button class="debug-button" onclick="testStepInsertion()">测试步骤插入功能</button>
        
        <h3>综合测试</h3>
        <button class="debug-button" onclick="runAllTests()">运行所有测试</button>
        <button class="debug-button" onclick="fixAllIssues()">修复所有问题</button>
        
        <div id="debug-output"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }

        function checkInsertButtons() {
            log('检查工站插入按钮...');
            
            // 检查"↑在此前插入工站"按钮
            const beforeButtons = document.querySelectorAll('button[onclick*="insertProcessStationBefore"]');
            log(`找到 ${beforeButtons.length} 个"↑在此前插入工站"按钮`);
            
            // 检查"↓在此后插入工站"按钮
            const afterButtons = document.querySelectorAll('button[onclick*="insertProcessStationAfter"]');
            log(`找到 ${afterButtons.length} 个"↓在此后插入工站"按钮`);
            
            // 检查可见的"↓在此后插入工站"按钮
            let visibleAfterButtons = 0;
            afterButtons.forEach((button, index) => {
                const container = button.parentElement;
                const isVisible = container && container.style.display !== 'none';
                if (isVisible) {
                    visibleAfterButtons++;
                    log(`  "↓在此后插入工站"按钮 ${index + 1}: 可见`, 'info');
                } else {
                    log(`  "↓在此后插入工站"按钮 ${index + 1}: 隐藏`, 'info');
                }
            });
            
            if (visibleAfterButtons === 1) {
                log('✅ "↓在此后插入工站"按钮显示正确（只有1个可见）', 'success');
            } else {
                log(`❌ "↓在此后插入工站"按钮显示错误（${visibleAfterButtons}个可见，应该只有1个）`, 'error');
            }
            
            // 检查工站数量
            const stationBlocks = document.querySelectorAll('.station-block');
            log(`找到 ${stationBlocks.length} 个工站块`);
            
            if (beforeButtons.length === stationBlocks.length) {
                log('✅ "↑在此前插入工站"按钮数量正确', 'success');
            } else {
                log(`❌ "↑在此前插入工站"按钮数量错误（${beforeButtons.length}个按钮，${stationBlocks.length}个工站）`, 'error');
            }
        }

        function fixInsertButtons() {
            log('修复工站插入按钮...');
            
            // 隐藏所有"↓在此后插入工站"按钮
            const afterButtons = document.querySelectorAll('button[onclick*="insertProcessStationAfter"]');
            afterButtons.forEach(button => {
                const container = button.parentElement;
                if (container) {
                    container.style.display = 'none';
                }
            });
            
            // 只显示最后一个"↓在此后插入工站"按钮
            if (afterButtons.length > 0) {
                const lastButton = afterButtons[afterButtons.length - 1];
                const lastContainer = lastButton.parentElement;
                if (lastContainer) {
                    lastContainer.style.display = 'block';
                }
            }
            
            log(`修复完成，隐藏了 ${afterButtons.length - 1} 个多余的"↓在此后插入工站"按钮`, 'success');
            
            // 重新检查
            setTimeout(() => {
                checkInsertButtons();
            }, 100);
        }

        function checkEquipmentDeleteButtons() {
            log('检查设备删除按钮...');
            
            const deleteButtons = document.querySelectorAll('button[onclick*="deleteEquipmentStation"]');
            log(`找到 ${deleteButtons.length} 个设备删除按钮`);
            
            deleteButtons.forEach((button, index) => {
                const onclick = button.getAttribute('onclick');
                log(`  删除按钮 ${index + 1}: ${onclick}`, 'info');
                
                // 检查函数是否存在
                if (typeof window.deleteEquipmentStation === 'function') {
                    log(`    ✅ deleteEquipmentStation函数存在`, 'success');
                } else {
                    log(`    ❌ deleteEquipmentStation函数不存在`, 'error');
                }
            });
            
            // 检查设备工站数据
            if (typeof window.equipmentStationsData !== 'undefined') {
                log(`设备工站数据存在，包含 ${window.equipmentStationsData.length} 个工站`, 'success');
            } else {
                log('❌ 设备工站数据不存在', 'error');
            }
        }

        function testEquipmentDelete() {
            log('测试设备删除功能...');
            
            if (typeof window.deleteEquipmentStation !== 'function') {
                log('❌ deleteEquipmentStation函数不存在，无法测试', 'error');
                return;
            }
            
            const equipmentStations = document.querySelectorAll('.equipment-station-block');
            if (equipmentStations.length === 0) {
                log('❌ 未找到设备工站，无法测试', 'error');
                return;
            }
            
            log(`找到 ${equipmentStations.length} 个设备工站，测试删除功能...`, 'info');
            log('注意：这只是测试函数调用，不会实际删除工站', 'warning');
            
            try {
                // 模拟删除第一个工站（但不实际执行）
                log('模拟调用 deleteEquipmentStation(0)...', 'info');
                // window.deleteEquipmentStation(0); // 注释掉实际调用
                log('✅ 函数调用成功（模拟）', 'success');
            } catch (error) {
                log(`❌ 函数调用失败: ${error.message}`, 'error');
            }
        }

        function checkProcessStepButtons() {
            log('检查工艺步骤新增按钮...');
            
            const insertButtons = document.querySelectorAll('button[onclick*="insertProcessStep"]');
            const addButtons = document.querySelectorAll('button[onclick*="addProcessStep"]');
            
            log(`找到 ${insertButtons.length} 个步骤插入按钮`);
            log(`找到 ${addButtons.length} 个步骤添加按钮`);
            
            // 检查函数是否存在
            if (typeof window.insertProcessStep === 'function') {
                log('✅ insertProcessStep函数存在', 'success');
            } else {
                log('❌ insertProcessStep函数不存在', 'error');
            }
            
            if (typeof window.addProcessStep === 'function') {
                log('✅ addProcessStep函数存在', 'success');
            } else {
                log('❌ addProcessStep函数不存在', 'error');
            }
        }

        function checkStationData() {
            log('检查工站数据结构...');
            
            // 检查工艺工站数据
            if (typeof window.processStationsData !== 'undefined') {
                log(`工艺工站数据存在，包含 ${window.processStationsData.length} 个工站`, 'success');
                
                window.processStationsData.forEach((station, index) => {
                    log(`  工站 ${index}: ST${station.station_number} - ${station.station_name}`, 'info');
                    if (station.process_steps) {
                        log(`    包含 ${station.process_steps.length} 个步骤`, 'info');
                    } else {
                        log(`    ❌ 缺少process_steps数组`, 'warning');
                    }
                });
            } else {
                log('❌ 工艺工站数据不存在', 'error');
            }
            
            // 检查全局processStationsData
            if (typeof processStationsData !== 'undefined') {
                log(`全局processStationsData存在，包含 ${processStationsData.length} 个工站`, 'success');
            } else {
                log('❌ 全局processStationsData不存在', 'error');
            }
        }

        function testStepInsertion() {
            log('测试步骤插入功能...');
            
            if (typeof window.insertProcessStep !== 'function') {
                log('❌ insertProcessStep函数不存在，无法测试', 'error');
                return;
            }
            
            const stationsData = window.processStationsData || processStationsData;
            if (!stationsData || stationsData.length === 0) {
                log('❌ 工站数据不存在，无法测试', 'error');
                return;
            }
            
            log(`找到 ${stationsData.length} 个工站，测试步骤插入功能...`, 'info');
            log('注意：这只是测试函数调用，不会实际插入步骤', 'warning');
            
            try {
                // 模拟在第一个工站插入步骤（但不实际执行）
                log('模拟调用 insertProcessStep(0, 0)...', 'info');
                // window.insertProcessStep(0, 0); // 注释掉实际调用
                log('✅ 函数调用成功（模拟）', 'success');
            } catch (error) {
                log(`❌ 函数调用失败: ${error.message}`, 'error');
            }
        }

        function runAllTests() {
            log('=== 开始运行所有测试 ===');
            
            checkInsertButtons();
            setTimeout(() => {
                checkEquipmentDeleteButtons();
                setTimeout(() => {
                    checkProcessStepButtons();
                    setTimeout(() => {
                        checkStationData();
                        setTimeout(() => {
                            testStepInsertion();
                            log('=== 所有测试完成 ===');
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        function fixAllIssues() {
            log('=== 开始修复所有问题 ===');
            
            fixInsertButtons();
            
            // 强制暴露必要的函数
            if (typeof deleteEquipmentStation === 'function' && typeof window.deleteEquipmentStation !== 'function') {
                window.deleteEquipmentStation = deleteEquipmentStation;
                log('✅ 修复了deleteEquipmentStation函数暴露', 'success');
            }
            
            if (typeof insertProcessStep === 'function' && typeof window.insertProcessStep !== 'function') {
                window.insertProcessStep = insertProcessStep;
                log('✅ 修复了insertProcessStep函数暴露', 'success');
            }
            
            if (typeof addProcessStep === 'function' && typeof window.addProcessStep !== 'function') {
                window.addProcessStep = addProcessStep;
                log('✅ 修复了addProcessStep函数暴露', 'success');
            }
            
            log('=== 所有问题修复完成 ===', 'success');
            
            // 重新运行测试
            setTimeout(() => {
                runAllTests();
            }, 1000);
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('页面加载完成，开始自动测试...', 'info');
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
