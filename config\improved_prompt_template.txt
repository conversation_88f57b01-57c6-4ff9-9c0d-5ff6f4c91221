请按照以下标准格式生成Linespec内容，确保与智能匹配系统生成的格式完全一致：

注意：请严格按照格式要求，确保每个字段都能被系统正确识别。

### 一、工艺部分

#### ST10 [工站名称]
（一）
1. 工艺过程描述: [详细描述工艺过程]
   - 人or设备: [人/设备]
   - 产品特性要求: [产品质量要求]
   - 过程防错要求: [防错措施]

2. 工艺过程描述: [如有多个步骤，继续描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [产品质量要求]
   - 过程防错要求: [防错措施]

#### ST15 [下一个工站名称]
（一）
1. 工艺过程描述: [详细描述工艺过程]
   - 人or设备: [人/设备]
   - 产品特性要求: [产品质量要求]
   - 过程防错要求: [防错措施]

### 二、设备部分

#### ST10 [工站名称]
一、[设备名称]-Equipment
（一）机械要求:
1. [机械设备要求]
2. [设备配置要求]

（二）电气要求:
1. [电气系统要求]
2. [控制系统要求]

（三）防错及点检要求:
要求1：[防错要求描述]
方案1：[防错解决方案]

二、[夹具名称]-Fixture
（一）机械要求:
1. [夹具机械要求]

（二）电气要求:
[电气相关要求]

（三）防错及点检要求:
要求1：[防错要求描述]
方案1：[防错解决方案]

#### ST15 [下一个工站名称]
[按相同格式继续...]

重要说明：
1. 必须严格按照上述格式输出，使用Markdown标题格式（### 一、工艺部分，#### ST10等）
2. 工站号必须使用ST+数字格式（如ST10、ST15、ST20）
3. 工艺部分每个步骤使用缩进格式：
   - 人or设备: [值]
   - 产品特性要求: [值]
   - 过程防错要求: [值]
4. 设备部分必须包含Equipment和Fixture两个子部分
5. 设备部分使用中文编号（一、二、三）和中文小标题（（一）机械要求:）
6. "人or设备"字段必须明确填写"人"、"设备"或"人+设备"
7. 根据提供的零件信息，自动匹配对应的工艺类型
8. 工艺部分和设备部分的工站号要对应
9. 防错要求使用"要求X：...方案X：..."的格式

现在请根据以下信息生成Linespec：
产品类型：{product_type}
零件清单：{parts_list}
项目要求：{project_requirements}
