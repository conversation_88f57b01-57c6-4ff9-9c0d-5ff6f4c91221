/* 工站样式文件 */

/* 工站容器样式 */
.station-block {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background: #fafbfc;
    position: relative;
    transition: all 0.3s ease;
}

.station-block:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #1a73e8;
}

/* 紧凑布局样式 */
.station-block.compact {
    padding: 0.8rem;
    margin-bottom: 1rem;
    border-radius: 6px;
}

.station-block.compact:hover {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
}

.process-steps-container.compact {
    margin-top: 0.6rem;
}

.process-step.compact {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
    border: 1px solid #e8e8e8;
}

.equipment-station-block.compact {
    padding: 0.8rem;
    margin-bottom: 1rem;
    border-radius: 6px;
}

/* 折叠功能样式 */
.collapse-icon {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
    line-height: 1;
}

.station-content {
    transition: all 0.3s ease;
    overflow: hidden;
}

.station-block.collapsed {
    margin-bottom: 0.5rem;
}

.station-block.collapsed .station-content {
    display: none;
}

.station-block.collapsed .collapse-icon {
    transform: rotate(-90deg);
}

/* 批量操作按钮样式 */
.batch-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.batch-actions .btn {
    font-size: 0.85rem;
    padding: 0.3rem 0.6rem;
}

@media (max-width: 768px) {
    .batch-actions {
        flex-direction: column;
    }

    .batch-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 工站头部样式 */
.station-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.station-title {
    margin: 0;
    color: #1a73e8;
    font-size: 1.1rem;
    font-weight: 600;
}

/* 工站基本信息样式 */
.station-info {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.station-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.station-info-label {
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

.station-info-input {
    padding: 0.4rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.station-info-input:focus {
    outline: none;
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* 工艺步骤容器样式 */
.process-steps-container {
    margin-top: 1rem;
}

.process-steps-title {
    margin-bottom: 0.8rem;
    color: #333;
    font-size: 1rem;
    font-weight: 500;
}

/* 工艺步骤样式 */
.process-step {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 0.8rem;
    margin-bottom: 0.8rem;
    background: white;
    transition: all 0.3s ease;
}

.process-step:hover {
    border-color: #1a73e8;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.process-step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.process-step-title {
    margin: 0;
    color: #1a73e8;
    font-size: 0.95rem;
    font-weight: 500;
}

.process-step-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

@media (max-width: 768px) {
    .process-step-grid {
        grid-template-columns: 1fr;
    }
}

/* 表单字段样式 */
.form-field {
    display: flex;
    flex-direction: column;
}

.form-label {
    display: block;
    margin-bottom: 0.3rem;
    font-weight: 500;
    font-size: 0.9rem;
    color: #333;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.4rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-textarea {
    min-height: 60px;
    resize: vertical;
    font-family: inherit;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* 紧凑表单样式 */
.compact .form-label {
    margin-bottom: 0.2rem;
    font-size: 0.8rem;
    color: #555;
}

.compact .form-input,
.compact .form-textarea,
.compact .form-select {
    padding: 0.3rem;
    font-size: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 3px;
}

.compact .form-textarea {
    min-height: 40px;
    resize: none;
    line-height: 1.3;
}

.compact .form-input:focus,
.compact .form-textarea:focus,
.compact .form-select:focus {
    border-color: #1a73e8;
    box-shadow: 0 0 0 1px rgba(26, 115, 232, 0.2);
}

/* 设备工站样式 */
.equipment-station-block {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    background: #fafbfc;
    transition: all 0.3s ease;
}

.equipment-station-block:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #52c41a;
}

.equipment-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

@media (max-width: 768px) {
    .equipment-grid {
        grid-template-columns: 1fr;
    }
}

/* 按钮样式 */
.btn {
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn.primary {
    background: #1a73e8;
    color: white;
}

.btn.primary:hover {
    background: #1557b0;
}

.btn.success {
    background: #52c41a;
    color: white;
}

.btn.success:hover {
    background: #389e0d;
}

.btn.danger {
    background: #ff4d4f;
    color: white;
}

.btn.danger:hover {
    background: #d9363e;
}

.btn.warning {
    background: #ff7875;
    color: white;
}

.btn.warning:hover {
    background: #ff5252;
}

.btn-small {
    padding: 2px 6px;
    font-size: 0.7rem;
}

.btn-large {
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
}

/* 操作按钮组样式 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* 提示信息样式 */
.info-tip {
    padding: 0.8rem;
    background: #f0f8ff;
    border-radius: 4px;
    border-left: 4px solid #1a73e8;
    margin-bottom: 1rem;
}

.info-tip p {
    margin: 0;
    font-size: 0.9rem;
    color: #333;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #999;
    font-size: 0.9rem;
}

.empty-state-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

/* 加载状态样式 */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    max-width: 300px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.notification-success {
    background-color: #52c41a;
}

.notification-error {
    background-color: #ff4d4f;
}

.notification-warning {
    background-color: #faad14;
}

.notification-info {
    background-color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .station-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .station-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.3rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    /* 移动端紧凑布局 */
    .process-step-grid {
        grid-template-columns: 1fr !important;
        gap: 0.4rem;
    }

    .equipment-grid {
        grid-template-columns: 1fr !important;
        gap: 0.4rem;
    }

    .station-block.compact {
        padding: 0.6rem;
        margin-bottom: 0.8rem;
    }

    .process-step.compact {
        padding: 0.4rem;
        margin-bottom: 0.4rem;
    }

    .compact .form-input,
    .compact .form-textarea,
    .compact .form-select {
        padding: 0.25rem;
        font-size: 0.75rem;
    }

    .compact .form-textarea {
        min-height: 35px;
    }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
    .station-block.compact {
        padding: 0.5rem;
        margin-bottom: 0.6rem;
    }

    .process-step.compact {
        padding: 0.3rem;
    }

    .compact .form-label {
        font-size: 0.75rem;
        margin-bottom: 0.1rem;
    }

    .compact .form-input,
    .compact .form-textarea,
    .compact .form-select {
        padding: 0.2rem;
        font-size: 0.7rem;
    }

    .compact .form-textarea {
        min-height: 30px;
    }
}
