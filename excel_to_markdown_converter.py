#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据转换为Markdown知识库脚本
将Linespec-data-SAB-V0.xlsx转换为结构化Markdown文档
"""

import pandas as pd
import os
from pathlib import Path

class SABDataConverter:
    def __init__(self, excel_file_path):
        self.excel_file_path = excel_file_path
        self.output_dir = "SAB知识库"
        self.sab_categories = ['A', 'B', 'C', 'D', 'E', 'F']
        
    def create_output_directory(self):
        """创建输出目录"""
        Path(self.output_dir).mkdir(exist_ok=True)
        print(f"创建输出目录: {self.output_dir}")
    
    def read_excel_data(self):
        """读取Excel文件数据"""
        try:
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(self.excel_file_path)
            print(f"Excel文件包含的工作表: {excel_file.sheet_names}")
            
            self.sheets_data = {}
            for sheet_name in excel_file.sheet_names:
                self.sheets_data[sheet_name] = pd.read_excel(excel_file, sheet_name=sheet_name)
                print(f"读取工作表 '{sheet_name}': {len(self.sheets_data[sheet_name])} 行数据")
            
            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def extract_sab_category_data(self, category):
        """提取特定SAB类别的数据"""
        category_data = {
            'parts': [],
            'process_stations': [],
            'equipment_requirements': [],
            'fixture_requirements': []
        }

        # 查找包含该类别数据的工作表
        for sheet_name, data in self.sheets_data.items():
            if f'SAB-{category.upper()}' in sheet_name.upper():
                print(f"处理 SAB-{category} 类别数据，工作表: {sheet_name}")

                # 主要工艺数据表（如 SAB-A, SAB-B 等）
                if sheet_name == f'SAB-{category.upper()}':
                    category_data['process_stations'] = self.extract_process_data_from_main_sheet(data)
                    category_data['equipment_requirements'] = self.extract_equipment_data_from_main_sheet(data)
                    category_data['fixture_requirements'] = self.extract_fixture_data_from_main_sheet(data)

                # 设备夹具清单表
                elif '设备夹具清单' in sheet_name:
                    equipment_fixture_data = self.extract_equipment_fixture_list(data)
                    category_data['equipment_requirements'].extend(equipment_fixture_data.get('equipment', []))
                    category_data['fixture_requirements'].extend(equipment_fixture_data.get('fixture', []))

        # 从Sheet1提取零件映射信息
        if 'Sheet1' in self.sheets_data:
            category_data['parts'] = self.extract_parts_from_mapping(self.sheets_data['Sheet1'], category)

        return category_data
    
    def extract_parts_from_mapping(self, data, category):
        """从Sheet1映射表提取零件数据"""
        parts = []
        try:
            if not data.empty:
                # 查找SAB类别行
                for index, row in data.iterrows():
                    if str(row.iloc[0]).strip().upper() == category.upper():
                        # 提取该行的所有非空零件
                        for col_idx in range(1, len(row)):
                            part_value = str(row.iloc[col_idx]).strip()
                            if part_value and part_value != 'nan' and part_value != 'NaN' and part_value != '':
                                # 获取列名作为零件类型
                                col_name = data.columns[col_idx]
                                if col_name and str(col_name) != 'nan':
                                    parts.append(f"{col_name}: {part_value}")
                        break
        except Exception as e:
            print(f"提取零件映射数据时出错: {e}")

        return parts
    
    def extract_process_data_from_main_sheet(self, data):
        """从主工作表提取工艺数据"""
        stations = []
        try:
            if not data.empty:
                current_station = None

                for index, row in data.iterrows():
                    # 检查station列
                    station_value = str(row.get('station', '')).strip()
                    if station_value and station_value != 'nan' and 'ST' in station_value:
                        current_station = station_value

                    # 提取工艺过程描述
                    process_desc = str(row.get('process description', '')).strip()
                    man_machine = str(row.get('man/machine', '')).strip()
                    product_req = str(row.get('产品特性要求', '')).strip()
                    error_req = str(row.get('防错要求', '')).strip()

                    if current_station and process_desc and process_desc != 'nan':
                        # 查找是否已有该工站
                        station_found = False
                        for station in stations:
                            if station['station_number'] == current_station:
                                station['processes'].append({
                                    'description': process_desc,
                                    'man_machine': man_machine,
                                    'product_requirements': product_req,
                                    'error_requirements': error_req
                                })
                                station_found = True
                                break

                        if not station_found:
                            stations.append({
                                'station_number': current_station,
                                'station_name': current_station,
                                'processes': [{
                                    'description': process_desc,
                                    'man_machine': man_machine,
                                    'product_requirements': product_req,
                                    'error_requirements': error_req
                                }]
                            })
        except Exception as e:
            print(f"提取工艺数据时出错: {e}")

        return stations
    
    def extract_equipment_data_from_main_sheet(self, data):
        """从主工作表提取设备数据"""
        equipment = []
        try:
            if not data.empty:
                current_station = None

                for index, row in data.iterrows():
                    # 检查station列
                    station_value = str(row.get('station', '')).strip()
                    if station_value and station_value != 'nan' and 'ST' in station_value:
                        current_station = station_value

                    # 提取设备信息
                    equipment_name = str(row.get('设备名', '')).strip()
                    mechanical_req = str(row.get('设备机械要求', '')).strip()
                    electrical_req = str(row.get('设备电气要求', '')).strip()
                    error_req = str(row.get('设备防错&点检要求', '')).strip()

                    if current_station and equipment_name and equipment_name != 'nan':
                        equipment.append({
                            'station': current_station,
                            'equipment_name': equipment_name,
                            'mechanical_requirements': mechanical_req if mechanical_req != 'nan' else '',
                            'electrical_requirements': electrical_req if electrical_req != 'nan' else '',
                            'error_prevention_requirements': error_req if error_req != 'nan' else ''
                        })
        except Exception as e:
            print(f"提取设备数据时出错: {e}")

        return equipment
    
    def extract_fixture_data_from_main_sheet(self, data):
        """从主工作表提取夹具数据"""
        fixtures = []
        try:
            if not data.empty:
                current_station = None

                for index, row in data.iterrows():
                    # 检查station列
                    station_value = str(row.get('station', '')).strip()
                    if station_value and station_value != 'nan' and 'ST' in station_value:
                        current_station = station_value

                    # 提取夹具信息
                    fixture_name = str(row.get('夹具名', '')).strip()
                    mechanical_req = str(row.get('夹具机械要求', '')).strip()
                    electrical_req = str(row.get('夹具电气要求', '')).strip()
                    error_req = str(row.get('夹具防错&点检要求', '')).strip()

                    if current_station and fixture_name and fixture_name != 'nan':
                        fixtures.append({
                            'station': current_station,
                            'fixture_name': fixture_name,
                            'mechanical_requirements': mechanical_req if mechanical_req != 'nan' else '',
                            'electrical_requirements': electrical_req if electrical_req != 'nan' else '',
                            'error_prevention_requirements': error_req if error_req != 'nan' else ''
                        })
        except Exception as e:
            print(f"提取夹具数据时出错: {e}")

        return fixtures

    def extract_equipment_fixture_list(self, data):
        """从设备夹具清单表提取数据"""
        result = {'equipment': [], 'fixture': []}
        try:
            if not data.empty:
                for index, row in data.iterrows():
                    item_type = str(row.get('类型', '')).strip().lower()
                    item_name = str(row.get('名称', '')).strip()
                    station = str(row.get('工站', '')).strip()

                    if item_name and item_name != 'nan':
                        item_info = {
                            'station': station if station != 'nan' else '',
                            'name': item_name,
                            'mechanical_requirements': '',
                            'electrical_requirements': '',
                            'error_prevention_requirements': ''
                        }

                        if '设备' in item_type or 'equipment' in item_type:
                            result['equipment'].append(item_info)
                        elif '夹具' in item_type or 'fixture' in item_type:
                            result['fixture'].append(item_info)
        except Exception as e:
            print(f"提取设备夹具清单时出错: {e}")

        return result
    
    def generate_markdown_content(self, category, category_data):
        """生成Markdown内容"""
        content = f"""# SAB-{category}类知识库

## 概述
SAB-{category}类产品的工艺和设备要求规范。

## 零件清单
"""
        
        # 添加零件清单
        if category_data['parts']:
            for part in category_data['parts']:
                content += f"- {part}\n"
        else:
            content += "- [待补充零件信息]\n"
        
        content += "\n## 标准工艺流程\n\n### 工艺部分\n\n"

        # 添加工艺流程
        if category_data['process_stations']:
            for station in category_data['process_stations']:
                content += f"#### {station['station_number']} {station['station_name']}\n"
                content += "（一）\n"

                for i, process in enumerate(station['processes'], 1):
                    content += f"{i}. 工艺过程描述: {process['description']}\n"
                    content += f"   - 人or设备: {process['man_machine']}\n"
                    if process['product_requirements']:
                        content += f"   - 产品特性要求: {process['product_requirements']}\n"
                    if process['error_requirements']:
                        content += f"   - 过程防错要求: {process['error_requirements']}\n"
                    content += "\n"
        else:
            # 使用默认模板
            content += f"""#### ST10 SAB-{category}-工站1
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

#### ST15 SAB-{category}-工站2
（一）
1. 工艺过程描述: [具体描述]
   - 人or设备: [人/设备]
   - 产品特性要求: [具体要求]
   - 过程防错要求: [具体要求]

"""
        
        content += "### 设备部分\n\n"

        # 添加设备要求
        if category_data['equipment_requirements'] or category_data['fixture_requirements']:
            # 按工站组织设备和夹具信息
            stations_dict = {}

            # 收集设备信息
            for equipment in category_data['equipment_requirements']:
                station = equipment['station']
                if station not in stations_dict:
                    stations_dict[station] = {'equipment': [], 'fixture': []}
                stations_dict[station]['equipment'].append(equipment)

            # 收集夹具信息
            for fixture in category_data['fixture_requirements']:
                station = fixture['station']
                if station not in stations_dict:
                    stations_dict[station] = {'equipment': [], 'fixture': []}
                stations_dict[station]['fixture'].append(fixture)

            # 生成每个工站的设备部分
            for station, station_data in stations_dict.items():
                content += f"#### {station} SAB-{category}-{station}\n"

                # Equipment部分
                if station_data['equipment']:
                    for equipment in station_data['equipment']:
                        content += f"一、{equipment.get('equipment_name', '设备名称')}-Equipment \n"
                        content += "（一）机械要求:\n"
                        if equipment['mechanical_requirements']:
                            content += f"1. {equipment['mechanical_requirements']}\n"
                        else:
                            content += "1. [具体要求]\n"

                        content += "\n（二）电气要求:\n"
                        if equipment['electrical_requirements']:
                            content += f"1. {equipment['electrical_requirements']}\n"
                        else:
                            content += "1. [具体要求]\n"

                        content += "\n（三）防错及点检要求:\n"
                        if equipment['error_prevention_requirements']:
                            content += f"要求1：{equipment['error_prevention_requirements']}\n"
                            content += "方案1：[具体方案]\n"
                        else:
                            content += "要求1：[具体要求]\n方案1：[具体方案]\n"
                        content += "\n"

                # Fixture部分
                if station_data['fixture']:
                    for fixture in station_data['fixture']:
                        content += f"二、{fixture.get('fixture_name', '夹具名称')}-Fixture \n"
                        content += "（一）机械要求:\n"
                        if fixture['mechanical_requirements']:
                            content += f"1. {fixture['mechanical_requirements']}\n"
                        else:
                            content += "1. [具体要求]\n"

                        content += "\n（二）电气要求:\n"
                        if fixture['electrical_requirements']:
                            content += f"1. {fixture['electrical_requirements']}\n"
                        else:
                            content += "1. [具体要求]\n"

                        content += "\n（三）防错及点检要求:\n"
                        if fixture['error_prevention_requirements']:
                            content += f"要求1：{fixture['error_prevention_requirements']}\n"
                            content += "方案1：[具体方案]\n"
                        else:
                            content += "要求1：[具体要求]\n方案1：[具体方案]\n"
                        content += "\n"
        else:
            # 使用默认模板
            content += f"""#### ST10 SAB-{category}-工站1
一、设备名称-Equipment
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

#### ST15 SAB-{category}-工站2
一、设备名称-Equipment
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

二、夹具名称-Fixture
（一）机械要求:
1. [具体要求]

（二）电气要求:
1. [具体要求]

（三）防错及点检要求:
要求1：[具体要求]
方案1：[具体方案]

"""
        
        return content
    
    def save_markdown_file(self, category, content):
        """保存Markdown文件"""
        filename = f"SAB-{category}类.md"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已生成: {filepath}")
            return True
        except Exception as e:
            print(f"保存文件失败 {filepath}: {e}")
            return False
    
    def convert_all_categories(self):
        """转换所有SAB类别"""
        success_count = 0
        
        for category in self.sab_categories:
            print(f"\n处理 SAB-{category} 类别...")
            
            # 提取数据
            category_data = self.extract_sab_category_data(category)
            
            # 生成Markdown内容
            content = self.generate_markdown_content(category, category_data)
            
            # 保存文件
            if self.save_markdown_file(category, content):
                success_count += 1
        
        print(f"\n转换完成！成功生成 {success_count}/{len(self.sab_categories)} 个文件")
        return success_count == len(self.sab_categories)
    
    def run(self):
        """运行转换流程"""
        print("开始Excel到Markdown转换...")
        
        # 创建输出目录
        self.create_output_directory()
        
        # 读取Excel数据
        if not self.read_excel_data():
            return False
        
        # 转换所有类别
        return self.convert_all_categories()

def main():
    """主函数"""
    excel_file = "Linespec-data-SAB-V0.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误: Excel文件 '{excel_file}' 不存在")
        print("请确保文件在当前目录中")
        return
    
    converter = SABDataConverter(excel_file)
    
    if converter.run():
        print("\n✅ 转换成功完成！")
        print(f"📁 输出目录: {converter.output_dir}")
        print("📄 生成的文件:")
        for category in converter.sab_categories:
            print(f"   - SAB-{category}类.md")
    else:
        print("\n❌ 转换过程中出现错误")

if __name__ == "__main__":
    main()
