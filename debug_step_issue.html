<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤功能调试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.danger { background: #dc3545; color: white; }
        .btn.warning { background: #ffc107; color: black; }
        .btn:hover { opacity: 0.8; }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .check-result {
            font-weight: bold;
        }
        .check-result.pass { color: #28a745; }
        .check-result.fail { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 步骤功能调试页面</h1>
        
        <div class="debug-panel">
            <h2>📋 系统检查</h2>
            <button class="btn primary" onclick="runSystemCheck()">运行系统检查</button>
            <button class="btn warning" onclick="runDeepDiagnosis()">深度诊断</button>
            <button class="btn" onclick="clearAllLogs()">清空所有日志</button>
            
            <div id="system-status" class="status info">
                ℹ️ 点击"运行系统检查"开始诊断
            </div>
            
            <div id="check-results" style="margin-top: 15px;">
                <!-- 检查结果将在这里显示 -->
            </div>
        </div>

        <div class="debug-panel">
            <h2>🧪 功能测试</h2>
            <button class="btn success" onclick="testBasicFunctions()">测试基础函数</button>
            <button class="btn success" onclick="testDataStructure()">测试数据结构</button>
            <button class="btn success" onclick="testDOMElements()">测试DOM元素</button>
            <button class="btn danger" onclick="forceAddStep()">强制添加步骤</button>
            <button class="btn danger" onclick="forceDeleteStep()">强制删除步骤</button>
            
            <div id="test-status" class="status info">
                ℹ️ 选择测试项目
            </div>
        </div>

        <div class="debug-panel">
            <h2>🏭 工站显示区域</h2>
            <div id="stations-list" style="min-height: 200px; border: 2px dashed #ccc; padding: 20px; border-radius: 6px;">
                工站将在这里显示...
            </div>
        </div>

        <div class="debug-panel">
            <h2>📝 详细调试日志</h2>
            <div id="debug-log" class="log">
                调试日志将在这里显示...
            </div>
        </div>
    </div>

    <!-- 引入JavaScript文件 -->
    <script src="static/js/station_generator.js"></script>
    <script src="static/js/station_manager.js"></script>

    <script>
        // 全局调试变量
        let debugInfo = {
            jsFilesLoaded: false,
            functionsAvailable: false,
            dataInitialized: false,
            domReady: false
        };

        function log(message, type = 'info') {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function clearAllLogs() {
            document.getElementById('debug-log').textContent = '';
            document.getElementById('check-results').innerHTML = '';
        }

        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const icons = { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️' };
            element.className = `status ${type}`;
            element.textContent = `${icons[type]} ${message}`;
        }

        function addCheckResult(name, result, details = '') {
            const container = document.getElementById('check-results');
            const checkItem = document.createElement('div');
            checkItem.className = 'check-item';
            
            const resultClass = result ? 'pass' : 'fail';
            const resultText = result ? '✅ 通过' : '❌ 失败';
            
            checkItem.innerHTML = `
                <span>${name}</span>
                <span class="check-result ${resultClass}">${resultText}</span>
            `;
            
            if (details) {
                checkItem.title = details;
            }
            
            container.appendChild(checkItem);
        }

        function runSystemCheck() {
            log('开始运行系统检查...');
            document.getElementById('check-results').innerHTML = '';
            
            // 1. 检查JavaScript文件加载
            log('检查JavaScript文件加载...');
            const stationGeneratorLoaded = typeof StationGenerator !== 'undefined';
            const stationManagerLoaded = typeof insertProcessStep === 'function';
            
            addCheckResult('StationGenerator类', stationGeneratorLoaded);
            addCheckResult('station_manager.js函数', stationManagerLoaded);
            
            // 2. 检查关键函数
            log('检查关键函数...');
            const functions = [
                'insertProcessStep',
                'insertProcessStepBefore',
                'insertProcessStepAfter',
                'deleteProcessStep',
                'addProcessStep',
                'regenerateProcessStation',
                'showStepInsertMenu',
                'hideStepInsertMenu'
            ];
            
            let functionsOk = true;
            functions.forEach(funcName => {
                const exists = typeof window[funcName] === 'function';
                addCheckResult(`函数 ${funcName}`, exists);
                if (!exists) functionsOk = false;
                log(`${funcName}: ${exists ? '存在' : '不存在'}`);
            });
            
            // 3. 检查全局变量
            log('检查全局变量...');
            const processDataExists = typeof processStationsData !== 'undefined';
            const stationGenExists = typeof stationGenerator !== 'undefined';
            
            addCheckResult('processStationsData', processDataExists);
            addCheckResult('stationGenerator实例', stationGenExists);
            
            // 4. 检查DOM元素
            log('检查DOM元素...');
            const stationsListExists = document.getElementById('stations-list') !== null;
            addCheckResult('stations-list元素', stationsListExists);
            
            // 5. 尝试初始化测试数据
            log('尝试初始化测试数据...');
            try {
                initializeTestData();
                addCheckResult('测试数据初始化', true);
                updateStatus('system-status', '系统检查完成，可以进行功能测试', 'success');
            } catch (error) {
                addCheckResult('测试数据初始化', false, error.message);
                updateStatus('system-status', `系统检查发现问题: ${error.message}`, 'error');
                log(`初始化失败: ${error.message}`, 'error');
            }
        }

        function initializeTestData() {
            log('初始化测试数据...');
            
            if (typeof StationGenerator === 'undefined') {
                throw new Error('StationGenerator类未定义');
            }

            const testStation = {
                station_number: '10',
                station_name: '调试测试工站',
                content: '用于调试的测试工站',
                process_steps: [
                    {
                        step_number: '1',
                        description: '调试步骤1',
                        operator: '人',
                        quality_requirements: '调试质量要求1',
                        error_prevention: '调试防错要求1'
                    },
                    {
                        step_number: '2',
                        description: '调试步骤2',
                        operator: '设备',
                        quality_requirements: '调试质量要求2',
                        error_prevention: '调试防错要求2'
                    }
                ]
            };

            window.processStationsData = [testStation];
            
            if (!window.stationGenerator) {
                window.stationGenerator = new StationGenerator();
                log('创建了新的stationGenerator实例');
            }
            
            stationGenerator.generateProcessStations([testStation]);
            log('测试数据初始化完成');
        }

        function testBasicFunctions() {
            log('测试基础函数...');
            
            try {
                // 测试函数存在性
                const functions = ['insertProcessStep', 'deleteProcessStep', 'addProcessStep'];
                functions.forEach(funcName => {
                    if (typeof window[funcName] !== 'function') {
                        throw new Error(`函数 ${funcName} 不存在`);
                    }
                    log(`✅ ${funcName} 函数存在`);
                });
                
                updateStatus('test-status', '基础函数测试通过', 'success');
            } catch (error) {
                log(`基础函数测试失败: ${error.message}`, 'error');
                updateStatus('test-status', `基础函数测试失败: ${error.message}`, 'error');
            }
        }

        function testDataStructure() {
            log('测试数据结构...');
            
            try {
                if (typeof processStationsData === 'undefined') {
                    throw new Error('processStationsData未定义');
                }
                
                if (!Array.isArray(processStationsData)) {
                    throw new Error('processStationsData不是数组');
                }
                
                if (processStationsData.length === 0) {
                    throw new Error('processStationsData为空');
                }
                
                const station = processStationsData[0];
                if (!station.process_steps) {
                    throw new Error('工站没有process_steps');
                }
                
                log(`✅ 数据结构正常，工站数: ${processStationsData.length}, 步骤数: ${station.process_steps.length}`);
                updateStatus('test-status', '数据结构测试通过', 'success');
                
            } catch (error) {
                log(`数据结构测试失败: ${error.message}`, 'error');
                updateStatus('test-status', `数据结构测试失败: ${error.message}`, 'error');
            }
        }

        function testDOMElements() {
            log('测试DOM元素...');
            
            try {
                const stationsList = document.getElementById('stations-list');
                if (!stationsList) {
                    throw new Error('stations-list元素不存在');
                }
                
                const stationBlocks = stationsList.querySelectorAll('.station-block');
                log(`✅ 找到 ${stationBlocks.length} 个工站块`);
                
                const stepElements = stationsList.querySelectorAll('.process-step');
                log(`✅ 找到 ${stepElements.length} 个步骤元素`);
                
                const insertButtons = stationsList.querySelectorAll('.step-insert-btn');
                log(`✅ 找到 ${insertButtons.length} 个插入按钮`);
                
                const deleteButtons = stationsList.querySelectorAll('button[onclick*="deleteProcessStep"]');
                log(`✅ 找到 ${deleteButtons.length} 个删除按钮`);
                
                updateStatus('test-status', 'DOM元素测试通过', 'success');
                
            } catch (error) {
                log(`DOM元素测试失败: ${error.message}`, 'error');
                updateStatus('test-status', `DOM元素测试失败: ${error.message}`, 'error');
            }
        }

        function forceAddStep() {
            log('强制添加步骤...');
            
            try {
                if (typeof insertProcessStep !== 'function') {
                    throw new Error('insertProcessStep函数不存在');
                }
                
                if (!processStationsData || !processStationsData[0]) {
                    throw new Error('工站数据不存在');
                }
                
                const beforeCount = processStationsData[0].process_steps.length;
                log(`添加前步骤数: ${beforeCount}`);
                
                // 直接调用函数
                insertProcessStep(0, beforeCount);
                
                setTimeout(() => {
                    const afterCount = processStationsData[0].process_steps.length;
                    log(`添加后步骤数: ${afterCount}`);
                    
                    if (afterCount > beforeCount) {
                        log('✅ 强制添加步骤成功', 'success');
                        updateStatus('test-status', '强制添加步骤成功', 'success');
                    } else {
                        log('❌ 强制添加步骤失败', 'error');
                        updateStatus('test-status', '强制添加步骤失败', 'error');
                    }
                }, 200);
                
            } catch (error) {
                log(`强制添加步骤失败: ${error.message}`, 'error');
                updateStatus('test-status', `强制添加步骤失败: ${error.message}`, 'error');
            }
        }

        function forceDeleteStep() {
            log('强制删除步骤...');
            
            try {
                if (typeof deleteProcessStep !== 'function') {
                    throw new Error('deleteProcessStep函数不存在');
                }
                
                if (!processStationsData || !processStationsData[0] || !processStationsData[0].process_steps.length) {
                    throw new Error('没有步骤可删除');
                }
                
                const beforeCount = processStationsData[0].process_steps.length;
                log(`删除前步骤数: ${beforeCount}`);
                
                // 模拟用户确认
                const originalConfirm = window.confirm;
                window.confirm = () => {
                    log('模拟用户确认删除');
                    return true;
                };
                
                // 直接调用函数
                deleteProcessStep(0, 0);
                
                setTimeout(() => {
                    const afterCount = processStationsData[0].process_steps.length;
                    log(`删除后步骤数: ${afterCount}`);
                    
                    if (afterCount < beforeCount) {
                        log('✅ 强制删除步骤成功', 'success');
                        updateStatus('test-status', '强制删除步骤成功', 'success');
                    } else {
                        log('❌ 强制删除步骤失败', 'error');
                        updateStatus('test-status', '强制删除步骤失败', 'error');
                    }
                    
                    // 恢复原始confirm
                    window.confirm = originalConfirm;
                }, 200);
                
            } catch (error) {
                log(`强制删除步骤失败: ${error.message}`, 'error');
                updateStatus('test-status', `强制删除步骤失败: ${error.message}`, 'error');
            }
        }

        function runDeepDiagnosis() {
            log('开始深度诊断...');
            
            // 检查所有可能的问题
            log('=== 深度诊断开始 ===');
            
            // 1. 检查错误处理函数
            log('检查错误处理函数...');
            if (typeof ensureStationGenerator === 'function') {
                const result = ensureStationGenerator();
                log(`ensureStationGenerator结果: ${result}`);
            } else {
                log('ensureStationGenerator函数不存在', 'error');
            }
            
            // 2. 检查事件监听
            log('检查事件监听...');
            document.addEventListener('click', function(event) {
                if (event.target.tagName === 'BUTTON') {
                    log(`点击了按钮: ${event.target.textContent} (${event.target.onclick})`);
                }
            }, { once: true });
            
            // 3. 检查CSS样式
            log('检查CSS样式...');
            const buttons = document.querySelectorAll('button');
            log(`页面上共有 ${buttons.length} 个按钮`);
            
            // 4. 检查控制台错误
            log('请检查浏览器控制台是否有JavaScript错误');
            
            log('=== 深度诊断完成 ===');
            updateStatus('system-status', '深度诊断完成，请查看日志', 'info');
        }

        // 页面加载完成后的检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面DOM加载完成');
            debugInfo.domReady = true;
            
            // 自动运行基础检查
            setTimeout(() => {
                log('自动运行系统检查...');
                runSystemCheck();
            }, 1000);
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            log(`全局错误: ${event.error.message}`, 'error');
            log(`错误位置: ${event.filename}:${event.lineno}:${event.colno}`, 'error');
            updateStatus('system-status', `发生错误: ${event.error.message}`, 'error');
        });

        // 监听所有点击事件
        document.addEventListener('click', function(event) {
            if (event.target.tagName === 'BUTTON' && event.target.onclick) {
                log(`点击按钮: ${event.target.textContent}`);
            }
        });
    </script>
</body>
</html>
